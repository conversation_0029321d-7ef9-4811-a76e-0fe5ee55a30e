<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->
<%
' 定义黑名单数组
Dim blacklist
blacklist = Array("16181925804593972", "manager7977") ' 你需要将需要忽略的用户 ID 添加到这个数组中

Set userMap = Server.CreateObject("Scripting.Dictionary")

' 查询用户表获取用户名称
sql2 = "SELECT * FROM user2011"
rs.open sql2,conn,1,2

' 创建用户 ID 到用户名的映射
Do While Not rs.EOF
    if not userMap.Exists(rs("dingding_id").value) then
        ' response.write rs("mingzhi").value & " - " & rs("是否锁定").value & "<br>"
        if rs("是否锁定").value = "True" then
            Dim originalLength
            originalLength = UBound(blacklist)
            ReDim Preserve blacklist(originalLength + 1)

            blacklist(originalLength + 1) = rs("dingding_id").value
        else
            userMap.Add rs("dingding_id").value, rs("mingzhi").value
        end if
    end if
    rs.MoveNext
Loop
rs.Close


' 查询SQL sqlserver
sql = "SELECT creator_userid, " & _
      "SUM(CASE WHEN DATEPART(YEAR, gmt_create) = DATEPART(YEAR, DATEADD(WEEK, -1, GETDATE())) AND " & _
      "          DATEPART(WEEK, gmt_create) = DATEPART(WEEK, DATEADD(WEEK, -1, GETDATE())) THEN 1 ELSE 0 END) AS LastWeekCount, " & _
      "SUM(CASE WHEN MONTH(gmt_create) = MONTH(GETDATE()) THEN 1 ELSE 0 END) AS ThisMonthCount " & _
      " FROM saler_work_record " & _
      "GROUP BY creator_userid " & _
      "ORDER BY ThisMonthCount DESC" ' 按照本月数据从高到低排序

' 查询SQL access
'sql = "SELECT creator_userid, " & _
'      "SUM(IIF(Format(gmt_create, 'ww', 2) = Format(Date(), 'ww', 2), 1, 0)) AS LastWeekCount, " & _
'      "SUM(IIF(Month(gmt_create) = Month(Date()), 1, 0)) AS ThisMonthCount, " & _
'      "SUM(IIF(Month(gmt_create) = Month(Date())-1, 1, 0)) AS LastMonthCount, " & _
'      "SUM(IIF(Format(gmt_create, 'q', 2) = Format(Date(), 'q', 2), 1, 0)) AS ThisQuarterCount, " & _
'      "SUM(IIF(Format(gmt_create, 'q', 2) = Format(DateAdd('m', -3, Date()), 'q', 2), 1, 0)) AS LastQuarterCount, " & _
'      "SUM(IIF(Year(gmt_create) = Year(Date()), 1, 0)) AS ThisYearCount, " & _
'      "SUM(IIF(Year(gmt_create) = Year(Date())-1, 1, 0)) AS LastYearCount " & _
'      "FROM saler_work_record " & _
'      "GROUP BY creator_userid"

LogMessage "workRecord query local db, sql -> " & sql
rs.open sql,conn,1,2


Dim bodyStr
Dim username
bodyStr = "钉钉客户管理-客户跟进记录\n\n"
Do While Not rs.EOF 
    If Not IsInArray(rs("creator_userid").Value, blacklist) Then
        bodyStr = bodyStr & userMap(rs("creator_userid").value) & " / 上周:" & rs("LastWeekCount") & " 本月:" & rs("ThisMonthCount") &"\n"
    End If
    rs.MoveNext
Loop
rs.close
Set rs = Nothing

conn.close
Set conn = Nothing

LogMessage "跟进记录发送内容： -> " & bodyStr
'response.end



' 定义请求参数  
Dim apiUrl
Dim accessToken
Dim keyword
apiUrl = "https://oapi.dingtalk.com/robot/send"
' 销售群： bc1b8c894f06da26b4bb7d39d4c8427f61bade1896cc4772cbf4c1f1cccd6f8f
' 关键词： SOP
' 测试群： 6d08809f8b67386024c62db3741bd1f602f0af50cf1194449b5ad8a6130c5b25
' 关键词： TEST

accessToken = Request.QueryString("token")
keyword = Request.QueryString("keyword")
if accessToken = "" Then
    accessToken = "bc1b8c894f06da26b4bb7d39d4c8427f61bade1896cc4772cbf4c1f1cccd6f8f"
end if
if keyword = "" Then
    keyword = "SOP"
end if
LogMessage "发送token,keyword： -> " & accessToken & " / " & keyword


' 构造要发送的消息内容
Dim jsonData
Dim messageContent 

messageContent = bodyStr & "\n------------\n" & keyword & "(系统标记,请忽略)" ' 请替换成您要发送的实际消息内容
jsonData = "{""msgtype"": ""text"", ""text"": {""content"": """ & messageContent & """}}"

'response.write jsonData
'Response.end

' 发送POST请求
Dim xmlhttp
Set xmlhttp = Server.CreateObject("MSXML2.ServerXMLHTTP")
xmlhttp.open "POST", apiUrl & "?access_token=" & accessToken, False
xmlhttp.setRequestHeader "Content-Type", "application/json"
xmlhttp.send jsonData

' 获取响应内容
Dim responseContent
responseContent = xmlhttp.responseText

' 检查响应状态
If xmlhttp.Status = 200 Then
    Response.Write "success"
    Response.Write "<br/>"
    Response.Write "response:" & responseContent
    LogMessage "销售群 钉钉发送 success -> " & responseContent
Else
    LogMessage "销售群 钉钉发送 error -> " & xmlhttp.Status
    Response.Write "error" & xmlhttp.Status
End If

Set xmlhttp = Nothing



Function IsInArray(valToBeFound, arr)
    Dim i
    For i = LBound(arr) To UBound(arr)
        If arr(i) = valToBeFound Then
            IsInArray = True
            Exit Function
        End If
    Next
    IsInArray = False
End Function
%>