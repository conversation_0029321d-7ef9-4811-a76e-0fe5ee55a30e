<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../lib/config.asp"-->
<!--#include file="../lib/dingdingutil.asp"-->
<!--#include file="../../shujuku.asp"-->
<%
	'存储到日志表
	'参数：
	'    ids：            日志记录的ID，多个则以英文逗号分隔
	'返回：
	'	 errcode	      返回码
	'    errmsg	          对返回码的文本描述内容
	Response.Charset="utf-8"
	Response.ContentType="application/json"
	Set rstJSON = New aspJSON
	With rstJSON.data
    	.Add "errcode", -1
		.Add "errmsg", "fail"
	End With
	'on error resume next 
	set conn = GetSQLServerConnection()'获取数据库连接
	dim logsstr '日志详细信息
	logsstr = request("logs")
	dim sum,success,exist
	exist=0 
	sum=0
	success=0
	if logsstr <> "" then
		logsstr = "{""list"":" & logsstr & "}"
		Set oJSON = New aspJSON
		oJSON.loadJSON(logsstr)
		For Each phonenr In oJSON.data("list")
    		Set logs = oJSON.data("list").item(phonenr)
		  	'检查是否已经入过库
			
			Set rst=  CreateObject("ADODB.Recordset") 
			sql="SELECT [钉钉日志id] FROM [钉钉日志记录表] where [钉钉日志id]='" & logs.item("report_id") & "'"
			rst.open sql,conn,1,3,1
			iRowCount = rst.recordcount
			if iRowCount = 0 then
				dim n
				sql = "insert into [钉钉日志记录表]([录入人ID],[录入人],[录入日期],[实施日期],[实施情况],[日志类型],[钉钉日志id]) values (" & _
				"'" & logs.item("creator_id") & "','" & logs.item("creator_name") & "','" & logs.item("create_time") & "','" & logs.item("bussiness_time") & _
				"','" & logs.item("contents") & "','" & logs.item("template_name") & _
				"','" & logs.item("report_id") & "')"

				conn.Execute sql,n
				success = success + n
			else
				exist = exist + 1
			end if
			sum = sum + 1
		Next
	else
		err.number=-1
		err.Description="日志记录不能为空"
	end if
	
	if err.number=0 then
		rstJSON.data("errcode")=0
		rstJSON.data("errmsg")="总共操作" & sum & "条记录，执行成功" & success & "条," & exist & "条记录数据中已存在"
	else
		rstJSON.data("errcode")=err.number
		rstJSON.data("errmsg")=err.Description
	end if
	
	conn.close
	Response.Write rstJSON.JSONoutput() 
%>