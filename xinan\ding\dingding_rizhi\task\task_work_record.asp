<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->

<%
Response.Charset="utf-8"
Response.ContentType="application/json"
Set rstJSON = New aspJSON
With rstJSON.data
    .Add "errcode", 0
    .Add "errmsg", "success"
    .Add "list", rstJSON.Collection()
End With

Dim resultFlag

'查询参数
Dim startTime,endTime,userName,userId,name_pinYin,query_dsl
startTime = request.form("startTime")'开始时间
endTime = request.form("endTime")'结束时间
userId = ""
query_dsl = ""

Dim offsetDays
days = request.querystring("days")
if days = "" then
    offsetDays = 2
else
    offsetDays = days
    LogMessage "offsetDays -> " & days
end if

if startTime = "" or endTime = "" then
    endTime = Date()
    startTime = DateAdd("d", -(offsetDays), endTime)
end if
LogMessage "startTime -> " & startTime & " endTime -> " & endTime
response.write "startTime -> " & startTime & " endTime -> " & endTime & vbCrLf

'判断日期参数是否正确,2024/1/3,待更新上线
Dim currentDate
currentDate = Date
date1 = CDate(startTime)
date2 = CDate(endTime)

Dim diff
diff1 = DateDiff("d", date1, currentDate)
diff2 = DateDiff("d", date2, currentDate)
if diff1 > offsetDays or diff1 < 0 or diff2 > offsetDays or diff2 < 0 then
    response.write "参数有误,最多支持近"& offsetDays &"天," & "开始日期距今 " & diff1 & " 天,结束日期距今 "  & diff2 & " 天"
    response.end
end if

'获取access_token
Dim access_token
access_token = GetAccessToken("","",appkey,appsecret)
LogMessage "WorkRecord access_token -> " & access_token

startTime = int(ToUnixTime(startTime,+8))*1000
endTime = int(ToUnixTime(endTime,+8))*1000+86400000 '+86400000的原因是钉钉不查询当天的日报,需要输入明天的日期才能查看今天

Dim successNum,existNum
successNum = 0
existNum = 0

''''''''''''''''''''''''''''''''''''''''''''''''
'''''''''''''''以下开始同步销售跟进记录'''''''''''''
''''''''''''''''''''''''''''''''''''''''''''''''
'封装条件查询dsl参数
query_dsl = "{""queryGroupList"":[{""logicType"":""AND"",""queryObjectList"":[{""filterType"":""BETWEEN"",""value"":[""" & startTime & """,""" & endTime & """],""fieldId"":""gmt_create""}]}],""order"":""DESC"",""orderFieldId"":""gmt_create""}"
LogMessage "query_dsl -> " & query_dsl
response.write query_dsl & vbCrLf

'批量获取钉钉跟进记录,分页查询
Dim has_more, next_cursor, insIndex
has_more = true
next_cursor = 0
insIndex = 0
do while has_more
    '默认为false,防止后面逻辑错误死循环
    has_more = false
    Set params = New aspJSON ' 把 params 定义为一个 JSON 格式，把前端页面传过来的几个参数按照钉钉开发文档要求的格式进行梳理然后提交给钉钉云端查询'
    params.data.Add "cursor", next_cursor
    params.data.Add "page_size", 100
    params.data.Add "query_dsl", query_dsl

    Set FollowrecordQueryResult = FollowrecordQuery(params, access_token)

    dim errcode,errmsg
    errcode = FollowrecordQueryResult.data("errcode")

    '查询成功
    if errcode = 0 then
        resultFlag = true
        next_cursor = FollowrecordQueryResult.data("result").item("next_cursor") ' 下一页起始 id
        has_more = FollowrecordQueryResult.data("result").item("has_more") ' 判断是否有下一页
        set instanceList = FollowrecordQueryResult.data("result").item("values")
        if instanceList.count > 0 then
            For Each idx In instanceList
                'response.write TypeName(instanceList)
                Set item = instanceList.item(idx)

                Dim creator_userid,data,extend_data,gmt_create,gmt_modified,instance_id,object_type,proc_inst_status,proc_out_result
                creator_userid = item("creator_userid")
                data = item("data")
                extend_data = item("extend_data")
                gmt_create = item("gmt_create")
                gmt_modified = item("gmt_modified")
                instance_id = item("instance_id")
                object_type = item("object_type")
                proc_inst_status = item("proc_inst_status")
                proc_out_result = item("proc_out_result")
				
				'解析出关联的单位名称
				data = formatStr2Json(data)
				Set companyData = New aspJSON
				companyData.loadJSON(data)
				instanceId = companyData.data("follow_record_related_customer").item("extendValue").item("list").item(0).item("instanceId")
				companyName = companyData.data("follow_record_related_customer").item("value").item(0)
				
				'插入单位信息
				dim companyId
				companyId = insertCompany(companyName, instanceId, creator_userid)

                sql="SELECT [instance_id] FROM [saler_work_record] where [instance_id] = '" & instance_id & "'"
                rs.open sql,conn,1,2
                iRowCount = rs.recordCount
                if iRowCount = 0 then
                    sql = "insert into [saler_work_record]([crm_customer_id],[creator_userid],[data],[extend_data],[gmt_create],[gmt_modified],[instance_id],[object_type],[proc_inst_status],[proc_out_result],[Import_date],[status]) values (" & _
                    "'" & companyId & "','" & creator_userid & "','" & data & "','" & extend_data & "','" & gmt_create & _
                    "','" & gmt_modified & "','" & instance_id & _
                    "','" & object_type & "','" & proc_inst_status & _
                    "','" & proc_out_result & "','" & now() & "', 0)"
                    LogMessage "insert into [saler_work_record] -> " & sql
                    conn.Execute(sql)
                    successNum = successNum + 1
                else
                    'sql="delete  from [saler_work_record] where [instance_id]='"&instance_id&"' "
                    'conn.execute(sql)
                    existNum = existNum + 1
                end if
                rs.close

                'With rstJSON.data("list")
                '    .Add insIndex, instanceList(idx)
                'end With
                insIndex = insIndex + 1
            Next
        end if
    else
        errmsg = FollowrecordQueryResult.data("errmsg")
        LogMessage "errmsg -> " & errmsg
        resultFlag = errmsg
        Exit Do
    end if
loop
msg = "sync WorkRecord result,totalNum -> " & insIndex & " successNum -> " & successNum & " existNum -> " & existNum
LogMessage msg
if resultFlag = true then
    response.write "sync WorkRecord success -> " & msg & vbCrLf
else
    response.write "sync WorkRecord failed  -> " & msg & resultFlag & vbCrLf
end if

'''''''''''''''''''''''''''''''''''''''''''''''''''''
'''''''''''''''以下开始同步日报数据''''''''''''''''''''''
'''''''''''''''''''''''''''''''''''''''''''''''''''''

'插入单位信息
Function insertCompany(companyName, instanceId, creator_userid)
    sql1 = "SELECT [id] FROM [crm_customer_personal] WHERE [instance_id]='" & instanceId & "'"

    ''根据钉钉ID查询OA用户
    Set rs3 = Server.CreateObject("ADODB.Recordset")
    sql3 = "SELECT [mingzhi] FROM [user2011] where [dingding_id] = '" & creator_userid & "'"
    LogMessage "user2011 select sql3 -> " & sql3
    rs3.open sql3,conn,1,2
    if not rs3.eof then
        nickname = rs3(0)
    end if

    Dim rs1
    Set rs1 = Server.CreateObject("ADODB.Recordset")
    rs1.Open sql1, conn, 1, 2
    iRowCount = rs1.recordCount

	dim companyId
    If iRowCount = 0 Then
        sql2 = "insert into [crm_customer_personal]([instance_id],[customer_name],[created_at],[sales]) values (" & _
        "'" & instanceId & "','" & companyName & "','" & now() & "','" & nickname & "')"
        
        LogMessage "CrmCustomer insert sql -> " & sql2
        conn.Execute(sql2)

        ' 获取新插入的ID
		
		Set rs2 = Server.CreateObject("ADODB.Recordset")
    	rs2.Open sql1, conn,1,2
		companyId =  rs2("id")
		rs2.Close
    	Set rs2 = Nothing
		LogMessage "插入的单位ID是: " & companyId & " 单位名称：" & companyName
    Else
        ''更新该单位销售为当前负责人
        sql4 = "UPDATE [crm_customer_personal] SET [sales] = '" & nickname & "', [update_at] = '"& now() &"' WHERE id = " & rs1("id")
        LogMessage "CrmCustomer update sql -> " & sql4
        conn.Execute(sql4)

		LogMessage "已存在单位ID -> " & rs1("id") & " 单位名称：" & companyName
		companyId = rs1("id")
    End If
    
    rs1.Close
    Set rs1 = Nothing
	insertCompany = companyId
End Function

Function PostHttpPageWithHeaderDify(method, httpUrl, headerToken, data)
    If IsNull(httpUrl) = True Or Len(httpUrl)<18 Or httpUrl = "$False$" Then
        PostHttpPageWithHeader = "$False$"
        Exit Function
    End If
    Dim Http
    Set Http = server.CreateObject("MSXML2.ServerXMLHTTP")
    Http.setOption(2) = 13056
    'Set Http = server.CreateObject("MSXML2.XMLHTTP")
    Http.Open method, httpUrl, False
    Http.setRequestHeader "CONTENT-TYPE", " application/json;charset=utf-8"
    Http.setRequestHeader "Authorization", headerToken
    Http.Send data
    If Http.Readystate<>4 Then
        Set Http = Nothing
        PostHttpPageWithHeader = "$False$"
        Exit Function
    End If
    'PostHttpPage = bytesToBSTR(Http.responseBody, "g b2312")
    PostHttpPageWithHeaderDify = bytesToBSTR(Http.responseBody, "utf-8")
    Set Http = Nothing
    If Err.Number<>0 Then
        Err.Clear
    End If
End Function

'将数据插入到数据库中
Function insertReport(report_id, dataItem)
    Dim successNum,existNum
    successNum = 0
    existNum = 0

    creator_id = dataItem("creator_id")
    creator_name = dataItem("creator_name")
    create_time = dataItem("create_time")
    create_time = DateAdd("s", create_time / 1000, #1970/01/01 00:00:00#)
    dept_name = dataItem("dept_name")
    modified_time = dataItem("modified_time")
    modified_time = DateAdd("s", modified_time / 1000, #1970/01/01 00:00:00#)
    remark = dataItem("remark")
    report_id = dataItem("report_id")
    template_name = dataItem("template_name")

    Set oJSON = New aspJSON
    With oJSON.data
        .Add "contents", oJSON.Collection()
        With oJSON.data("contents")
            For Each contentKey In dataItem("contents")
                .Add contentKey, oJSON.Collection()
                With .item(contentKey)
                    .Add "key", dataItem("contents").item(contentKey).item("key")
                    .Add "value", dataItem("contents").item(contentKey).item("value")
                End With
            Next
        End With
    End With
    contents = oJSON.JSONoutput()
    contents = Trim(Replace(Replace(contents, vbCrLf, " "), " ", ""))
    contents = Replace(contents, "'", """")
    contents = Mid(contents, 13, Len(contents) - 13)

    '清理数据
    'sql="delete  from [ding_talk_report] where [report_id]='"&report_id&"' "
    'conn.execute(sql)

    '调用AI Agent获取评语及评分
    'Dim aiUrl, aiToken, aiHeaders, aiData, aiResponse, aiResult, evaluation, score
    'aiUrl = "http://**************/v1/completion-messages"
    'aiToken = "Bearer app-pRNE6hKdSw2x33SwIBijPsp5"
    '
    'Set aiHeaders = CreateObject("Scripting.Dictionary")
    'aiHeaders.Add "Authorization", aiToken
    'aiHeaders.Add "Content-Type", "application/json"
    '
    'Set aiData = New aspJSON
    'With aiData.data
    '    .Add "inputs", aiData.Collection()
    '    With .item("inputs")
    '        .Add "question_format", "请评价一下日报的内容"
    '        .Add "job", dept_name&"工程师"
    '        .Add "work_log_data", contents
    '    End With
    '    .Add "response_mode", "blocking"
    '    .Add "user", "secAdmin"
    'End With
    
    'dim aiResultStr
    'aiResultStr = PostHttpPageWithHeaderDify("POST", aiUrl, aiToken, aiData.JSONoutput())
    'Set aiResult = New aspJSON
    'aiResult.loadJSON(aiResultStr)
    'response.write aiResult.data("answer")
    'response.end
    
    'If aiResult.data("answer") <> "" Then
    '    evaluation = UnicodeToStr(Split(aiResult.data("answer"), "|")(1))
    '    score = Split(aiResult.data("answer"), "|")(0)
    'Else
    '    evaluation = ""
    '    score = "0"
    'End If
    
    evaluation = ""
    score = "0"

    'LogMessage "dailyReport score -> " & score & " evaluation -> " & evaluation
    'LogMessage "dailyReport score -> " & score 

    sql="SELECT [report_id] FROM [ding_talk_report] where [report_id]='" & report_id & "'"
    rs.open sql,conn,1,2
    iRowCount = rs.recordCount
    if iRowCount = 0 then
        sql = "insert into [ding_talk_report]([creator_id],[creator_name],[create_time],[dept_name],[modified_time],[remark],[report_id],[template_name],[contents],[import_date],[comment],[score]) values (" & _
        "'" & creator_id & "','" & creator_name & "','" & create_time & "','" & dept_name & _
        "','" & modified_time & "','" & remark & _
        "','" & report_id & "','" & template_name & _
        "','" & contents & "','" & now() & "','" & evaluation & "','" & score & "')"
        'LogMessage "dailyReport insert sql -> " & sql
        'response.write sql
        'response.end
        conn.Execute(sql)
        successNum = successNum + 1
    else
        existNum = existNum + 1
    end if
    rs.close
    insertReport = successNum& "," &existNum
End Function

Function UnicodeToStr(str)
    Dim regEx, matches, match
    Set regEx = New RegExp
    regEx.Pattern = "\\u([0-9a-fA-F]{4})"
    regEx.Global = True

    Set matches = regEx.Execute(str)
    For Each match in matches
        Dim hexVal, charVal
        hexVal = match.SubMatches(0)
        charVal = ChrW("&H" & hexVal)
        str = Replace(str, match.Value, charVal)
    Next
    UnicodeToStr = str
End Function

access_token = GetAccessToken(corpId,corpSecret,appkey,appsecret)
LogMessage "dailyReport access_token -> " & access_token

successNum = 0
existNum = 0

has_more = true
next_cursor=0
insIndex = 0
do while has_more
    has_more = false
    Set params = New aspJSON '把params定义为一个json格式,把前端页面传过来的几个参数按照钉钉开发文档要求的格式进行梳理 然后提交给钉钉云端查询'
    params.data.Add "cursor", next_cursor
    params.data.Add "size", 20
    params.data.Add "start_time", startTime
    params.data.Add "end_time", endTime
    'params.data.Add "template_name", templateName
    'params.data.Add "userid", userId
    LogMessage params.JSONOutput()
    Set logList = ListLogs(params,access_token)'ListLogs函数就是dingdingutil.asp页面里面向钉钉post获取日志的函数'
    'response.write logList.JSONOutput()
    'response.end
    'LogMessage logList.JSONOutput()
    'dim errcode,errmsg
    errcode = logList.data("errcode")
    if errcode = 0 then '成功
        resultFlag = true
        set dataList = logList.data("result").item("data_list") '取出日志列表
        next_cursor = logList.data("result").item("next_cursor") '下一页起始id
        has_more = logList.data("result").item("has_more") '判断是否有下一页
        'has_more = false
        if dataList.count > 0 then
            For Each idx In dataList
                Set dataItem = dataList.item(idx)

                ''''''''''''''''''''demo''''''''''''''''''''
                    '.Add "familyName", "Smith"
                    '.Add "familyMembers", oJSON.Collection()

                    'With oJSON.data("familyMembers")
                    '    .Add 0, oJSON.Collection()
                    '    With .item(0)
                    '        .Add "firstName", "John"
                    '        .Add "age", 41

                    '        .Add "job", oJSON.Collection()          'Create named object
                    '        With .item("job")
                    '            .Add "function", "Webdeveloper"
                    '            .Add "salary", 70000
                    '        End With
                    '    End With
                    'End With

                    'response.write oJSON.JSONOutput()
                ''''''''''''''''''''demo''''''''''''''''''''

                For Each key In dataItem

                    'contents = dataItem.item("contents")
                    'response.write contents

                    Set oJSON = New aspJSON
                    With oJSON.data
                        if key = "contents" then
                            .Add "contents", oJSON.Collection()
                            With oJSON.data("contents")
                                    For Each contentKey In dataItem("contents")
                                        .Add contentKey, oJSON.Collection()
                                        With .item(contentKey)
                                            .Add "key", dataItem("contents").item(contentKey).item("key")
                                            .Add "value", dataItem("contents").item(contentKey).item("value")
                                        End With

                                        'cotItem.data.Add dataItem("contents").item(contentKey).item("key"), dataItem("contents").item(contentKey).item("value")
                                        'report = dataItem("contents").item(contentKey).item("key") & ":" & dataItem("contents").item(contentKey).item("value")
                                        'response.write report & vbCrLf
                                        'response.end
                                    Next
                            End With
                        else
                            if not key = "images" then
                                oJSON.data.Add key, dataItem.item(key)
                            end if
                        end if
                    End With
                Next

                result = insertReport(report_id, dataItem)
                toalNum = Split(result, ",")
                successNum = successNum + toalNum(0)
                existNum = existNum + toalNum(1)
                insIndex = insIndex + 1
            Next
            LogMessage "successNum -> "& successNum & " existNum -> " & existNum
        end if
    else
        errmsg = logList.data("errmsg")
        LogMessage "errmsg -> " & errmsg
        resultFlag = errmsg
        Exit Do
    end if
loop

conn.close
msg = "sync DailyReport result,totalNum -> " & insIndex & " successNum -> " & successNum & " existNum -> " & existNum
LogMessage msg
if resultFlag = true then
    response.write "sync DailyReport success -> " & msg & vbCrLf
else
    response.write "sync DailyReport failed  -> " & msg & resultFlag & vbCrLf
end if
%>