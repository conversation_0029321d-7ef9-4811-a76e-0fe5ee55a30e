<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../../shujuku.asp"-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script> 
<script src="../js/json2.js" type="text/javascript"></script>
<title>日志记录</title>
</head>

<body>
<div style="padding:10px">
		<table border="0" cellpadding="0" cellspacing="0">
			<tr>
				<td style="width:100px">起止时间</td>
				<td style="width:380px">
					<input type="text" id="startTime" value="2019-12-01" /> ~ <input type="text" id="endTime" value="2019-12-30" />
				</td>
				<td style="width:80px">录入人</td>
				<td style="width:180px">
				<!--	<input type="text" id="userName" value="" /> -->
					<select name="userName" id="userName">
						<option></option>
						<%
						sql="select mingzhi from user2011 where 是否锁定=0 order by mingzhi"
						rs2.open sql,conn2,1,2
						while not rs2.eof 
						
						%>
						<option value="<%=rs2(0)%>"><%=rs2(0)%></option>
						<%
						rs2.movenext
						wend
						rs2.close
						%>
					</select>
				</td>
				<td style="width:100px">日志类型</td>
				<td style="width:180px">
					<select id="templateName">
						<option value=""></option>
						<option value="日报">日报</option>
						<option value="周报">周报</option>
					</select>
				</td>
				<td>
                	<input type="button" id="query" value="查询"/>
                    <input type="button" id="bathIns" value="批量导入日志"  style="margin-left:10px"/>
                </td>
			</tr>
		</table>
	</div>
	<hr/>
	<div style="padding:10px">
		<table border="0" cellpadding="0" cellspacing="0" width="100%" style="border:0px solid #666666">
			<thead>
				<tr>
                	<th width="5%" height="40px"><input type="checkbox" id="all" /></th>
					<th width="10%">日志ID</th>
					<th width="10%">录入人</th>
                    <th width="10%">录入日期</th>
					<th width="15%">实施日期</th>
					<th width="15%">实施情况</th>
					<th width="10%">日志类型</th>
					<th width="10%">操作</th>
				</tr>
			</thead>
			<tbody id="datalist">
			
			</tbody>
			<tr id="tmp" style="display:none">
            	<td width="5%" height="40px" align="center">
					<input type="checkbox" id="report_id" name="report_id" value="" />
					<input type="hidden" id="creator_id" value="" />
					<input type="hidden" id="creator_name" value="" />
					<input type="hidden" id="create_time" value="" />
					<input type="hidden" id="bussiness_time" value="" />
					<input type="hidden" id="contents" value="" />
					<input type="hidden" id="template_name" value="" />
				</td>
				<td width="10%" align="center"></td>
				<td width="10%" align="center"></td>
                <td width="10%" align="center"></td>
				<td width="15%" align="center"></td>
				<td width="15%" align="center"></td>
				<td width="10%" align="center"></td>
				<td width="10%" align="center"></td>
			</tr>
		</table>
	</div>
	<script type="text/javascript">
		//由于钉钉日志查询的起始日期要求必须是时间的毫秒数格式,所以需要把日期转化为这格式 https://ding-doc.dingtalk.com/doc#/serverapi2/yknhmg
		function toNormalDate(str,format) {
			var oDate = new Date(str), oYear = oDate.getFullYear(),oMonth = oDate.getMonth() + 1,oDay =  oDate.getDate(),oHour = oDate.getHours(),oMin = oDate.getMinutes(),oSen = oDate.getSeconds();
			oTime = oYear + '-' + getzf(oMonth) + '-' + getzf(oDay)+ ' ' + getzf(oHour)+ ':' + getzf(oMin)+ ':' + getzf(oSen); //最后拼接时间
			if(!format){
				format="Y-m-d-h-i-s";
			}
			if(format==="Y-m-d"){
				dataTime= oTime.substr(0,10);
			}else if(format==="Y-m-d-h-i"){
				dataTime= oTime.substr(0,21);
			}else if(format==="Y-m-d-h-i-s"){
				dataTime= oTime;
			}
			return dataTime;
		}
		//getzf 函数为把1-9月份数字前面加个0字符
		function getzf(num) {
			if(parseInt(num) < 10) {
				num = '0' + num;
			}
			return num;
		}
		//getLogContent函数是获取日志的内容 由于日报的内容会有很多组件组成,所以要把日报内容全部以数组的形式读取出来
		function getLogContent(contents){
			var content = [];
			for(var i=0;i<contents.length;i++) {
				var conn = contents[i];
				//JavaScript push() 方法可向数组的末尾添加一个或多个元素，并返回新的长度。
				content.push(conn.key+":"+conn.value);
			}
			//JavaScript join() 方法用于把数组中的所有元素放入一个字符串。下面是代表用"；"隔开
			return content.join("；");
		}
		//导入到数据库
		function addLog(ids){
			var logs = [];
			ids = ids.split(",");
			for(var i=0;i<ids.length;i++){
				var logid = ids[i];
				var log = {};
				var tr = $("#report_id_"+logid);
				log.report_id=$("#report_id",tr).val();
				log.creator_id=$("#creator_id",tr).val();
				log.creator_name=$("#creator_name",tr).val();
				log.create_time=$("#create_time",tr).val();
				log.bussiness_time=$("#bussiness_time",tr).val();
				log.contents=$("#contents",tr).val();
				log.template_name=$("#template_name",tr).val();
				logs.push(log);
			}
			$.ajax({
				 type: "POST",
				 url: "savelog.asp",
				 data: {logs:JSON.stringify(logs),},
				 dataType: "json",
				 success: function(data){
					 if(data.errcode==0){
						alert(data.errmsg || "导入日志成功");
					}else{
						alert(data.errmsg || 'error')
					}
					
				 },
				 error:function(e){
					console.log(e);
				 }
			});
		}
		$(function(){
			//批量导入日志
			$("#bathIns").click(function(){
				var idArr = new Array;
        		$("#datalist :checkbox[id='report_id']").each(function(i){
					if($(this).prop("checked")){
						idArr.push($(this).val());	
					}
        		});
       			var ids = idArr.join(',');
				if(ids==""){
					alert("请选择记录");	
					return;
				}
				addLog(ids);
			});
			
			//查询按钮
			$("#query").click(function(){
				$.ajax({
					 type: "POST",
					 url: "queryloglist.asp",  //把下面的4个参数内容传递到这个页面里进行查询
					 data: {
						 startTime:$.trim($("#startTime").val()), 
					 	 endTime:$.trim($("#endTime").val()),
						 templateName:$.trim($("#templateName").val()),
						 userName:$.trim($("#userName").val())
					 },
					 dataType: "json",
					 success: function(data){
						if(data.errcode==0){
							var list = data.list;//查询成功后找到日志列表datalist里面的内容,这些名字都是钉钉开发文档里规定好的名字https://ding-doc.dingtalk.com/doc#/serverapi2/yknhmg
							$("#datalist").html("");
							for(var i=0;i<list.length;i++){
								var item = list[i];
								var con = $("#tmp").clone();
								con.attr("id","report_id_"+item.report_id);
								var tds = con.find("td");
								$(tds[0]).find(":checkbox[id='report_id']").val(item.report_id);//调取日志id
								$(tds[0]).find(":hidden[id='creator_name']").val(item.creator_name);//调取日志创建人
								$(tds[0]).find(":hidden[id='create_time']").val(toNormalDate(item.create_time));//调取日志创建时间
								$(tds[0]).find(":hidden[id='bussiness_time']").val(toNormalDate(item.create_time));//调取日志创建时间
								$(tds[0]).find(":hidden[id='contents']").val(getLogContent(item.contents));//调取日志内日
								$(tds[0]).find(":hidden[id='template_name']").val(item.template_name);//调取日志模板名
								$(tds[0]).find(":hidden[id='creator_id']").val(item.creator_id);//调取日志创建人userid 即钉钉id

								$(tds[1]).html(i+"."+item.report_id);
								$(tds[2]).html(item.creator_name);
								$(tds[3]).html(toNormalDate(item.create_time));
								$(tds[4]).html(toNormalDate(item.create_time));
								$(tds[5]).html(getLogContent(item.contents));
								$(tds[6]).html(item.template_name);
								$(tds[7]).html('<a href="javascript:;" onclick="addLog(\''+item.report_id+'\')">导入日志</a>');
								con.show();
								$("#datalist").append(con);
							}
							alert("查询成功");
						}else{
							alert(data.errmsg || 'error')
						}
						
					 },
					 error:function(e){
						console.log(e);
					 }
				});
			});
			
			//全选或全不选
			$("#all").click(function(){   
				if(this.checked){   
					$("#datalist :checkbox").prop("checked", true);  
				}else{   
					$("#datalist :checkbox").prop("checked", false);
				}   
			}); 
		});
		
	</script>
</body>
</html>
