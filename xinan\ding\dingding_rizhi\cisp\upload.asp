<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="uploadutil.asp"-->
<%
Response.CharSet = "utf-8"
Session.CodePage = 65001
Response.ContentType = "application/json"

Function JsonResponse(success, message, data)
    Dim json
    json = "{""success"":" & LCase(success) & ",""message"":""" & message & """"
    If Not IsEmpty(data) Then
        json = json & ",""data"":" & data
    End If
    json = json & "}"
    JsonResponse = json
End Function

If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    Dim personId
    personId = Request.QueryString("person_id")
    If personId = "" Then
        Response.Write JsonResponse(False, "个人ID不能为空", "")
        Response.End
    End If

    Dim FileUpload, FormName, f_Err, f_Name, f_SaveName, f_Path, f_Size, f_Ext, f_Time
    FormName = "file" ' 文件域名称
    Set FileUpload = New UpLoadClass
    FileUpload.Charset = "UTF-8"
    FileUpload.SavePath = "./uploads/" & Year(Now) & "/" & Right("0" & Month(Now), 2) & "/" & Right("0" & Day(Now), 2) & "/" & personId & "/"
    FileUpload.MaxSize = 10 * 1024 * 1024
    FileUpload.FileType = "jpg/png/gif/pdf/jpeg/docx/doc"

    ' 确保目录存在
    Dim uploadDir
    uploadDir = Server.MapPath(FileUpload.SavePath)
    Dim objFSO
    Set objFSO = Server.CreateObject("Scripting.FileSystemObject")
    If Not objFSO.FolderExists(uploadDir) Then
        Dim arr, i, cur
        arr = Split(uploadDir, "\")
        cur = arr(0)
        For i = 1 To UBound(arr)
            cur = cur & "\" & arr(i)
            If Not objFSO.FolderExists(cur) Then
                objFSO.CreateFolder(cur)
            End If
        Next
    End If
    Set objFSO = Nothing

    On Error Resume Next
    FileUpload.Open()
    If Err.Number <> 0 Then
        Response.Write JsonResponse(False, "上传初始化失败: " & Err.Description, "")
        Response.End
    End If
    On Error Goto 0

    f_Err = FileUpload.Form(FormName & "_Err")
    If f_Err = "" Then f_Err = -1
    If CInt(f_Err) = 0 Then
        f_Name = FileUpload.Form(FormName & "_Name")
        f_SaveName = FileUpload.Form(FormName)
        f_Path = FileUpload.SavePath
        f_Size = FileUpload.Form(FormName & "_Size")
        f_Ext = FileUpload.Form(FormName & "_Ext")
        f_Time = Now()

        ' 获取当前访问的目录URL前缀
        Dim serverUrl, scriptPath, baseUrl
        serverUrl = "http://" & Request.ServerVariables("HTTP_HOST")
        scriptPath = Request.ServerVariables("SCRIPT_NAME")
        ' 去掉脚本名，保留目录
        baseUrl = Left(scriptPath, InStrRev(scriptPath, "/"))

        ' 拼接完整URL
        relativePath = Replace(FileUpload.SavePath & f_SaveName, "./", "") ' 去掉./
        Dim fullUrl
        fullUrl = serverUrl & baseUrl & relativePath

        ' 规范化路径中的多余斜杠
        fullUrl = Replace(fullUrl, "//", "/")
        fullUrl = Replace(fullUrl, "http:/", "http://")

        Response.Write JsonResponse(True, "上传成功", """" & fullUrl & """")
    Else
        Dim errMsg
        Select Case f_Err
            Case 1
                errMsg = "文件大小超出限制(最大10MB)"
            Case 2
                errMsg = "文件类型不允许"
            Case 3
                errMsg = "文件大小超出限制且类型不允许"
            Case Else
                errMsg = "未知错误，f_Err=" & f_Err & "，扩展名=" & FileUpload.Form(FormName & "_Ext")
        End Select
        Response.Write JsonResponse(False, errMsg, "")
    End If

    Set FileUpload = Nothing
    Response.End
Else
    Response.Write JsonResponse(False, "仅支持POST请求方式", "")
End If
%>