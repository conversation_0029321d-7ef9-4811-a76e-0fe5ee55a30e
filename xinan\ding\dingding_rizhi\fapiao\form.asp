<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>发票信息录入</title>
    <style>
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .upload-preview {
            max-width: 200px;
            margin-top: 10px;
            display: none;
        }
        .btn {
            background-color: #5cb85c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #4cae4c;
        }
        .upload-preview-container {
            position: relative;
            display: inline-block;
            margin-top: 10px;
        }
        .remove-btn {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: none;
        }
        .pdf-preview {
            width: 100px;
            height: 100px;
            display: none;
            margin-top: 10px;
            position: relative;
            background: #f4f4f4;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
        }
        .pdf-preview::before {
            content: 'PDF';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            font-weight: bold;
            color: #dc3545;
        }
        .pdf-preview::after {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            background: #dc3545;
            clip-path: polygon(20% 0%, 80% 0%, 100% 20%, 100% 80%, 80% 100%, 20% 100%, 0% 80%, 0% 20%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>发票信息录入</h2>
        
        <form id="mainForm" method="post" action="save.asp">
            <div class="form-group">
                <label for="title">发票标题</label>
                <input type="text" id="title" name="title" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="amount">金额</label>
                <input type="number" id="amount" name="amount" class="form-control" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label>发票图片</label>
                <input type="hidden" id="imgUrl" name="imgUrl">
                <div>
                    <input type="file" id="fileToUpload" accept=".jpg,.jpeg,.png,.pdf">
                    <button type="button" class="btn" id="uploadBtn" onclick="uploadFile()">上传</button>
                </div>
                <div class="upload-preview-container">
                    <img id="preview" class="upload-preview">
                    <div id="pdfPreview" class="pdf-preview"></div>
                    <button type="button" class="remove-btn" id="removeBtn" onclick="removeFile()">&times;</button>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">提交</button>
            </div>
        </form>
    </div>

    <script>
    function uploadFile() {
        var fileInput = document.getElementById('fileToUpload');
        var file = fileInput.files[0];
        if (!file) {
            alert('请选择文件');
            return;
        }

        var formData = new FormData();
        formData.append('fileToUpload', file);

        var uploadBtn = document.getElementById('uploadBtn');
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '上传中...';

        fetch('upload.asp', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('imgUrl').value = 'upfiles_caiwubu/' + data.filename;
                
                // 自动填充发票标题和金额
                if (data.apiResponse) {
                    document.getElementById('title').value = data.apiResponse.produce || '';
                    document.getElementById('amount').value = data.apiResponse.total_price || '';
                }
                
                var preview = document.getElementById('preview');
                var pdfPreview = document.getElementById('pdfPreview');
                var removeBtn = document.getElementById('removeBtn');
                
                // 根据文件类型显示不同的预览
                if (file.type === 'application/pdf') {
                    preview.style.display = 'none';
                    pdfPreview.style.display = 'block';
                } else if (file.type.startsWith('image/')) {
                    preview.src = 'upfiles_caiwubu/' + data.filename;
                    preview.style.display = 'block';
                    pdfPreview.style.display = 'none';
                }
                
                removeBtn.style.display = 'block';
                fileInput.value = '';
                uploadBtn.innerHTML = '重新上传';
                uploadBtn.disabled = false;
                
            } else {
                alert('上传失败：' + (data.error || '未知错误'));
                uploadBtn.innerHTML = '上传';
                uploadBtn.disabled = false;
            }
        })
        .catch(error => {
            alert('上传出错：' + error);
            uploadBtn.innerHTML = '上传';
            uploadBtn.disabled = false;
        });
    }

    function removeFile() {
        var preview = document.getElementById('preview');
        var pdfPreview = document.getElementById('pdfPreview');
        var removeBtn = document.getElementById('removeBtn');
        
        preview.src = '';
        preview.style.display = 'none';
        pdfPreview.style.display = 'none';
        removeBtn.style.display = 'none';

        document.getElementById('imgUrl').value = '';
        document.getElementById('uploadBtn').innerHTML = '上传';
        document.getElementById('fileToUpload').value = '';
    }
    </script>
</body>
</html>