<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../shujuku.asp"-->
<%
Dim action
action = Request.QueryString("action")

If action = "delete" Then
    Dim table_name, id
    table_name = Request.QueryString("table")
    id = Request.QueryString("id")
    
    If table_name <> "" And IsNumeric(id) Then
        Dim delete_sql
        Select Case table_name
            Case "personal_info"
                delete_sql = "DELETE 认证培训之个人信息表 WHERE ID = " & id
            Case "training_reg"
                delete_sql = "DELETE 认证培训之培训报名表 WHERE id = " & id
            Case "education"
                delete_sql = "DELETE FROM 认证培训之文化程度表 WHERE ID = " & id
            Case "certificate"
                delete_sql = "DELETE FROM 认证培训之培训证书表 WHERE ID = " & id
            Case "work_exp"
                delete_sql = "DELETE FROM 认证培训之工作经历 WHERE 编号 = " & id
        End Select
        
        conn.Execute delete_sql
        Response.Write "{""success"": true}"
        Response.End
    End If
End If
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>认证培训管理系统</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .btn-delete { color: red; cursor: pointer; }
        .section { margin-bottom: 30px; }
        h2 { color: #333; }
    </style>
    <script>
        function deleteRecord(table, id) {
            if (confirm('确定要删除这条记录吗？')) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', 'list.asp?action=delete&table=' + table + '&id=' + id, true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            alert('删除成功');
                            location.reload();
                        }
                    }
                };
                xhr.send();
            }
        }
    </script>
</head>
<body>
    <div class="section">
        <h2>培训报名信息</h2>
        <table>
            <tr>
                <th>编号</th>
                <th>姓名</th>
                <th>手机号</th>
                <th>性别</th>
                <th>身份证号</th>
                <th>手机号码</th>
                <th>单位</th>
                <th>报考方向</th>
                <th>意向考试月份</th>
                <th>意向考试城市</th>
                <th>考试状态</th>
                <th>创建时间</th>
                <th>更新时间</th>
                <th>是否删除</th>
                <th>操作</th>
            </tr>
            <%
            Dim rs_training
            Set rs_training = conn.Execute("SELECT * FROM [认证培训之培训报名表] ORDER BY 创建时间 DESC")
            Do While Not rs_training.EOF
            %>
            <tr>
                <td><%=rs_training("id")%></td>
                <td><%=rs_training("姓名")%></td>
                <td><%=rs_training("手机号码")%></td>
                <td><%=rs_training("性别")%></td>
                <td><%=rs_training("身份证号")%></td>
                <td><%=rs_training("手机号码")%></td>
                <td><%=rs_training("单位")%></td>
                <td><%=rs_training("报考方向")%></td>
                <td><%=rs_training("意向考试月份")%></td>
                <td><%=rs_training("意向考试城市")%></td>
                <td><%=rs_training("考试状态")%></td>
                <td><%=rs_training("创建时间")%></td>
                <td><%=rs_training("更新时间")%></td>
                <td><%=rs_training("是否删除")%></td>
                <td><a href="#" class="btn-delete" onclick="deleteRecord('training_reg', <%=rs_training("id")%>)">删除</a></td>
            </tr>
            <%
                rs_training.MoveNext
            Loop
            rs_training.Close
            %>
        </table>
    </div>

    <div class="section">
        <h2>个人信息</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>姓名</th>
                <th>通信地址</th>
                <th>手机号码</th>
                <th>电子邮箱</th>
                <th>电子版照片</th>
                <th>身份证正面</th>
                <th>身份证反面</th>
                <th>证书信息附件</th>
                <th>创建时间</th>
                <th>更新时间</th>
                <th>操作</th>
            </tr>
            <%
            Dim rs_personal
            Set rs_personal = conn.Execute("SELECT * FROM 认证培训之个人信息表 ORDER BY 创建时间 DESC")
            Do While Not rs_personal.EOF
            %>
            <tr>
                <td><%=rs_personal("ID")%></td>
                <td><%=rs_personal("姓名")%></td>
                <td><%=rs_personal("通信地址")%></td>
                <td><%=rs_personal("手机号码")%></td>
                <td><%=rs_personal("电子邮箱")%></td>
                <td><%If rs_personal("电子版照片") <> "" Then%><a href="<%=rs_personal("电子版照片")%>" target="_blank">查看照片</a><%End If%></td>
                <td><%If rs_personal("身份证正面") <> "" Then%><a href="<%=rs_personal("身份证正面")%>" target="_blank">查看照片</a><%End If%></td>
                <td><%If rs_personal("身份证反面") <> "" Then%><a href="<%=rs_personal("身份证反面")%>" target="_blank">查看照片</a><%End If%></td>
                <td><%If rs_personal("证书信息附件") <> "" Then%><a href="<%=rs_personal("证书信息附件")%>" target="_blank">查看照片</a><%End If%></td>
                <td><%=rs_personal("创建时间")%></td>
                <td><%=rs_personal("更新时间")%></td>
                <td><a href="#" class="btn-delete" onclick="deleteRecord('personal_info', <%=rs_personal("ID")%>)">删除</a></td>
            </tr>
            <%
                rs_personal.MoveNext
            Loop
            rs_personal.Close
            %>
        </table>
    </div>
    
</body>
</html>