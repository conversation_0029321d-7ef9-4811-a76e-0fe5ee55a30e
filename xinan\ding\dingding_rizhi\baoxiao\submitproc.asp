<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<%
Response.CodePage = 65001
Response.Charset = "UTF-8"
Response.ContentType = "text/html; charset=UTF-8"
%>
<!--#include file="../../lib/config.asp"-->
<!--#include file="../../lib/dingdingutil.asp"-->
<!--#include file="../../../shujuku.asp"-->

<%
On Error Resume Next
	'功能：
	'	 发起OA新流程，提交到钉钉
	'参数：
	'    procType：            表单类型，plan（普通OA报销），chalv（差旅费OA报销）
	'	 formComponentValues： 表单参数，结合钉钉的流程表单设计，详见pageadd.asp页面的js中的formComponentValues参数组装
	'  	 originatorUserId：    钉钉用户ID，需要修改下面的固定值为实际的id
	'	 deptId：              钉钉部门ID，需要修改下面的固定值为实际的id
	'返回：
	'	 errcode	            返回码
	'    errmsg	                对返回码的文本描述内容
	'    process_instance_id	审批实例id
	Response.Charset="utf-8"
	Response.ContentType="application/json"
	Set rstJSON = New aspJSON
	With rstJSON.data
    	.Add "errcode", -1
		.Add "process_instance_id",""
		.Add "errmsg", "fail"
	End With
	on error resume next 
	dim procType,formComponentValues,originatorUserId,deptId,sqlValues
	procType = request("procType") '表单类型：plan（普通OA报销），chalv（差旅费OA报销）
	mydeptid=request("mydeptid")  '选择的所在部门id'
	formComponentValues = request("formComponentValues") '表单参数
	
		sqlValues=request("sqlValues")'表单里面的其他参数'
	
	'originatorUserId="manager7400" '钉钉用户ID，需要修改为实际的id
	originatorUserId=session("dingdingid")
	deptId=-1 '钉钉部门ID，需要修改为实际的id
	deptId=mydeptid
	'获取access_token
	dim access_token,errcode
	access_token = GetAccessToken(corpId,corpSecret,appkey,appsecret)
		dim formshouqian_id
			dim formshouhou_id
			dim formshouqian_rizhi_id
			dim formshouhou_rizhi_id
			dim formxmxuanzhe
						dim shouqianbiaoid '售前拜访表id号'
			dim shouhoubiaoid '售后拜访表id号'
			dim zz1
			dim zz2
			dim zz3
			dim zz4
	if procType = "plan" then '普通OA报销
		set rst = SubmitProcess(processTempCodeOA1,formComponentValues,originatorUserId,deptId,access_token)
		errcode = rst.data("errcode")
		if errcode <> 0 then
			err.number=errcode
			err.Description = rst.data("errmsg")
		else
			'把生成的钉钉审批编号取下来 process_instance_id.'
			rstJSON.data("process_instance_id")=rst.data("process_instance_id")

			'把费用报销单信息写入oa数据库'
			'若报销人选择把申请说明内容添加到售前或者售后项目工作日志里面'
		
			formshouqian_rizhi_id=GetFormValue(sqlValues,"售前拜访表id")
			formshouhou_rizhi_id=GetFormValue(sqlValues,"售后拜访表id")
			formshouqian_id=GetFormValue(sqlValues,"售前项目id")
			formshouhou_id=GetFormValue(sqlValues,"售后项目id")
			formxmxuanzhe=GetFormValue(sqlValues,"项目选择")
			

			zz1=0
			zz2=0
			zz3=0
			zz4=0
		  
		
		
			sql="select top 1 * from caiwu_baoxiao"
			rs2.open sql,conn2,3,2
			rs2.addnew
				rs2("时间")=GetFormValue(formComponentValues,"花销日期")
			   	rs2("申请名称")=GetFormValue(formComponentValues,"花销日期")&"_"&session("mingzhi")&"_费用报销单"
				rs2("支出")=GetFormValue(formComponentValues,"费用金额")
				rs2("费用类型")=GetFormValue(formComponentValues,"费用类型")
				rs2("子费用类型")=GetFormValue(formComponentValues,"费用二级类型")
				rs2("发票凭证")=GetFormValue(formComponentValues,"有无发票")
				rs2("说明")=GetFormValue(formComponentValues,"事由说明")
				rs2("所属账本")=GetFormValue(formComponentValues,"zhangben")
				rs2("录入人")=session("name")
				rs2("录入时间")=date()
			    rs2("报销人")=session("mingzhi")
			    rs2("当前状态")="钉钉审批中"
				rs2("钉钉审批编号")=rst.data("process_instance_id")
				
								if cint(formxmxuanzhe)=1  then 
									rs2("个人售前ID")=clng(formshouqian_rizhi_id)
									rs2("项目售前ID")=clng(formshouqian_id)
								  end if
								   '自动把申请说明提交到售后项目生成售后项目拜访id号'
								 if cint(formxmxuanzhe)=2  then 
									rs2("个人售后ID")=clng(formshouhou_rizhi_id)
									rs2("项目售后ID")=clng(formshouhou_id)
								  end if
				
				
				
				
			rs2.update
			rs2.close
		end if
	elseif procType = "chalv" then '差旅费OA报销
		set rst = SubmitProcess(processTempCodeOA2,formComponentValues,originatorUserId,deptId,access_token)
		errcode = rst.data("errcode")
		if errcode <> 0 then
			err.number=errcode
			err.Description = rst.data("errmsg")
		else
			'取差旅费报销单的钉钉审批号'
			rstJSON.data("process_instance_id")=rst.data("process_instance_id")
			'把差旅费报销单表单写入oa数据库'
	
			formshouqian_rizhi_id=GetFormValue(sqlValues,"售前拜访表id")
			formshouhou_rizhi_id=GetFormValue(sqlValues,"售后拜访表id")
			formshouqian_id=GetFormValue(sqlValues,"售前项目id")
			formshouhou_id=GetFormValue(sqlValues,"售后项目id")
			formxmxuanzhe=GetFormValue(sqlValues,"项目选择")
			zz1=0
			zz2=0
			zz3=0
			zz4=0

			sql="select top 1 * from caiwu_baoxiao_cailv"
			rs2.open sql,conn2,3,2
			rs2.addnew
				rs2("时间")=GetFormValue(formComponentValues,"出差起日")
			    rs2("当前状态")="钉钉审批中"
				rs2("支出总计")=GetFormValue(formComponentValues,"总金额")
				rs2("报销人")=session("mingzhi")
				rs2("申请名称")=GetFormValue(formComponentValues,"出差起日")&"_"&session("mingzhi")&"_差旅费报销单"
				rs2("城市")=GetFormValue(formComponentValues,"所去城市及单位")
				rs2("录入人")=session("name")
				rs2("录入时间")=date()
				rs2("备注")=GetFormValue(formComponentValues,"说明")
				rs2("钉钉审批编号")=rst.data("process_instance_id")
				rs2("火车费")=GetFormValue(formComponentValues,"火车")
				rs2("汽车费")=GetFormValue(formComponentValues,"汽车")
				rs2("船费")=GetFormValue(formComponentValues,"船费")
				rs2("飞机费")=GetFormValue(formComponentValues,"飞机")
				rs2("市内交通费")=GetFormValue(formComponentValues,"交通费")
				rs2("杂支")=GetFormValue(formComponentValues,"其他")
				rs2("出差天数")=GetFormValue(formComponentValues,"住宿天数")		
				rs2("住宿补助")=GetFormValue(formComponentValues,"住宿")
				rs2("伙食补助")=GetFormValue(formComponentValues,"差旅补助")
				rs2("所属账本")=GetFormValue(formComponentValues,"zhangben")

				   '自动把申请说明提交到售前项目生成售前项目拜访id号'
				  if cint(formxmxuanzhe)=1  then 
					rs2("个人售前ID")=cint(formshouqian_rizhi_id)
					rs2("项目售前ID")=cint(formshouqian_id)
				  end if
				   '自动把申请说明提交到售后项目生成售后项目拜访id号'
				  if cint(formxmxuanzhe)=2  then 
					' rs2("个人售后ID")=cint(formshouhou_rizhi_id)
					' rs2("项目售后ID")=cint(formshouhou_id)
				  end if
			
			rs2.update
			rs2.close
			
			
		end if
	else
		err.number=-1
		err.Description="表单类型错误"
	end if
	
	if err.number=0 then
		rstJSON.data("errcode")=0
		rstJSON.data("errmsg")="success"
	else
		rstJSON.data("errcode")=err.number
		rstJSON.data("errmsg")=err.Description
	end if
	
	Response.Write rstJSON.JSONoutput() 
	

	If Err.Number <> 0 Then
	Dim errorInfo
    Set errorInfo = Server.GetLastError()
    
	Response.Write "<b>错误编号:</b> " & errorInfo.Number & "<br>"
    Response.Write "<b>错误描述:</b> " & errorInfo.Description & "<br>"
    Response.Write "<b>错误来源:</b> " & errorInfo.Source & "<br>"
    Response.Write "<b>错误行号:</b> " & errorInfo.Line & "<br>"

    ' 清除错误对象
    Err.Clear
End If	
%>