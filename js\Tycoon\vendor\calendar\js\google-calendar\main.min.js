/*!
FullCalendar Google Calendar Plugin v4.3.0
Docs & License: https://fullcalendar.io/
(c) 2019 <PERSON> Shaw
*/
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@fullcalendar/core")):"function"==typeof define&&define.amd?define(["exports","@fullcalendar/core"],r):r((e=e||self).FullCalendarGoogleCalendar={},e.FullCalendar)}(this,function(e,r){"use strict";var t=function(){return(t=Object.assign||function(e){for(var r,t=1,n=arguments.length;t<n;t++)for(var o in r=arguments[t])Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o]);return e}).apply(this,arguments)},n="https://www.googleapis.com/calendar/v3/calendars",o={url:String,googleCalendarApiKey:String,googleCalendarId:String,data:null},a={parseMeta:function(e){if("string"==typeof e&&(e={url:e}),"object"==typeof e){var t=r.refineProps(e,o);if(!t.googleCalendarId&&t.url&&(t.googleCalendarId=function(e){var r;if(/^[^\/]+@([^\/\.]+\.)*(google|googlemail|gmail)\.com$/.test(e))return e;if((r=/^https:\/\/www.googleapis.com\/calendar\/v3\/calendars\/([^\/]*)/.exec(e))||(r=/^https?:\/\/www.google.com\/calendar\/feeds\/([^\/]*)/.exec(e)))return decodeURIComponent(r[1])}(t.url)),delete t.url,t.googleCalendarId)return t}return null},fetch:function(e,o,a){var l=e.calendar,i=e.eventSource.meta,d=i.googleCalendarApiKey||l.opt("googleCalendarApiKey");if(d){var s=function(e){return n+"/"+encodeURIComponent(e.googleCalendarId)+"/events"}(i),c=function(e,n,o,a){var l,i,d;a.canComputeOffset?(i=a.formatIso(e.start),d=a.formatIso(e.end)):(i=r.addDays(e.start,-1).toISOString(),d=r.addDays(e.end,1).toISOString());l=t({},o||{},{key:n,timeMin:i,timeMax:d,singleEvents:!0,maxResults:9999}),"local"!==a.timeZone&&(l.timeZone=a.timeZone);return l}(e.range,d,i.data,l.dateEnv);r.requestJson("GET",s,c,function(e,r){var t,n;e.error?a({message:"Google Calendar API: "+e.error.message,errors:e.error.errors,xhr:r}):o({rawEvents:(t=e.items,n=c.timeZone,t.map(function(e){return function(e,r){var t=e.htmlLink||null;t&&r&&(t=function(e,r){return e.replace(/(\?.*?)?(#|$)/,function(e,t,n){return(t?t+"&":"?")+r+n})}(t,"ctz="+r));return{id:e.id,title:e.summary,start:e.start.dateTime||e.start.date,end:e.end.dateTime||e.end.date,url:t,location:e.location,description:e.description}}(e,n)})),xhr:r})},function(e,r){a({message:e,xhr:r})})}else a({message:"Specify a googleCalendarApiKey. See http://fullcalendar.io/docs/google_calendar/"})}};var l=r.createPlugin({eventSourceDefs:[a]});e.default=l,Object.defineProperty(e,"__esModule",{value:!0})});