<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="lib2/config.asp"-->
<!--#include file="lib2/dingdingutil.asp"-->
<!--#include file="lib2/functions.asp"-->

<%
	'功能：
	'	 根据指定条件查询跟进记录数据
	'参数：
	'    page_size           分页大小
	'    cursor				 分页游标
	'返回：
	'	 errcode	            返回码
	'    errmsg	                对返回码的文本描述内容
	'	 result                 跟进记录详情，具体每个字段含义参考：https://open.dingtalk.com/document/orgapp/query-and-dingtalk-data-of-track-records-in-apsara-stack
	Response.Charset="utf-8"
	Response.ContentType="application/json"
	Set rstJSON = New aspJSON
	With rstJSON.data
        .Add "errcode", 0
		.Add "errmsg", "success"
        .Add "list", rstJSON.Collection()
	End With

	dim isSuccess

	'on error resume next

	'查询参数
	dim startTime,endTime,userName,userId,name_pinYin,query_dsl
	startTime = request.form("startTime")'开始时间
	endTime = request.form("endTime")'结束时间
	userName = request.form("userName")'跟进记录人姓名
	userId = ""
	query_dsl = ""	

	'判断日期参数是否正确,2024/1/3,待更新上线
	Dim currentDate
	currentDate = Date
    date1 = CDate(startTime)
    date2 = CDate(endTime)
	Dim diff
    diff1 = DateDiff("d", date1, currentDate)
    diff2 = DateDiff("d", date2, currentDate)
	if diff1 > 90 or diff1 < 0 or diff2 > 90 or diff2 < 0 then
		response.write "参数有误,最多支持近90天," & "开始日期距今 " & diff1 & " 天,结束日期距今 "  & diff2 & " 天"
		response.end
	end if

	'获取access_token
	dim access_token 
	access_token = GetAccessToken("","",appkey,appsecret)
	LogMessage "access_token -> " & access_token
	if userName <> "" then
		'通过名称将数据库中的用户钉钉id
		sql="select dingding_id,mingzhi,name from user2011 where mingzhi='"&userName&"'"
		rs.open sql,conn,1,2
		if not rs.eof then
		userid_oa=rs(0)
		username_oa=rs(1)
		name_pinYin=rs(2)
		end if
		rs.close
		userId = userid_oa
	end if

	'response.write userId
	'response.end


	'直接查库
	'access 使用gmt_create >= #xxx# and gmt_create <= #xxx#
	'sqlserver 使用gmt_create >= 'xxx' and gmt_create <= 'xxx'
	dim tmpStartTime, tmpEndTime
	tmpStartTime = startTime & " 0:0:1"
	tmpEndTime = endTime & " 23:59:59"
	sql="select * from saler_work_record where creator_userid = '"&userId&"' and gmt_create BETWEEN '"&tmpStartTime&"' AND '"&tmpEndTime&"' order by id desc "
	LogMessage "workRecord query local db, sql -> " & sql
	rs.open sql,conn,1,2
	Dim idx
	idx = 0
	Do While Not rs.EOF
	    id = rs("id")
	    creator_userid = rs("creator_userid")
	    data = Replace(rs("data"), vbTab, "")
	    extend_data = rs("extend_data")
	    instance_id = rs("instance_id")
	    gmt_create = rs("gmt_create")
	    gmt_modified = rs("gmt_modified")
	    object_type = rs("object_type")
	    object_type = rs("object_type")
	    proc_inst_status = rs("proc_inst_status")
	    proc_out_result = rs("proc_out_result")
	    import_date = rs("Import_date")
	    status = rs("status")
		if isNull(rs("status")) then
			status = 0
		end if

	    With rstJSON.data("list")
            .Add idx, rstJSON.Collection()
            With .item(idx)
                .Add "id", id
                .Add "creator_userid", creator_userid
                .Add "data", data
                .Add "extend_data", extend_data
                .Add "gmt_create", gmt_create
                .Add "gmt_modified", gmt_modified
                .Add "instance_id", instance_id
                .Add "object_type", object_type
                .Add "proc_inst_status", proc_inst_status
                .Add "proc_out_result", proc_out_result
                .Add "import_date", import_date
                .Add "status", status
            End With
        end With

        rs.MoveNext
        idx = idx + 1
    Loop
    rs.close
    Set rs = Nothing

    conn.close
    Set conn = Nothing
    response.write rstJSON.JSONoutput()
    response.end
%>