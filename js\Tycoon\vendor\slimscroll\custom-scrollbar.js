// Custom Scroll for Sidebar
$(function() {
	$('.sidebar-content').slimScroll({
		color: '#e1e5f1',
		alwaysVisible: false,
		size: "5px",
		height: "100%",
		distance: '1px',
		railVisible: false,
		railColor: "#e1e5f1",
		position: 'left',
	});
});


// Full Height
$(function() {
	$('.fullHeight').slimScroll({
		height: "100%",
		color: 'rgba(39, 47, 71, 0)',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "rgba(39, 47, 71, 0)",
	});
});


// Custom Scroll 5
$(function() {
	$('.customScroll5').slimScroll({
		height: "250px",
		color: 'rgba(39, 47, 71, 0)',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "rgba(39, 47, 71, 0)",
	});
});


// Custom Scroll 160
$(function() {
	$('.customScroll160').slimScroll({
		height: "160px",
		color: 'rgba(39, 47, 71, 0)',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "rgba(39, 47, 71, 0)",
	});
});




// Chat Box
$(function() {
	$('.chat-logs').slimScroll({
		height: "300px",
		color: 'rgba(39, 47, 71, 0)',
		alwaysVisible: false,
		size: "5px",
		distance: '2px',
		railVisible: false,
		railColor: "rgba(73, 75, 80, 0.3)",
	});
});