<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->
<%
Response.Charset="utf-8"
Response.ContentType="application/json"

' 获取GET参数
Dim typeParam

typeParam = Request.QueryString("type")

' http://************:12380/xinan/ding/dingding_rizhi/task/task_contact.asp?share_user_ids=&follower_user_id=范亚辉&address=&create_time=2025-06-25 17:13:17&label_ids=重要,合作伙伴,潜在,郑州&mobile=15038025127&remark=&title=&type=1&user_id=010039345034418848&company_name=天祺测评&name=袁晓恒&state_code=86&email=

' http://************:12380/xinan/ding/dingding_rizhi/task/task_contact.asp?sales_user_name=%E5%BC%A0%E6%B5%81%E5%86%9B&user_tag=%E6%80%A7%E6%A0%BC-%E4%B8%AD%E6%80%A7,%E8%84%BE%E6%B0%94-%E5%A5%BD,%E6%80%A7%E5%AD%90-%E7%A8%B3,%E6%B0%94%E5%9C%BA-%E7%A8%B3,%E6%8A%80%E6%9C%AF-%E4%B8%8D%E6%87%82&create_time=2025-06-25%2019:00:11&job_desc=&company_instance_id=n1BGEHd7R22ANIZa3J76dg00121712298133&remark=&dept=%E5%AE%89%E5%85%A8%E7%9B%91%E6%B5%8B%E4%BF%A1%E6%81%AF%E4%B8%AD%E5%BF%83&type=1&instance_id=1LqqBTVgQoGsFF7mBGA6jA00121712298134&sales_user_id=286866082826170328&phone=13213805552&office_tel=&company_name=%E6%B1%9D%E5%B7%9E%E5%B8%82%E7%85%A4%E7%9F%BF%E5%AE%89%E5%85%A8%E7%9B%91%E5%AF%9F%E5%B1%80&name=%E6%A8%8A%E6%9E%9C%E6%9E%9C&tel=&job=%E4%B8%BB%E4%BB%BB&email=&is_key_job=1

if typeParam = "1" then
    ' dingding_ext_contact 字段
    dim user_id, name, mobile, email, address, company_name, title, share_user_ids, state_code, follower_user_id, label_ids, share_dept_ids, remark
    user_id = Request.QueryString("user_id")
    name = Request.QueryString("name")
    mobile = Request.QueryString("mobile")
    email = Request.QueryString("email")
    address = Request.QueryString("address")
    company_name = Request.QueryString("company_name")
    title = Request.QueryString("title")
    share_user_ids = Request.QueryString("share_user_ids")
    state_code = Request.QueryString("state_code")
    follower_user_id = Request.QueryString("follower_user_id")
    label_ids = Request.QueryString("label_ids")
    share_dept_ids = Request.QueryString("share_dept_ids")
    remark = Request.QueryString("remark")

    
    Dim result2
    result2 = insertExtContact(user_id, name, mobile, email, address, company_name, title, share_user_ids, state_code, follower_user_id, label_ids, share_dept_ids, remark)
    Response.Write(result2)
else
    ' dingding_crm_contacts 字段
    instance_id = Request.QueryString("instance_id")
    company_name = Request.QueryString("company_name")
    org_id = Request.QueryString("org_id")
    company_instance_id = Request.QueryString("company_instance_id")
    custom_type = Request.QueryString("custom_type")
    sales_user_id = Request.QueryString("sales_user_id")
    sales_user_name = Request.QueryString("sales_user_name")
    team_user_id = Request.QueryString("team_user_id")
    team_user_name = Request.QueryString("team_user_name")
    name1 = Request.QueryString("name")
    dept = Request.QueryString("dept")
    job = Request.QueryString("job")
    phone = Request.QueryString("phone")
    tel = Request.QueryString("tel")
    job_desc = Request.QueryString("job_desc")
    is_key_job = Request.QueryString("is_key_job")
    email1 = Request.QueryString("email")
    user_tag = Request.QueryString("user_tag")
    office_tel = Request.QueryString("office_tel")
    remark1 = Request.QueryString("remark")
    dtalk_create_user = Request.QueryString("dtalk_create_user")
    dtalk_create_time = Request.QueryString("dtalk_create_time")
    dtalk_update_time = Request.QueryString("dtalk_update_time")
    create_by = Request.QueryString("create_by")
    create_time1 = Request.QueryString("create_time")
    update_time = Request.QueryString("update_time")
    
    Dim result1
    result1 = insertContact(instance_id, company_name, org_id, company_instance_id, custom_type, _
                      sales_user_id, sales_user_name, team_user_id, team_user_name, _
                      name1, dept, job, phone, tel, job_desc, is_key_job, _
                      email1, user_tag, office_tel, remark1, dtalk_create_user, _
                      dtalk_create_time, dtalk_update_time, create_by, create_time1, update_time)
    Response.Write(result1)
end if

'插入客户联系人信息
'type=1逻辑
Function insertContact(instance_id, company_name, org_id, company_instance_id, custom_type, _
                      sales_user_id, sales_user_name, team_user_id, team_user_name, _
                      name, dept, job, phone, tel, job_desc, is_key_job, _
                      email, user_tag, office_tel, remark, dtalk_create_user, _
                      dtalk_create_time, dtalk_update_time, create_by, create_time, update_time)
    On Error Resume Next
    sql1 = "SELECT [instance_id] FROM [dingding_crm_contacts] WHERE [instance_id]='" & instance_id & "'"
    Dim rs1
    Set rs1 = Server.CreateObject("ADODB.Recordset")
    rs1.Open sql1, conn, 1, 2
    If Err.Number <> 0 Then
        LogMessage "查询错误: " & Err.Description
        insertContact = 0
        rs1.Close
        Set rs1 = Nothing
        Exit Function
    End If
    iRowCount = rs1.recordCount
    if iRowCount = 0 then
        sql2 = "INSERT INTO [dingding_crm_contacts] (" & _
               "[instance_id], [company_name], [org_id], [company_instance_id], [custom_type], " & _
               "[sales_user_id], [sales_user_name], [team_user_id], [team_user_name], " & _
               "[name], [dept], [job], [phone], [tel], [job_desc], [is_key_job], " & _
               "[email], [user_tag], [office_tel], [remark], [dtalk_create_user], " & _
               "[dtalk_create_time], [dtalk_update_time], [create_by], [create_time], [update_time]) " & _
               "VALUES (" & _
               "'" & instance_id & "', '" & company_name & "', '" & org_id & "', '" & company_instance_id & "', '" & custom_type & "', " & _
               "'" & sales_user_id & "', '" & sales_user_name & "', '" & team_user_id & "', '" & team_user_name & "', " & _
               "'" & name & "', '" & dept & "', '" & job & "', '" & phone & "', '" & tel & "', '" & job_desc & "', '" & is_key_job & "', " & _
               "'" & email & "', '" & user_tag & "', '" & office_tel & "', '" & remark & "', '" & dtalk_create_user & "', " & _
               "'" & dtalk_create_time & "', '" & dtalk_update_time & "', '" & create_by & "', '" & create_time & "', '" & update_time & "')"
        LogMessage "CrmContacts insert sql -> " & sql2
        conn.Execute(sql2)
        If Err.Number <> 0 Then
            LogMessage "插入错误: " & Err.Description
            insertContact = 0
        Else
            LogMessage "联系人信息插入成功！"
            insertContact = 1
        End If
    else
        LogMessage "联系人信息已存在，instance_id: " & instance_id
        insertContact = 1
    end if
    rs1.Close
    Set rs1 = Nothing
End Function 

'插入外部联系人信息
'type=2逻辑
Function insertExtContact(user_id, name, mobile, email, address, company_name, title, share_user_ids, state_code, follower_user_id, label_ids, share_dept_ids, remark)
    On Error Resume Next
    Dim sql1, sql2, rs1
    sql1 = "SELECT [user_id] FROM [dingding_ext_contact] WHERE [user_id]='" & user_id & "'"
    Set rs1 = Server.CreateObject("ADODB.Recordset")
    rs1.Open sql1, conn, 1, 2
    If Err.Number <> 0 Then
        LogMessage "查询错误: " & Err.Description
        insertExtContact = 0
        rs1.Close
        Set rs1 = Nothing
        Exit Function
    End If
    iRowCount = rs1.recordCount
    if iRowCount = 0 then
        sql2 = "INSERT INTO [dingding_ext_contact] (" & _
               "[user_id], [name], [mobile], [email], [address], [company_name], [title], [share_user_ids], [state_code], [follower_user_id], [label_ids], [share_dept_ids], [remark]) " & _
               "VALUES (" & _
               "'" & user_id & "', '" & name & "', '" & mobile & "', '" & email & "', '" & address & "', '" & company_name & "', '" & title & "', '" & share_user_ids & "', '" & state_code & "', '" & follower_user_id & "', '" & label_ids & "', '" & share_dept_ids & "', '" & remark & "')"
        LogMessage "ExtContact insert sql -> " & sql2
        conn.Execute(sql2)
        If Err.Number <> 0 Then
            LogMessage "插入错误: " & Err.Description
            insertExtContact = 0
        Else
            LogMessage "外部联系人插入成功！"
            insertExtContact = 1
        End If
    else
        LogMessage "外部联系人已存在，user_id: " & user_id
        insertExtContact = 1
    end if
    rs1.Close
    Set rs1 = Nothing
End Function 

%>
