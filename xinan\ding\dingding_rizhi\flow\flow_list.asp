<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->

<%
' 处理删除请求
If Request.Form("action") = "delete" Then
    Dim idsToDelete, idArray, id
    idsToDelete = Request.Form("ids")
    
    If Len(idsToDelete) > 0 Then
        idArray = Split(idsToDelete, ",")
        
        For Each id In idArray
            If IsNumeric(id) Then
                ' 执行删除操作
                Conn.Execute("DELETE FROM caiwu_baoxiao_dingding_feiyong WHERE ID=" & id)
            End If
        Next
        
        ' 记录删除操作
        LogMessage "已从 caiwu_baoxiao_dingding_feiyong 表中删除记录，ID: " & idsToDelete
        
        ' 重定向以避免刷新时重复提交
        Response.Redirect "flow_list.asp"
    End If
End If

Dim rsCheck, sql
sql = "SELECT * FROM caiwu_baoxiao_dingding_feiyong order by id desc"

' 创建记录集对象并执行查询
Set rsCheck = Server.CreateObject("ADODB.Recordset")
rsCheck.Open sql, Conn, 1, 1

' 检查是否有数据
If Not rsCheck.EOF Then
%>
<html>
<head>
    <meta charset="UTF-8">
    <title>财务报销钉钉费用列表</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:hover { background-color: #f5f5f5; }
        .btn { 
            padding: 8px 16px; 
            background-color: #4CAF50; 
            color: white; 
            border: none; 
            cursor: pointer; 
            margin: 10px 0; 
        }
        .btn-danger { background-color: #f44336; }
        .actions { margin: 10px 0; }
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
    </style>
    <script>
        function toggleAll(source) {
            var checkboxes = document.getElementsByName('record');
            for(var i=0; i<checkboxes.length; i++) {
                checkboxes[i].checked = source.checked;
            }
        }
        
        function deleteSelected() {
            if(!confirm('确定要删除选中的记录吗？此操作不可恢复！')) {
                return false;
            }
            
            var checkboxes = document.getElementsByName('record');
            var ids = [];
            
            for(var i=0; i<checkboxes.length; i++) {
                if(checkboxes[i].checked) {
                    ids.push(checkboxes[i].value);
                }
            }
            
            if(ids.length === 0) {
                alert('请至少选择一条记录！');
                return false;
            }
            
            document.getElementById('ids').value = ids.join(',');
            return true;
        }
    </script>
</head>
<body>
    <h1>财务报销钉钉费用列表</h1>
    
    <form method="post" onsubmit="return deleteSelected();">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="ids" id="ids">
        <div class="actions">
            <button type="submit" class="btn btn-danger">删除选中项</button>
        </div>
        
        <div class="table-container">
            <table border="1">
                <tr>
                    <th style="width:50px;"><input type="checkbox" onClick="toggleAll(this)"></th>
                    <th style="width:50px;">ID</th>
                    <th style="width:150px;">申请日期</th>
                    <th style="width:80px;">申请人</th>
                    <th style="width:100px;">申请部门</th>
                    <th style="width:100px;">归属公司</th>
                    <th style="width:80px;">金额</th>
                    <th style="width:150px;">标题</th>
                    <th>事由</th>
                    <th style="width:120px;">是否已报销</th>
                    <th style="width:120px;">钉钉审批编号</th>
                    <th style="width:150px;">添加时间</th>
                    <th style="width:150px;">核销时间</th>
                </tr>
                <% ' 循环输出数据
                Do While Not rsCheck.EOF
                    Response.Write "<tr>"& vbCrLf
                        Response.Write "<td><input type='checkbox' name='record' value='" & rsCheck("ID") & "'></td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("id") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("申请日期") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("申请人") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("申请部门") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("归属公司") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("金额") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("标题") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("事由") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("是否已报销") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("钉钉审批编号") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("添加时间") &"</td>"& vbCrLf
                        Response.Write "<td>"& rsCheck("核销时间") &"</td>"& vbCrLf
                    Response.Write "</tr>"& vbCrLf& vbCrLf
                    rsCheck.MoveNext
                Loop
                %>
            </table>
        </div>
    </form>
</body>
</html>
<% 
Else
    Response.Write "没有找到数据"
End If

' 关闭记录集
rsCheck.Close
Set rsCheck = Nothing
%>