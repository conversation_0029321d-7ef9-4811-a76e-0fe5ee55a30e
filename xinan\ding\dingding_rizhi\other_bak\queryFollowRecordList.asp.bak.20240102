<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../lib/config.asp"-->
<!--#include file="../lib/dingdingutil.asp"-->
<!--#include file="../lib/functions.asp"-->
<!--#include file="../../shujuku.asp"-->

<%
	'功能：
	'	 根据指定条件查询跟进记录数据
	'参数：
	'    page_size           分页大小
	'    cursor				 分页游标
	'返回：
	'	 errcode	            返回码
	'    errmsg	                对返回码的文本描述内容
	'	 result                 跟进记录详情，具体每个字段含义参考：https://open.dingtalk.com/document/orgapp/query-and-dingtalk-data-of-track-records-in-apsara-stack
	Response.Charset="utf-8"
	Response.ContentType="application/json"
	Set rstJSON = New aspJSON
	With rstJSON.data
        .Add "errcode", 0
		.Add "errmsg", "success"
        .Add "list", rstJSON.Collection()
	End With
	'on error resume next 

	'查询参数
	dim startTime,endTime,userName,userId,name_pinYin,query_dsl
	startTime = request.form("startTime")'开始时间
	endTime = request.form("endTime")'结束时间
	userName = request.form("userName")'跟进记录人姓名
	userId = ""
	query_dsl = ""

	'获取access_token
	dim access_token 
	access_token = GetAccessToken(corpId,corpSecret,appkey,appsecret)
	LogMessage "access_token -> " & access_token
	if userName <> "" then
		'通过名称将数据库中的用户钉钉id
		sql="select dingding_id,mingzhi,name from user2011 where mingzhi='"&userName&"'"
		rs2.open sql,conn2,1,2
		if not rs2.eof then
		userid_oa=rs2(0)
		username_oa=rs2(1)
		name_pinYin=rs2(2)
		end if
		rs2.close
		userId = userid_oa
		'response.write(userid)
	end if 
	
	
	startTime = int(ToUnixTime(startTime,+8))*1000
	endTime = int(ToUnixTime(endTime,+8))*1000+86400000 '+86400000的原因是钉钉不查询当天的日报,需要输入明天的日期才能查看今天

	'封装条件查询dsl参数
	query_dsl = "{""queryGroupList"":[{""logicType"":""AND"",""queryObjectList"":[{""filterType"":""IN"",""value"":["""& userId &"""],""fieldId"":""principal""},{""filterType"":""BETWEEN"",""value"":[""" & startTime & """,""" & endTime & """],""fieldId"":""gmt_create""}]}],""order"":""DESC"",""orderFieldId"":""gmt_create""}"
	LogMessage "query_dsl -> " & query_dsl


	'批量获取钉钉跟进记录,分页查询
	dim has_more, next_cursor, insIndex
	has_more = true
	next_cursor = 0
	insIndex = 0
	do while has_more
		Set params = New aspJSON ' 把 params 定义为一个 JSON 格式，把前端页面传过来的几个参数按照钉钉开发文档要求的格式进行梳理然后提交给钉钉云端查询'
		params.data.Add "cursor", next_cursor
		params.data.Add "page_size", 100
		params.data.Add "query_dsl", query_dsl
		if errcode = 0 then '成功
			Set FollowrecordQueryResult = FollowrecordQuery(params, access_token) ' FollowrecordQuery 函数就是 dingdingutil.asp 页面里面向钉钉 post 获取跟进记录的函数'
			errcode = FollowrecordQueryResult.data("errcode")
			next_cursor = FollowrecordQueryResult.data("result").item("next_cursor") ' 下一页起始 id
			has_more = FollowrecordQueryResult.data("result").item("has_more") ' 判断是否有下一页
			FollowrecordQueryResult.data("result").item("has_more")
			set instanceList = FollowrecordQueryResult.data("result").item("values")
			if instanceList.count > 0 then
				For Each phonenr In instanceList
					With rstJSON.data("list")
						.Add insIndex, instanceList(phonenr)
					end With
					insIndex = insIndex + 1
				Next
			end if
		else
			Exit Do
		end if
	loop
	Response.Write rstJSON.JSONoutput() 
%>