<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../shujuku.asp"-->
<!--#include file="md5.asp"-->
<%
' 设置响应头
Response.CharSet = "utf-8"
Session.CodePage = 65001
Response.ContentType = "application/json"

Set objMD5 = new MD5

''md5加密
'response.write objMD5.MakeDigest("123456")
'response.end

' 定义返回结果函数
Function JsonResponse(success, message)
    Dim json
    json = "{""success"":" & LCase(success) & ",""message"":""" & message & """}"
    JsonResponse = json
End Function

' 共用日志记录方法
' 定义日志文件路径

' 增强日志记录方法
Sub LogMessage(message)
    ' 文件日志记录
    Dim fso, logFile, logPath
    logPath = Server.MapPath("api_log.txt")
    Set fso = Server.CreateObject("Scripting.FileSystemObject")
    
    'On Error Resume Next
    ' 修改为相对路径确保权限
    Set logFile = fso.OpenTextFile(logPath, 8, True)
    If Err.Number = 0 Then
        logFile.WriteLine Now() & " " & message
        logFile.Close
    End If
    On Error GoTo 0
    
    Set logFile = Nothing
    Set fso = Nothing
End Sub

' 检查必填参数
Function CheckRequiredParams(params)
    Dim param
    CheckRequiredParams = True
    For Each param in params
        If Request.Form(param) = "" Then
            CheckRequiredParams = False
            Exit Function
        End If
    Next
End Function

Function URLDecode(ByVal str)
    Dim i, c, out, hex
    out = ""
    i = 1
    Do While i <= Len(str)
        c = Mid(str, i, 1)
        If c = "+" Then
            out = out & " "
        ElseIf c = "%" And i + 2 <= Len(str) Then
            hex = Mid(str, i + 1, 2)
            If IsNumeric("&H" & hex) Then
                out = out & Chr(CLng("&H" & hex))
                i = i + 2
            Else
                out = out & c
            End If
        Else
            out = out & c
        End If
        i = i + 1
    Loop
    URLDecode = out
End Function


' 防止SQL注入
Function SafeSQL(str)
    If IsNull(str) Then
        SafeSQL = NULL
    Else
        'SafeSQL = str
        SafeSQL = "'" & Replace(str, "'", "''") & "'"
    End If
End Function

' 生成签名
Function GenerateSign(paramsDict, secretKey)
    ' 参数按字典序排序
    Dim keys, i, sortedParams, signStr
    keys = paramsDict.Keys
    
    ' 对参数名进行排序
    For i = 0 To paramsDict.Count - 1
        For j = i + 1 To paramsDict.Count - 1
            If StrComp(keys(i), keys(j), vbTextCompare) > 0 Then
                Dim temp
                temp = keys(i)
                keys(i) = keys(j)
                keys(j) = temp
            End If
        Next
    Next
    
    ' 拼接参数值
    sortedParams = ""
    For Each key In keys
        If sortedParams <> "" Then
            sortedParams = sortedParams & "&"
        End If
        sortedParams = sortedParams & key & "=" & (paramsDict(key))
    Next
    
    ' 拼接密钥并MD5加密
    signStr = sortedParams & "&key=" & secretKey
    LogMessage "signStr:" & signStr
    GenerateSign = objMD5.MakeDigest(signStr)
    LogMessage "GenerateSign:" & GenerateSign
End Function


'response.write objMD5.MakeDigest("a=中国&action=debug&b=2&key=1A833DA63A6B7E20098DAE06D06602E1")
'response.end


' 验证签名
Function VerifySign(paramsDict, secretKey, sign)
    Dim generatedSign
    generatedSign = GenerateSign(paramsDict, secretKey)
    VerifySign = (StrComp(generatedSign, sign, vbTextCompare) = 0)
End Function

' 检查个人信息ID是否存在
Function CheckPersonExists(person_id)
    Dim rs, sql
    CheckPersonExists = False
    
    If Not IsNumeric(person_id) Then Exit Function
    
    sql = "SELECT COUNT(*) AS cnt FROM 认证培训之个人信息表 WHERE ID = " & CLng(person_id)
    Set rs = conn.Execute(sql)
    
    If Not rs.EOF Then
        CheckPersonExists = (rs("cnt") > 0)
    End If
    
    rs.Close
    Set rs = Nothing
End Function

' 获取最后插入的ID
Function GetLastInsertID()
    Dim rs, lastID
    Set rs = conn.Execute("SELECT @@IDENTITY AS LastID")
    If Not rs.EOF Then
        lastID = rs("LastID")
    Else
        lastID = 0
    End If
    rs.Close
    Set rs = Nothing
    GetLastInsertID = lastID
End Function

' 处理请求
If Request.ServerVariables("REQUEST_METHOD") <> "POST" Then
    Response.Write JsonResponse(False, "仅支持POST请求方式")
    Response.End
End If

' 定义密钥
Const SECRET_KEY = "1A833DA63A6B7E20098DAE06D06602E1"

' 签名验证
Dim paramsDict, sign
Set paramsDict = Server.CreateObject("Scripting.Dictionary")

' 收集所有请求参数
For Each key In Request.Form
    paramsDict.Add key, Request.Form(key)
    LogMessage "key:" & key & "|value:" & Request.Form(key)
Next

' 检查是否包含签名参数
If Not paramsDict.Exists("sign") Then
    Response.Write JsonResponse(False, "缺少签名参数")
    Response.End
End If

sign = paramsDict("sign")
paramsDict.Remove("sign") ' 移除签名参数本身

dim serverSign
serverSign = GenerateSign(paramsDict, SECRET_KEY)

LogMessage "sign:" & sign
LogMessage "serverSign:" & serverSign

' 验证签名
'If Not VerifySign(paramsDict, SECRET_KEY, sign) Then
'    'Response.Write JsonResponse(False, "签名验证失败[Client:" & sign & "]" & "[Server:" & serverSign & "]")
'    Response.Write JsonResponse(False, "签名验证失败")
'    Response.End
'End If

If Request.Form("sign") <> SECRET_KEY Then
    Response.Write JsonResponse(False, "签名验证失败")
    Response.End
End If


' 获取操作类型
Dim action
action = Request.Form("action")

LogMessage "action:" & action

' 根据操作类型执行不同的逻辑
Select Case action
    Case "register_training"
        ' 添加培训报名
        Call RegisterTraining()
    Case "add_personal_info"
        ' 添加个人信息
        Call AddPersonalInfo()
    Case "add_education"
        ' 添加文化程度信息
        'Call AddEducation()
    Case "add_certificate"
        ' 添加培训证书信息
        'Call AddCertificate()
    Case "update_personal_docs"
        ' 更新个人资料文档
        Call UpdatePersonalDocs()
    Case "add_work_experience"
        ' 添加工作经历
        'Call AddWorkExperience()
    Case "debug"
        Response.Write JsonResponse(True, "API请求成功")
        Response.End    
    Case Else
        Response.Write JsonResponse(False, "无效的操作类型")
        Response.End
End Select

' 添加个人信息
Sub AddPersonalInfo()
    ' 定义必填参数
    Dim requiredParams
    requiredParams = Array("name", "address", "mobile", "email", "create_time")
    
    ' 检查必填参数
    If Not CheckRequiredParams(requiredParams) Then
        Response.Write JsonResponse(False, "必填项不能为空")
        Response.End
    End If
    
    ' 获取表单参数
    Dim name, address, mobile, email, create_time
    
    name = Request.Form("name")
    address = Request.Form("address")
    mobile = Request.Form("mobile")
    email = Request.Form("email")
    create_time = Request.Form("create_time")
    
    ' 构建SQL语句
    Dim sql
    sql = "INSERT INTO 认证培训之个人信息表 (姓名, 通信地址, 手机号码, 电子邮箱, 创建时间) VALUES (" & _
          SafeSQL(name) & ", " & _
          SafeSQL(address) & ", " & _
          SafeSQL(mobile) & ", " & _
          SafeSQL(email) & ", " & _
          SafeSQL(create_time) & ")"
    
    ' 执行SQL语句
    'On Error Resume Next
    conn.Execute sql
    
    If Err.Number <> 0 Then
        Call LogMessage("个人信息添加失败: " & Err.Description)
        Response.Write JsonResponse(False, "个人信息添加失败：" & Err.Description)
    Else
        ' 获取最后插入的ID
        Dim lastID
        lastID = GetLastInsertID()
        
        Call LogMessage("个人信息添加成功，用户ID：" & lastID)
        Response.Write JsonResponse(True, "个人信息添加成功" & "|" & lastID)
    End If
End Sub

' 获取最后插入的ID
Function GetLastInsertID()
    Dim rs, sql
    sql = "SELECT @@IDENTITY AS LastID"
    Set rs = conn.Execute(sql)
    If Not rs.EOF Then
        GetLastInsertID = rs("LastID")
    Else
        GetLastInsertID = 0
    End If
    rs.Close
    Set rs = Nothing
End Function

' 添加文化程度信息
Sub AddEducation()
    ' 定义必填参数
    Dim requiredParams
    requiredParams = Array("person_id")
    
    ' 检查必填参数
    If Not CheckRequiredParams(requiredParams) Then
        Response.Write JsonResponse(False, "个人信息ID不能为空")
        Response.End
    End If
    
    ' 获取个人信息ID并验证是否存在
    Dim person_id
    person_id = Request.Form("person_id")
    
    If Not CheckPersonExists(person_id) Then
        Response.Write JsonResponse(False, "个人信息ID不存在")
        Response.End
    End If
    
    ' 检查是否有多条记录
    Dim recordCount, i
    recordCount = Request.Form("record_count")
    
    ' 如果没有指定记录数量，默认为1
    If recordCount = "" Then
        recordCount = 1
    End If
    
    ' 处理每条记录
    Dim successCount, errorMessages
    successCount = 0
    errorMessages = ""
    
    For i = 1 To CInt(recordCount)
        Dim suffix, education_time, school, education_level, education_major
        
        ' 如果是多条记录，参数名称会带有索引后缀
        If CInt(recordCount) > 1 Then
            suffix = "_" & i
        Else
            suffix = ""
        End If
        
        ' 获取当前记录的参数
        education_time = Request.Form("education_time" & suffix)
        school = Request.Form("school" & suffix)
        education_level = Request.Form("education_level" & suffix)
        education_major = Request.Form("education_major" & suffix)
        
        ' 检查当前记录的必填参数
        If education_time <> "" And school <> "" And education_level <> "" Then
            ' 构建SQL语句
            Dim sql
            sql = "INSERT INTO 认证培训之文化程度表 (个人信息ID, 时间, 毕业学校, 学历, 专业) VALUES (" & _
                  SafeSQL(person_id) & ", " & _
                  SafeSQL(education_time) & ", " & _
                  SafeSQL(school) & ", " & _
                  SafeSQL(education_level) & ", " & _
                  SafeSQL(education_major) & ")"
            
            ' 执行SQL语句
            'On Error Resume Next
            conn.Execute sql
            
            If Err.Number <> 0 Then
                errorMessages = errorMessages & "记录" & i & "添加失败：" & Err.Description & "; "
            Else
                successCount = successCount + 1
            End If
            On Error GoTo 0
        Else
            ' 如果是多条记录模式且参数不完整，则跳过该记录
            If CInt(recordCount) > 1 Then
                ' 如果有部分参数但不完整，记录错误
                If education_time <> "" Or school <> "" Or education_level <> "" Then
                    errorMessages = errorMessages & "记录" & i & "参数不完整，已跳过; "
                End If
                ' 否则静默跳过（可能是空记录）
            Else
                ' 单条记录模式下，必填参数不完整则报错
                Response.Write JsonResponse(False, "必填项不能为空：时间、毕业学校、学历")
                Response.End
            End If
        End If
    Next
    
    ' 返回结果
    If successCount > 0 Then
        Dim message
        message = "成功添加" & successCount & "条文化程度记录"
        If errorMessages <> "" Then
            message = message & "，但有以下错误：" & errorMessages
        End If
        Response.Write JsonResponse(True, message)
    Else
        Response.Write JsonResponse(False, "未能添加任何文化程度记录：" & errorMessages)
    End If
End Sub

' 添加培训证书信息
Sub AddCertificate()
    ' 定义必填参数
    Dim requiredParams
    requiredParams = Array("person_id")
    
    ' 检查必填参数
    If Not CheckRequiredParams(requiredParams) Then
        Response.Write JsonResponse(False, "个人信息ID不能为空")
        Response.End
    End If
    
    ' 获取个人信息ID并验证是否存在
    Dim person_id
    person_id = Request.Form("person_id")
    
    If Not CheckPersonExists(person_id) Then
        Response.Write JsonResponse(False, "个人信息ID不存在")
        Response.End
    End If
    
    ' 检查是否有多条记录
    Dim recordCount, i
    recordCount = Request.Form("record_count")
    
    ' 如果没有指定记录数量，默认为1
    If recordCount = "" Then
        recordCount = 1
    End If
    
    ' 处理每条记录
    Dim successCount, errorMessages
    successCount = 0
    errorMessages = ""
    
    For i = 1 To CInt(recordCount)
        Dim suffix, certificate_name, issuing_organization, certificate_number, certificate_file, issue_date
        
        ' 如果是多条记录，参数名称会带有索引后缀
        If CInt(recordCount) > 1 Then
            suffix = "_" & i
        Else
            suffix = ""
        End If
        
        ' 获取当前记录的参数
        certificate_name = Request.Form("certificate_name" & suffix)
        issuing_organization = Request.Form("issuing_organization" & suffix)
        certificate_number = Request.Form("certificate_number" & suffix)
        certificate_file = Request.Form("certificate_file" & suffix)
        issue_date = Request.Form("issue_date" & suffix)
        
        ' 检查当前记录的必填参数
        If certificate_name <> "" And issuing_organization <> "" And certificate_file <> "" Then
            ' 构建SQL语句
            Dim sql
            sql = "INSERT INTO 认证培训之培训证书表 (个人信息ID, 证书名称, 发证机构, 证书编号, 证书文件, 获证日期) VALUES (" & _
                  SafeSQL(person_id) & ", " & _
                  SafeSQL(certificate_name) & ", " & _
                  SafeSQL(issuing_organization) & ", " & _
                  SafeSQL(certificate_number) & ", " & _
                  SafeSQL(certificate_file) & ", " & _
                  SafeSQL(issue_date) & ")"
            
            ' 执行SQL语句
            'On Error Resume Next
            conn.Execute sql
            
            If Err.Number <> 0 Then
                errorMessages = errorMessages & "记录" & i & "添加失败：" & Err.Description & "; "
            Else
                successCount = successCount + 1
            End If
            On Error GoTo 0
        Else
            ' 如果是多条记录模式且参数不完整，则跳过该记录
            If CInt(recordCount) > 1 Then
                ' 如果有部分参数但不完整，记录错误
                If certificate_name <> "" Or issuing_organization <> "" Then
                    errorMessages = errorMessages & "记录" & i & "参数不完整，已跳过; "
                End If
                ' 否则静默跳过（可能是空记录）
            Else
                ' 单条记录模式下，必填参数不完整则报错
                Response.Write JsonResponse(False, "必填项不能为空：证书名称、发证机构、证书文件")
                Response.End
            End If
        End If
    Next
    
    ' 返回结果
    If successCount > 0 Then
        Dim message
        message = "成功添加" & successCount & "条培训证书记录"
        If errorMessages <> "" Then
            message = message & "，但有以下错误：" & errorMessages
        End If
        Response.Write JsonResponse(True, message)
    Else
        Response.Write JsonResponse(False, "未能添加任何培训证书记录：" & errorMessages)
    End If
End Sub

' 更新个人资料文档
Sub UpdatePersonalDocs()
    ' 定义必填参数
    Dim requiredParams
    requiredParams = Array("person_id")
    
    ' 检查必填参数
    If Not CheckRequiredParams(requiredParams) Then
        Response.Write JsonResponse(False, "个人信息ID不能为空")
        Response.End
    End If
    
    ' 获取个人信息ID
    Dim person_id
    person_id = Request.Form("person_id")
    
    If Not CheckPersonExists(person_id) Then
        Response.Write JsonResponse(False, "个人信息ID不存在")
        Response.End
    End If
    
    ' 获取表单参数
    Dim photo, id_card_front, id_card_back, certification_info
    
    photo = Request.Form("photo")
    id_card_front = Request.Form("id_card_front")
    id_card_back = Request.Form("id_card_back")
    certification_info = Request.Form("certification_info")
    
    ' 检查是否有至少一个字段需要更新
    If photo = "" And id_card_front = "" And id_card_back = "" And certification_info = "" Then
        Response.Write JsonResponse(False, "至少需要提供一个要更新的字段")
        Response.End
    End If
    
    ' 构建SQL语句
    Dim sql, updateFields, hasUpdate
    updateFields = ""
    hasUpdate = False
    
    ' 添加电子版照片字段
    If photo <> "" Then
        If hasUpdate Then updateFields = updateFields & ", "
        updateFields = updateFields & "电子版照片 = " & SafeSQL(photo)
        hasUpdate = True
    End If

    ' 添加身份证正面字段
    If id_card_front <> "" Then
        If hasUpdate Then updateFields = updateFields & ", "
        updateFields = updateFields & "身份证正面 = " & SafeSQL(id_card_front)
        hasUpdate = True
    End If

    ' 添加身份证反面字段
    If id_card_back <> "" Then
        If hasUpdate Then updateFields = updateFields & ", "
        updateFields = updateFields & "身份证反面 = " & SafeSQL(id_card_back)
        hasUpdate = True
    End If

    ' 添加证书信息附件字段
    If certification_info <> "" Then
        If hasUpdate Then updateFields = updateFields & ", "
        updateFields = updateFields & "证书信息附件 = " & SafeSQL(certification_info)
        hasUpdate = True
    End If
    
    ' 添加更新时间字段
    If hasUpdate Then
        updateFields = updateFields & ", 更新时间 = '" & Now() & "'"
    End If
    
    ' 构建完整的SQL语句
    sql = "UPDATE 认证培训之个人信息表 SET " & updateFields & " WHERE ID = " & CLng(person_id)
    LogMessage sql
    
    ' 执行SQL语句
    'On Error Resume Next
    conn.Execute sql
    
    If Err.Number <> 0 Then
        Response.Write JsonResponse(False, "个人资料文档更新失败：" & Err.Description)
    Else
        Response.Write JsonResponse(True, "个人资料文档更新成功")
    End If
End Sub

' 培训报名
Sub RegisterTraining()
    ' 定义必填参数
    Dim requiredParams
    requiredParams = Array("name", "name_pinyin", "sex", "unit_name", "id_card", "training_type", "mobile", "plan_month", "plan_city", "exam_status")
    
    ' 检查必填参数
    If Not CheckRequiredParams(requiredParams) Then
        Response.Write JsonResponse(False, "必填项不能为空")
        Response.End
    End If
    
    ' 获取表单数据
    Dim name, sex, mobile, unit_name, id_card, training_type, plan_month, plan_city, exam_status
    
    name = Request.Form("name")
    name_pinyin = Request.Form("name_pinyin")
    sex = Request.Form("sex")
    id_card = Request.Form("id_card")
    mobile = Request.Form("mobile")
    unit_name = Request.Form("unit_name")
    training_type = Request.Form("training_type")
    plan_month = Request.Form("plan_month") 
    plan_city = Request.Form("plan_city") 
    exam_status = Request.Form("exam_status")
    is_delete = 0
    create_time = Request.Form("create_time")

    ' 检查是否已报名
    checkSql = "SELECT COUNT(*) FROM 认证培训之培训报名表 WHERE [是否删除] = 0 And 身份证号 = " & SafeSQL(id_card)
    Set rs = conn.Execute(checkSql)
    If rs(0) > 0 Then
        Response.Write JsonResponse(False, "该身份证号已报名，请勿重复提交")
        Response.End
    End If
    
    ' 构建SQL语句
    sql = "INSERT INTO [认证培训之培训报名表] ([姓名], [姓名拼音], [性别], [身份证号], [手机号码], [单位], [报考方向], [意向考试月份], [意向考试城市], [考试状态], [是否删除], [创建时间]) VALUES (" & _
       SafeSQL(name) & ", " & _
       SafeSQL(name_pinyin) & ", " & _
       SafeSQL(sex) & ", " & _
       SafeSQL(id_card) & ", " & _
       SafeSQL(mobile) & ", " & _
       SafeSQL(unit_name) & ", " & _
       SafeSQL(training_type) & ", " & _
       SafeSQL(plan_month) & ", " & _
       SafeSQL(plan_city) & ", " & _
       SafeSQL(exam_status) & ", " & _
       is_delete & ", " & _
       SafeSQL(create_time) & ")"
       
    LogMessage sql
    
    ' 执行SQL语句
    'On Error Resume Next
    conn.Execute sql
    
    If Err.Number <> 0 Then
        Response.Write JsonResponse(False, "培训报名失败：" & Err.Description)
    Else
        Response.Write JsonResponse(True, "培训报名成功")
    End If
    On Error GoTo 0
End Sub

' 添加工作经历
Sub AddWorkExperience()
    ' 定义必填参数
    Dim requiredParams
    requiredParams = Array("person_id")
    
    ' 检查必填参数
    If Not CheckRequiredParams(requiredParams) Then
        Response.Write JsonResponse(False, "个人信息ID不能为空")
        Response.End
    End If
    
    ' 获取个人信息ID并验证是否存在
    Dim person_id
    person_id = Request.Form("person_id")
    
    If Not CheckPersonExists(person_id) Then
        Response.Write JsonResponse(False, "个人信息ID不存在")
        Response.End
    End If
    
    ' 检查是否有多条记录
    Dim recordCount, i
    recordCount = Request.Form("record_count")
    
    ' 如果没有指定记录数量，默认为1
    If recordCount = "" Then
        recordCount = 1
    End If
    
    ' 处理每条记录
    Dim successCount, errorMessages
    successCount = 0
    errorMessages = ""
    
    For i = 1 To CInt(recordCount)
        Dim suffix, start_date, end_date, company, position, main_duties, witness, is_security_related
        
        ' 如果是多条记录，参数名称会带有索引后缀
        If CInt(recordCount) > 1 Then
            suffix = "_" & i
        Else
            suffix = ""
        End If
        
        ' 获取当前记录的参数
        start_date = Request.Form("start_date" & suffix)
        end_date = Request.Form("end_date" & suffix)
        company = Request.Form("company" & suffix)
        position = Request.Form("position" & suffix)
        main_duties = Request.Form("main_duties" & suffix)
        witness = Request.Form("witness" & suffix)
        is_security_related = Request.Form("is_security_related" & suffix)
        
        ' 处理是否与信息安全相关的默认值
        If is_security_related = "1" Then
            is_security_related = "1"
        Else
            is_security_related = "0"
        End If
        
        ' 检查当前记录的必填参数
        If start_date <> "" And end_date <> "" And company <> "" And position <> "" And main_duties <> "" And witness <> "" And is_security_related <> "" Then
            ' 构建SQL语句
            Dim sql
            sql = "INSERT INTO 认证培训之工作经历 (起止日期, 结束日期, 工作单位, 职务, 主要职责, 证明人, 是否安全相关, 个人信息ID) VALUES (" & _
                  SafeSQL(start_date) & ", " & _
                  SafeSQL(end_date) & ", " & _
                  SafeSQL(company) & ", " & _
                  SafeSQL(position) & ", " & _
                  SafeSQL(main_duties) & ", " & _
                  SafeSQL(witness) & ", " & _
                  is_security_related & ", " & _
                  SafeSQL(person_id) & ")"
            
            ' 执行SQL语句
            On Error Resume Next
            conn.Execute sql
            
            If Err.Number <> 0 Then
                errorMessages = errorMessages & "记录" & i & "添加失败：" & Err.Description & "; "
            Else
                successCount = successCount + 1
            End If
            On Error GoTo 0
        Else
            ' 如果是多条记录模式且参数不完整，则跳过该记录
            If CInt(recordCount) > 1 Then
                ' 如果有部分参数但不完整，记录错误
                If start_date <> "" And end_date <> "" And company <> "" And position <> "" And main_duties <> "" And witness <> "" And is_security_related <> "" Then
                    errorMessages = errorMessages & "记录" & i & "参数不完整，已跳过; "
                End If
                ' 否则静默跳过（可能是空记录）
            Else
                ' 单条记录模式下，必填参数不完整则报错
                Response.Write JsonResponse(False, "必填项不能为空：起止日期、结束日期、工作单位、职务")
                Response.End
            End If
        End If
    Next
    
    ' 返回结果
    If successCount > 0 Then
        Dim message
        message = "成功添加" & successCount & "条工作经历记录"
        If errorMessages <> "" Then
            message = message & "，但有以下错误：" & errorMessages
        End If
        Response.Write JsonResponse(True, message)
    Else
        Response.Write JsonResponse(False, "未能添加任何工作经历记录：" & errorMessages)
    End If
End Sub

conn.Close
Set conn = Nothing
%>