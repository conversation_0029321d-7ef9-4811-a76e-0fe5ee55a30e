<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->

<%
Response.Charset="utf-8"
Response.ContentType="application/json"


Set rstJSON = New aspJSON
With rstJSON.data
    .Add "errcode", 0
    .Add "errmsg", "success"
    .Add "list", rstJSON.Collection()
End With

Dim resultFlag

'查询参数
Dim startTime,endTime
startTime = request.querystring("startTime")'开始时间
endTime = request.querystring("endTime")'结束时间
userId = ""
query_dsl = ""

Dim offsetDays
days = request.querystring("days")
if days = "" then
    offsetDays = 2
else
    offsetDays = days
    LogMessage "offsetDays -> " & days
end if

if startTime = "" or endTime = "" then
    endTime = Date()
    startTime = DateAdd("d", -(offsetDays), endTime)
end if
LogMessage "startTime -> " & startTime & " endTime -> " & endTime

'判断日期参数是否正确,2024/1/3,待更新上线
Dim currentDate
currentDate = Date
date1 = CDate(startTime)
date2 = CDate(endTime)

Dim diff
diff1 = DateDiff("d", date1, currentDate)
diff2 = DateDiff("d", date2, currentDate)
if diff1 > offsetDays or diff1 < 0 or diff2 > offsetDays or diff2 < 0 then
    response.write "参数有误,最多支持近"& offsetDays &"天," & "开始日期距今 " & diff1 & " 天,结束日期距今 "  & diff2 & " 天"
    response.end
end if

'获取access_token
Dim access_token
access_token = GetAccessToken("","",appkey,appsecret)
LogMessage "WorkRecord access_token -> " & access_token

startTime = int(ToUnixTime(startTime,+8))*1000
endTime = int(ToUnixTime(endTime,+8))*1000+86400000 '+86400000的原因是钉钉不查询当天的日报,需要输入明天的日期才能查看今天

Dim successNum,existNum
successNum = 0
existNum = 0

''''''''''''''''''''''''''''''''''''''''''''''''
'''''''''''''''以下开始查询单位信息'''''''''''''
''''''''''''''''''''''''''''''''''''''''''''''''
'' ***** 1、首先查询数据库已有的跟进记录数据 *****
'' ***** 2、根据数据库中已有的跟进记录数据查询关联的客户单位 *****

''1、首先查询数据库已有的跟进记录数据
startTime = time2Date(startTime)
endTime = time2Date(endTime)
sql = "select id,data,extend_data from saler_work_record where gmt_create BETWEEN #"&startTime&"# AND #"&endTime&"# order by id desc "
LogMessage "salerWorkRecord query local db, sql -> " & sql


rs.open sql,conn,1,2
Dim idx
idx = 0
Do While Not rs.EOF
    dingId = rs("creator_userid")

    sql2="SELECT [mingzhi] FROM [user2011] where [dingding_id] = '" & dingId & "'"
    rs.open sql2,conn,1,2
    if not rs.eof then
        nickname = rs(0)
    end if

    data = rs("data")
    'response.write data
	data = formatStr2Json(data)
	
	''{"follow_record_related_customer":{"extendValue":{"quote":1,"list":[{"instanceId":"LZKtChX7RZatYL5anUQ0Qw00121711009374"}]},"value":["河南检察职业学院(国家检察官学院河南分院)"]},"follow_record_related_contact":{"extendValue":{"quote":1,"list":[{"instanceId":"gzAZlWesSsm1VebOJRC08g00121711009375"}]},"value":["杨艳华"]},"DDDateField_1J06OARGJX0G0":1730217600000,"DDSelectField-K2U5UJAC":{"extendValue":{"label":"当面拜访","key":"option_K2U5VJBL"},"value":"当面拜访"},"follow_record_related_content":"和杨老师做了sop阶段性汇报，杨老师表示目前感觉我们的sop运营已经给学院做到了很大的提升，同时针对她的工作也提供了很多的帮助，但是希望后续我们能持续的帮助他们做好网络安全检测，以及加大对学院网络安全建设的建议，另外一块杨老师说最近教育厅连续下发了好几个文要求各高校最近一段时间加强网络安全防护，以及对自身漏洞的排查，因为他们得到消息说目前国外有个匿名者组织宣称最近要对中国高校进行大范围网络攻击@范亚辉@刘鹏飞@齐文振","TextareaField-KG8KCDA6":"跟进整体安全服务推进情况"}
	
	Set jsonData = New aspJSON
	jsonData.loadJSON(data)
	
    'response.write jsonData.data("follow_record_related_content")
	instanceId = jsonData.data("follow_record_related_customer").item("extendValue").item("list").item(0).item("instanceId")
	companyName = jsonData.data("follow_record_related_customer").item("value").item(0)
	'response.write instanceId
	'response.write companyName
    'response.end

    ''2、根据数据库中已有的跟进记录数据查询关联的客户单位
	call insertCompany(companyName, instanceId, nickname)
	
    rs.MoveNext
    idx = idx + 1
Loop
rs.close
Set rs = Nothing

'插入单位信息
Function insertCompany(companyName, instanceId, nickname)
    sql1 = "SELECT [instance_id] FROM [crm_customer_personal] where [instance_id]='" & instanceId & "'"
	'response.write sql1
	
	Dim rs1
    Set rs1 = Server.CreateObject("ADODB.Recordset")
    rs1.Open sql1, conn, 1, 2
    iRowCount = rs1.recordCount
	'response.write iRowCount
	'response.end
    if iRowCount = 0 then
        sql2 = "insert into [crm_customer_personal]([instance_id],[customer_name],[created_at],[sales]) values (" & _
        "'" & instanceId & "','" & companyName & "','" & now() & "','" & nickname & "')"
        LogMessage "CrmCustomer insert sql -> " & sql
        'response.write sql
        'response.end
        conn.Execute(sql2)
    end if
	
	rs1.Close
    Set rs1 = Nothing
End Function 
















''===========================================================

'封装条件查询dsl参数
query_dsl = "{""queryGroupList"":[{""logicType"":""AND"",""queryObjectList"":[{""filterType"":""BETWEEN"",""value"":[""" & startTime & """,""" & endTime & """],""fieldId"":""gmt_create""}]}],""order"":""DESC"",""orderFieldId"":""gmt_create""}"
LogMessage "query_dsl -> " & query_dsl

'批量获取钉钉跟进记录,分页查询
Dim has_more, next_cursor, insIndex
has_more = true
next_cursor = 0
insIndex = 0
do while has_more
    '默认为false,防止后面逻辑错误死循环
    has_more = false
    Set params = New aspJSON ' 把 params 定义为一个 JSON 格式，把前端页面传过来的几个参数按照钉钉开发文档要求的格式进行梳理然后提交给钉钉云端查询'
    params.data.Add "cursor", next_cursor
    params.data.Add "page_size", 100
    params.data.Add "query_dsl", query_dsl

    Set FollowrecordQueryResult = FollowrecordQuery(params, access_token)

    dim errcode,errmsg
    errcode = FollowrecordQueryResult.data("errcode")

    '查询成功
    if errcode = 0 then
        resultFlag = true
        next_cursor = FollowrecordQueryResult.data("result").item("next_cursor") ' 下一页起始 id
        has_more = FollowrecordQueryResult.data("result").item("has_more") ' 判断是否有下一页
        set instanceList = FollowrecordQueryResult.data("result").item("values")
        if instanceList.count > 0 then
            For Each idx In instanceList
                'response.write TypeName(instanceList)
                Set item = instanceList.item(idx)

                Dim creator_userid,data,extend_data,gmt_create,gmt_modified,instance_id,object_type,proc_inst_status,proc_out_result
                creator_userid = item("creator_userid")
                data = item("data")
                extend_data = item("extend_data")
                gmt_create = item("gmt_create")
                gmt_modified = item("gmt_modified")
                instance_id = item("instance_id")
                object_type = item("object_type")
                proc_inst_status = item("proc_inst_status")
                proc_out_result = item("proc_out_result")

                sql="SELECT [instance_id] FROM [saler_work_record] where [instance_id]='" & instance_id & "'"
                rs.open sql,conn,1,2
                iRowCount = rs.recordCount
                if iRowCount = 0 then
                    sql = "insert into [saler_work_record]([creator_userid],[data],[extend_data],[gmt_create],[gmt_modified],[instance_id],[object_type],[proc_inst_status],[proc_out_result],[Import_date],[status]) values (" & _
                    "'" & creator_userid & "','" & data & "','" & extend_data & "','" & gmt_create & _
                    "','" & gmt_modified & "','" & instance_id & _
                    "','" & object_type & "','" & proc_inst_status & _
                    "','" & proc_out_result & "','" & now() & "', 0)"
                    LogMessage "insert into [saler_work_record] -> " & sql
                    conn.Execute(sql)
                    successNum = successNum + 1
                else
                    'sql="delete  from [saler_work_record] where [instance_id]='"&instance_id&"' "
                    'conn.execute(sql)
                    existNum = existNum + 1
                end if
                rs.close

                'With rstJSON.data("list")
                '    .Add insIndex, instanceList(idx)
                'end With
                insIndex = insIndex + 1
            Next
        end if
    else
        errmsg = FollowrecordQueryResult.data("errmsg")
        LogMessage "errmsg -> " & errmsg
        resultFlag = errmsg
        Exit Do
    end if
loop
msg = "sync WorkRecord result,totalNum -> " & insIndex & " successNum -> " & successNum & " existNum -> " & existNum
LogMessage msg
if resultFlag = true then
    response.write "sync WorkRecord success -> " & msg & vbCrLf
else
    response.write "sync WorkRecord failed  -> " & msg & resultFlag & vbCrLf
end if

'''''''''''''''''''''''''''''''''''''''''''''''''''''
'''''''''''''''以下开始同步日报数据''''''''''''''''''''''
'''''''''''''''''''''''''''''''''''''''''''''''''''''

'将数据插入到数据库中
Function insertReport(report_id, dataItem)
    Dim successNum,existNum
    successNum = 0
    existNum = 0

    creator_id = dataItem("creator_id")
    creator_name = dataItem("creator_name")
    create_time = dataItem("create_time")
    create_time = DateAdd("s", create_time / 1000, #1970/01/01 00:00:00#)
    dept_name = dataItem("dept_name")
    modified_time = dataItem("modified_time")
    modified_time = DateAdd("s", modified_time / 1000, #1970/01/01 00:00:00#)
    remark = dataItem("remark")
    report_id = dataItem("report_id")
    template_name = dataItem("template_name")

    Set oJSON = New aspJSON
    With oJSON.data
        .Add "contents", oJSON.Collection()
        With oJSON.data("contents")
            For Each contentKey In dataItem("contents")
                .Add contentKey, oJSON.Collection()
                With .item(contentKey)
                    .Add "key", dataItem("contents").item(contentKey).item("key")
                    .Add "value", dataItem("contents").item(contentKey).item("value")
                End With
            Next
        End With
    End With
    contents = oJSON.JSONoutput()
    contents = Trim(Replace(Replace(contents, vbCrLf, " "), " ", ""))
    contents = Replace(contents, "'", """")
    contents = Mid(contents, 13, Len(contents) - 13)

    '清理数据
    'sql="delete  from [ding_talk_report] where [report_id]='"&report_id&"' "
    'conn.execute(sql)

    sql="SELECT [report_id] FROM [ding_talk_report] where [report_id]='" & report_id & "'"
    rs.open sql,conn,1,2
    iRowCount = rs.recordCount
    if iRowCount = 0 then
        sql = "insert into [ding_talk_report]([creator_id],[creator_name],[create_time],[dept_name],[modified_time],[remark],[report_id],[template_name],[contents],[import_date]) values (" & _
        "'" & creator_id & "','" & creator_name & "','" & create_time & "','" & dept_name & _
        "','" & modified_time & "','" & remark & _
        "','" & report_id & "','" & template_name & _
        "','" & contents & "','" & now() & "')"
        'LogMessage "dailyReport insert sql -> " & sql
        'response.write sql
        'response.end
        conn.Execute(sql)
        successNum = successNum + 1
    else
        existNum = existNum + 1
    end if
    rs.close
    insertReport = successNum& "," &existNum
End Function


access_token = GetAccessToken(corpId,corpSecret,appkey,appsecret)
LogMessage "dailyReport access_token -> " & access_token

successNum = 0
existNum = 0

has_more = true
next_cursor = 0
insIndex = 0
do while has_more
    has_more = false
    Set params = New aspJSON '把params定义为一个json格式,把前端页面传过来的几个参数按照钉钉开发文档要求的格式进行梳理 然后提交给钉钉云端查询'
    params.data.Add "cursor", next_cursor
    params.data.Add "size", 20
    params.data.Add "start_time", startTime
    params.data.Add "end_time", endTime
    'params.data.Add "template_name", templateName
    'params.data.Add "userid", userId
    LogMessage params.JSONOutput()
    Set logList = ListLogs(params,access_token)'ListLogs函数就是dingdingutil.asp页面里面向钉钉post获取日志的函数'
    'response.write logList.JSONOutput()
    'LogMessage logList.JSONOutput()
    'dim errcode,errmsg
    errcode = logList.data("errcode")
    if errcode = 0 then '成功
        resultFlag = true
        set dataList = logList.data("result").item("data_list") '取出日志列表
        next_cursor = logList.data("result").item("next_cursor") '下一页起始id
        has_more = logList.data("result").item("has_more") '判断是否有下一页
        'has_more = false
        if dataList.count > 0 then
            For Each idx In dataList
                Set dataItem = dataList.item(idx)

                ''''''''''''''''''''demo''''''''''''''''''''
                    '.Add "familyName", "Smith"
                    '.Add "familyMembers", oJSON.Collection()

                    'With oJSON.data("familyMembers")
                    '    .Add 0, oJSON.Collection()
                    '    With .item(0)
                    '        .Add "firstName", "John"
                    '        .Add "age", 41

                    '        .Add "job", oJSON.Collection()          'Create named object
                    '        With .item("job")
                    '            .Add "function", "Webdeveloper"
                    '            .Add "salary", 70000
                    '        End With
                    '    End With
                    'End With

                    'response.write oJSON.JSONOutput()
                ''''''''''''''''''''demo''''''''''''''''''''

                For Each key In dataItem

                    'contents = dataItem.item("contents")
                    'response.write contents

                    Set oJSON = New aspJSON
                    With oJSON.data
                        if key = "contents" then
                            .Add "contents", oJSON.Collection()
                            With oJSON.data("contents")
                                    For Each contentKey In dataItem("contents")
                                        .Add contentKey, oJSON.Collection()
                                        With .item(contentKey)
                                            .Add "key", dataItem("contents").item(contentKey).item("key")
                                            .Add "value", dataItem("contents").item(contentKey).item("value")
                                        End With

                                        'cotItem.data.Add dataItem("contents").item(contentKey).item("key"), dataItem("contents").item(contentKey).item("value")
                                        'report = dataItem("contents").item(contentKey).item("key") & ":" & dataItem("contents").item(contentKey).item("value")
                                        'response.write report & vbCrLf
                                        'response.end
                                    Next
                            End With
                        else
                            if not key = "images" then
                                oJSON.data.Add key, dataItem.item(key)
                            end if
                        end if
                    End With
                Next

                result = insertReport(report_id, dataItem)
                toalNum = Split(result, ",")
                successNum = successNum + toalNum(0)
                existNum = existNum + toalNum(1)
                insIndex = insIndex + 1
            Next
            LogMessage "successNum -> "& successNum & " existNum -> " & existNum
        end if
    else
        errmsg = logList.data("errmsg")
        LogMessage "errmsg -> " & errmsg
        resultFlag = errmsg
        Exit Do
    end if
loop

conn.close
msg = "sync DailyReport result,totalNum -> " & insIndex & " successNum -> " & successNum & " existNum -> " & existNum
LogMessage msg
if resultFlag = true then
    response.write "sync DailyReport success -> " & msg & vbCrLf
else
    response.write "sync DailyReport failed  -> " & msg & resultFlag & vbCrLf
end if
%>