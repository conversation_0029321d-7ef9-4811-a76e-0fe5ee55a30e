<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../lib/dingdingutil.asp"-->
<!--#include file="../lib/config.asp"-->
<!--#include file="../../shujuku.asp"-->

<%

	'功能：
	'	 查询钉钉日志记录
	'参数：
	'    startTime：			日志开始时间
	'	 endTime：			    日志结束时间
	'	 userName			    日志记录人姓名
	'	 templateName			日志类型
	'返回：
	'	 errcode	            返回码
	'    errmsg	                对返回码的文本描述内容
	'	 list                   日志详情列表，具体每个字段含义参考：https://ding-doc.dingtalk.com/doc#/serverapi2/yknhmg
	Response.Charset="utf-8"
	Response.ContentType="application/json"
	Set rstJSON = New aspJSON
	With rstJSON.data
    	.Add "errcode", 0
		.Add "errmsg", "success"
    	.Add "list", rstJSON.Collection()
	End With
	'on error resume next 
	'查询参数
	dim startTime,endTime,userName,templateName,userId,name_pinYin
	startTime = request.form("startTime")'开始时间
	endTime = request.form("endTime")'结束时间
	userName = request.form("userName")'日志记录人姓名
	'userName=session("mingzhi")
	templateName = request.form("templateName")'日志类型
	userId = ""

	'判断日期参数是否正确,2024/1/3,待更新上线
	Dim currentDate
	currentDate = Date
    date1 = CDate(startTime)
    date2 = CDate(endTime)
	Dim diff
    diff1 = DateDiff("d", date1, currentDate)
    diff2 = DateDiff("d", date2, currentDate)
	if diff1 > 90 or diff1 < 0 or diff2 > 90 or diff2 < 0 then
		response.write "参数有误,最多支持近90天," & "开始日期距今 " & diff1 & " 天,结束日期距今 "  & diff2 & " 天"
		response.end
	end if

	'获取access_token
	dim access_token 
	access_token = GetAccessToken(corpId,corpSecret,appkey,appsecret)
	'response.Write(access_token)
	'response.End()
	LogMessage "access_token -> " & access_token
	if userName <> "" then
		'通过名称将数据库中的用户钉钉id
		sql="select dingding_id,mingzhi,name from user2011 where mingzhi='"&userName&"'"
		rs2.open sql,conn2,1,2
		if not rs2.eof then
		userid_oa=rs2(0)
		username_oa=rs2(1)
		name_pinYin=rs2(2)
		LogMessage "userName -> "& userName & " userid_oa -> "&userid_oa
		end if
		rs2.close
		userId = userid_oa
	end if 
	'批量获取钉钉日志,分页查询
	Dim has_more,next_cursor,insIndex
	has_more = true
	next_cursor=0
	insIndex = 0
	do while has_more
	  	Set params = New aspJSON '把params定义为一个json格式,把前端页面传过来的几个参数按照钉钉开发文档要求的格式进行梳理 然后提交给钉钉云端查询'
		params.data.Add "cursor", next_cursor
		params.data.Add "size", 20
		params.data.Add "start_time", int(ToUnixTime(startTime,+8))*1000
		params.data.Add "end_time", int(ToUnixTime(endTime,+8))*1000+86400000 '+86400000的原因是钉钉不查询当天的日报,需要输入明天的日期才能查看今天的日报'
		params.data.Add "template_name", templateName
		params.data.Add "userid", userId
		Set logList = ListLogs(params,access_token)'ListLogs函数就是dingdingutil.asp页面里面向钉钉post获取日志的函数'
		dim errcode
		errcode = logList.data("errcode")
		if errcode = 0 then '成功
			set instanceList = logList.data("result").item("data_list") '取出日志列表
			next_cursor = logList.data("result").item("next_cursor") '下一页起始id
			has_more = logList.data("result").item("has_more") '判断是否有下一页
			if instanceList.count > 0 then
				For Each phonenr In instanceList
					With rstJSON.data("list")
						.Add insIndex, instanceList(phonenr)
					end With
					insIndex = insIndex + 1
				Next
			end if
		else
			Exit Do
		end if
	loop
	
	Response.Write rstJSON.JSONoutput() 
%>

















