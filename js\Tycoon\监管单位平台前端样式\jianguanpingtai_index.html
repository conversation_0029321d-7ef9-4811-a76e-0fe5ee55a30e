﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>监管单位全称</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">
		<!-- Chat css -->
		<link rel="stylesheet" href="css/chat.css">

		<!-- *************
			************ Vendor Css Files *************
		************ -->
			<!-- 点击连接 ajax异步显示 begin   非常重要  -->
			<script type="text/javascript">
				function toContent(url){
					if(url != '/'){
						$("#mainFrame").attr("src",url);
					};
					  
				};
			</script>
			<!-- 点击连接 ajax异步显示 end  -->

	</head>

	<body>

		<!-- Loading starts -->
		<div id="loading-wrapper">
			<div class="spinner-border" role="status">
				<span class="sr-only">Loading...</span>
			</div>
		</div>
		<!-- Loading ends -->
		
		<!-- Page wrapper start -->
		<div class="page-wrapper">
			
			<!-- Sidebar wrapper start -->
			<nav id="sidebar" class="sidebar-wrapper">

				
				
				<!-- User profile start -->
				<div align="center">
					<img class="img-fluid" style="max-width: 100px;" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2F5b0988e595225.cdn.sohucs.com%2Fq_70%2Cc_zoom%2Cw_640%2Fimages%2F20191127%2F23a4636ad2074accaa8a33b994cc39a9.JPG&refer=http%3A%2F%2F5b0988e595225.cdn.sohucs.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=**********&t=8c6c647b940e4ed5b51d196c16f877ef" class="profile-thumb" alt="User Thumb">
					<h3>监管单位全称</h3>
				</div>
				
				<!-- Sidebar brand end  
				<div class="sidebar-user-details">
					<div class="profile-actions">
						<a href="account-settings.html" data-toggle="tooltip" data-placement="top" title="" data-original-title="信息设置">
							<i class="icon-settings1"></i>
						</a>
						<a href="javascript:void(0)" data-toggle="tooltip" data-placement="top" title="" data-original-title="用户管理">
							<i class="icon-user1"></i>
						</a>
						<a href="login.html" class="red" data-toggle="tooltip" data-placement="top" title="" data-original-title="Logout">
							<i class="icon-power1"></i>
						</a>
					</div>					
				</div>
				User profile end -->

				<!-- Sidebar content start -->
				<div class="sidebar-content">

					<!-- sidebar menu start -->
					<div class="sidebar-menu">
						<ul>
							<li class="sidebar-dropdown active ">
								<a href="#">
									<i class="icon-circular-graph"></i>
									<span class="menu-text">网络安全检查工作</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_pichi_view.html')" class="current-page">正在检查中的</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jinnian_tongji.html')">全年检查看板</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchapichi_list.html')">检查批次列表</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_nian_mingxi_list.html')">检查记录明细表</a>
										</li>
									
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_danwei_tongji.html')">得分分析表</a>
										</li>	
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_koufen_tongji.html')">扣分项分析表</a>
										</li>	
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_hangye_tongji.html')">行业得分对比表</a>
										</li>		
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_moudanwei_fenxi.html')">某个单位详情</a>
										</li>	
									</ul>
								</div>
							</li>
							<li >
								<a href="#" onclick="toContent('jianguanpingtai_redian_list.html')">
									<i class="icon-book-open"></i>
									<span class="menu-text">网络安全热点工作</span>
								</a>
							</li>
							<li >
								<a href="#"onclick="toContent('jianguanpingtai_falvfagui_list.html')">
									<i class="icon-star2"></i>
									<span class="menu-text">网络安全法律法规</span>
								</a>
							</li>
							<li >
								<a href="#"onclick="toContent('jianguanpingtai_tongbao_list.html')">
									<i class="icon-bell"></i>
									<span class="menu-text">网络安全通报工作</span>
								</a>
							</li>
							
							<li >
								<a href="#"onclick="toContent('jianguanpingtai_zhongbao_list.html')">
									<i class="icon-shield1"></i>
									<span class="menu-text">网络安全重保工作</span>
								</a>
							</li>
							<li >
								<a href="#"onclick="toContent('jianguanpingtai_peixun_list.html')">
									<i class="icon-monitor"></i>
									<span class="menu-text">网络安全培训工作</span>
								</a>
							</li>
							<li >
								<a href="#"onclick="toContent('jianguanpingtai_yanlian_list.html')">
									<i class="icon-local_car_wash"></i>
									<span class="menu-text">安全应急演练工作</span>
								</a>
							</li>
						
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-people_outline"></i>
									<span class="menu-text">检查单位信息</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">医疗行业</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">政府单位</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">企事业单位</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">其他</a>
										</li>
										
									</ul>
								</div>
							</li>
							<li >
								<a href="#">
									<i class="icon-timeline"></i>
									<span class="menu-text"><DEL>支撑单位工作成绩（是否添加待考虑）</DEL></span>
								</a>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-people_outline"></i>
									<span class="menu-text"><DEL>日常监测工作</DEL></span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">网站监测</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">app公众号</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">企事业单位</a>
										</li>
										<li>
											<a href="#"onclick="toContent('jianguanpingtai_jianchadanwei_list.html')">其他</a>
										</li>
										
									</ul>
								</div>
							</li>
							
						
							
							
							
							
							
							
						</ul>
					</div>
					<!-- sidebar menu end -->

				</div>
				<!-- Sidebar content end -->
				
			</nav>
			<!-- Sidebar wrapper end -->

			<!-- Page content start  -->
			<div class="page-content">				
				
				<!-- Header start -->
				<header class="header">
					<div class="toggle-btns">
						<a id="toggle-sidebar" href="#">
							<i class="icon-menu"></i>
						</a>
						<a id="pin-sidebar" href="#">
							<i class="icon-menu"></i>
						</a>
					</div>
					<div class="header-items">
						
						<!-- Header actions start -->
						<ul class="header-actions">
							
							
							<li class="dropdown user-settings">
								<a href="#" id="userSettings" data-toggle="dropdown" aria-haspopup="true">
									<img src="img/user2.png" class="user-avatar" alt="Avatar">
								</a>
								<div class="dropdown-menu dropdown-menu-right" aria-labelledby="userSettings">
									<div class="header-profile-actions">
										<div class="header-user-profile">
											<div class="header-user">
												<img src="img/user2.png" alt="Admin Template">
											</div>
											<h5>用户甲</h5>
											<p>单位:焦作网信办</p>
										</div>
										<a href="account-settings.html"><i class="icon-settings1"></i> 密码重置</a>
										<a href="login.html"><i class="icon-log-out1"></i> Sign Out</a>
									</div>
								</div>
							</li>
						</ul>						
						<!-- Header actions end -->
					</div>
				</header>
				<!-- Header end -->

				<!-- Main container start -->
				<div class="main-container">

					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							<span id="message" style="color:green"></span>
							<div class="embed-responsive embed-responsive-4by3">
							  <iframe id="mainFrame" class="embed-responsive-item"  ></iframe>
							
							</div>
						</div>
					</div>
						
					

				</div>
				<!-- Main container end -->

				<!-- Container fluid start -->
				<div class="container-fluid">
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">
							<!-- Footer start -->
							<div class="footer">
								Copyright <a href="http://www.secdriver.com">河南信安世纪科技有限公司</a>  2022
							</div>
							<!-- Footer end -->
						</div>
					</div>
					<!-- Row end -->
				</div>
				<!-- Container fluid end -->

				
			</div>
			<!-- Page content end -->

		</div>
		<!-- Page wrapper end -->

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>


		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>

		<!-- Polyfill JS -->
		<script src="vendor/polyfill/polyfill.min.js"></script>
		<script src="vendor/polyfill/class-list.min.js"></script>

		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		
		<!-- Peity Charts -->
		<script src="vendor/peity/peity.min.js"></script>
		<script src="vendor/peity/custom-peity.js"></script>
		
		<!-- Circleful Charts -->
		<script src="vendor/circliful/circliful.min.js"></script>
		<script src="vendor/circliful/circliful.custom.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>