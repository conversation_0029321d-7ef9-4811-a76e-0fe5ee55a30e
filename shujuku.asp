<%
'这是最终的信安数据库连接文件
db="/xinan/oa.mdb"
dim conn,db 
dim connstr 
'response.write server.mappath(""&db&"")
'response.end
'on error resume next 
connstr="provider=microsoft.jet.oledb.4.0;data source="+server.mappath(""&db&"")
set conn=server.createobject("ADODB.CONNECTION") 
if err then 
	err.clear 
else 
	conn.open connstr 
end if 

Set rs = Server.CreateObject("ADODB.Recordset")
Set rs2 = Server.CreateObject("ADODB.Recordset")

Set connerp = Server.CreateObject("ADODB.Connection")
connerp.Open connstr

Set rserp = Server.CreateObject("ADODB.Recordset")
Set rserp.ActiveConnection = connerp

Set connxa = Server.CreateObject("ADODB.Connection")
connxa.Open connstr

Set rsxa = Server.CreateObject("ADODB.Recordset")
Set rsxa.ActiveConnection = connxa

'Dim conn, rs
'Set conn = Server.CreateObject("ADODB.Connection")
'Set rs = Server.CreateObject("ADODB.Recordset")

'' 数据库连接字符串
'Dim connectionString
'connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" & Server.MapPath("E:/work/secdriver/oa/oa.accdb") & ";Persist Security Info=False;"

'' 打开数据库连接
'if err then 
'	err.clear 
'else 
'	conn.Open connectionString
'end if 


'' 定义连接字符串
'Dim connStr
''connStr = "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=" & Server.MapPath("oa11.accdb")&";"
'connStr = "Provider=Microsoft.jet.OLEDB.4.0;Data Source=E:\work\secdriver\oa\oa.mdb"
'' 创建连接对象并打开连接
'Dim conn
'Set conn = Server.CreateObject("ADODB.Connection")
'conn.Open connStr


'LogMessage "This is a log message."

'response.write("success")
'response.end



'预设session
Session("name") = "JohnDoe"
Session("mingzhi") = "lixiaocheng"
%>