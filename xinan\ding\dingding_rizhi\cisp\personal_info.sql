-- 创建培训报名信息表
CREATE TABLE 认证培训之培训报名表 (

    编号 INT IDENTITY(1,1) PRIMARY KEY,                -- 自增主键
    姓名 NVARCHAR(50) NOT NULL,                       -- 姓名
    拼音 NVARCHAR(100),                               -- 拼音
    单位名称 NVARCHAR(100) NOT NULL,                  -- 单位名称
    证件号码 NVARCHAR(18) NOT NULL,                   -- 身份证/军官证
    培训种类 NVARCHAR(50) NOT NULL,                   -- 培训种类
    培训时间 NVARCHAR(100) NOT NULL,                  -- 培训时间
    考试状态 NVARCHAR(20),                            -- 考试状态
    是否持证 BIT DEFAULT 0,                           -- 是否持有CISP证书
    创建时间 DATETIME DEFAULT GETDATE(),              -- 创建时间
    更新时间 DATETIME DEFAULT GETDATE(),              -- 更新时间
    是否删除 BIT DEFAULT 0                            -- 软删除标记
)

-- 创建个人信息表
DROP TABLE [认证培训之个人信息表];
CREATE TABLE 认证培训之个人信息表 (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    姓名 NVARCHAR(50) NOT NULL,
    姓名拼音 NVARCHAR(100),
    性别 NVARCHAR(10) NOT NULL,
    民族 NVARCHAR(50),
    出生日期 DATE,
    籍贯 NVARCHAR(100),
    政治面貌 NVARCHAR(50),
    专业 NVARCHAR(100),
    最高学历 NVARCHAR(500),

    身份证号 NVARCHAR(18) NOT NULL,
    工作单位 NVARCHAR(200),
    通信地址 NVARCHAR(500),
    邮编 NVARCHAR(10),
    联系电话 NVARCHAR(20),
    手机号码 NVARCHAR(20),
    电子邮箱 NVARCHAR(100),


    第一学历证书 NVARCHAR(500),
    最高学历证书 NVARCHAR(500),
    电子版照片 NVARCHAR(500),
    个人推荐书 NVARCHAR(500),
    个人声明书 NVARCHAR(500),
    身份证正面 NVARCHAR(500),
    身份证反面 NVARCHAR(500),

    创建时间 DATETIME DEFAULT GETDATE(),
    更新时间 DATETIME DEFAULT GETDATE()
);

-- 创建文化程度表
CREATE TABLE 认证培训之文化程度表 (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    个人信息ID INT FOREIGN KEY REFERENCES 认证培训之个人信息表(ID),
    时间 NVARCHAR(100),
    毕业学校 NVARCHAR(200),
    学历 NVARCHAR(50),
    专业 NVARCHAR(100)
);

-- 创建培训证书表
CREATE TABLE 认证培训之培训证书表 (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    个人信息ID INT FOREIGN KEY REFERENCES 认证培训之个人信息表(ID),
    证书名称 NVARCHAR(200),
    发证机构 NVARCHAR(200),
    证书编号 NVARCHAR(100),
    证书文件 NVARCHAR(500),
    获证日期 DATE
);

CREATE TABLE 认证培训之工作经历(

    [编号] [int] IDENTITY(1,1) NOT NULL,
    [个人信息ID] INT FOREIGN KEY REFERENCES 认证培训之个人信息表(ID),
    [起止日期] [date] NOT NULL,
    [结束日期] [date] NOT NULL,
    [工作单位] [nvarchar](200) NOT NULL,
    [职务] [nvarchar](100) NOT NULL,
    [主要职责] [nvarchar](500) NULL,
    [证明材料] [nvarchar](200) NULL,
    [是否安全相关] [bit] NOT NULL DEFAULT(0),
    [创建时间] [datetime] NOT NULL DEFAULT(GETDATE()),
    [更新时间] [datetime] NULL
);

-- 创建索引
CREATE INDEX IDX_认证培训之培训报名表_证件号码 ON 认证培训之培训报名表(证件号码)
CREATE INDEX IDX_认证培训之个人信息表_身份证号 ON 认证培训之个人信息表(身份证号);
CREATE INDEX IDX_认证培训之文化程度表_个人信息ID ON 认证培训之文化程度表(个人信息ID);
CREATE INDEX IDX_认证培训之培训证书表_个人信息ID ON 认证培训之培训证书表(个人信息ID);
CREATE INDEX IDX_认证培训之工作经历_个人信息ID ON 认证培训之工作经历(个人信息ID);






-- 先删除原来的表
DROP INDEX IDX_认证培训之培训报名表_证件号码 ON 认证培训之培训报名表;
DROP INDEX IDX_认证培训之个人信息表_身份证号 ON 认证培训之个人信息表;
DROP INDEX IDX_认证培训之文化程度表_个人信息ID ON 认证培训之文化程度表;
DROP INDEX IDX_认证培训之培训证书表_个人信息ID ON 认证培训之培训证书表;
DROP INDEX IDX_认证培训之工作经历_个人信息ID ON 认证培训之工作经历;

DROP TABLE IF EXISTS 认证培训之工作经历;
DROP TABLE IF EXISTS 认证培训之培训证书表;
DROP TABLE IF EXISTS 认证培训之文化程度表;
DROP TABLE IF EXISTS 认证培训之培训报名表;
DROP TABLE IF EXISTS 认证培训之个人信息表;

-- 再创建新表
CREATE TABLE [认证培训之个人信息表] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [姓名] NVARCHAR(50),
    [通信地址] NVARCHAR(200),
    [手机号码] NVARCHAR(20),
    [电子邮箱] NVARCHAR(100),
    [电子版照片] NVARCHAR(200),
    [身份证正面] NVARCHAR(200),
    [身份证反面] NVARCHAR(200),
    [证书信息附件] NVARCHAR(200),
    [创建时间] DATETIME,
    [更新时间] DATETIME
);


CREATE TABLE [认证培训之培训报名表] (
    [编号] INT IDENTITY(1,1) PRIMARY KEY,
    [姓名] NVARCHAR(50),
    [性别] NVARCHAR(10),
    [身份证号] NVARCHAR(20),
    [手机号码] NVARCHAR(20),
    [单位] NVARCHAR(100),
    [报考方向] NVARCHAR(50),
    [意向考试月份] NVARCHAR(20),
    [意向考试城市] NVARCHAR(50),
    [考试状态] NVARCHAR(20),
    [创建时间] DATETIME,
    [更新时间] DATETIME,
    [是否删除] NVARCHAR(10)
);

CREATE INDEX IDX_认证培训之培训报名表_证件号码 ON 认证培训之培训报名表(证件号码)
CREATE INDEX IDX_认证培训之个人信息表_身份证号 ON 认证培训之个人信息表(身份证号);