﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>Tycoon Admin Template  - Grid</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
		<!-- Prism css -->
		<link rel="stylesheet" href="vendor/prism/prism.css" />

	</head>

	<body>
		
		<!-- Page wrapper start -->
		<div class="page-wrapper">
			
			<!-- Sidebar wrapper start -->
			<nav id="sidebar" class="sidebar-wrapper">

				<!-- Sidebar brand start  -->
				<div class="sidebar-brand">
					<a href="index.html" class="logo">Tycöòn</a>
				</div>
				<!-- Sidebar brand end  -->
				
				<!-- Quick links start -->
				<div class="quick-links-container">
					<h6>Quick Links</h6>
					<div class="quick-links">
						<a href="dashboard2.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Sales">
							<i class="icon-line-graph"></i>
						</a>
						<a href="dashboard3.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Reports">
							<i class="icon-triangle"></i>
						</a>
						<a href="widgets.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Widgets">
							<i class="icon-layers2"></i>
						</a>
						<a href="graph-widgets.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Graph Widgets">
							<i class="icon-pie-chart1"></i>
						</a>
						<a href="account-settings.html" class="bg-danger" data-toggle="tooltip" data-placement="top" title="" data-original-title="Settings">
							<i class="icon-settings1"></i>
						</a>
						<a href="login.html" class="bg-danger" data-toggle="tooltip" data-placement="top" title="" data-original-title="Logout">
							<i class="icon-power1"></i>
						</a>
					</div>
				</div>
				<!-- Quick links end -->

				<!-- Sidebar content start -->
				<div class="sidebar-content">

					<!-- sidebar menu start -->
					<div class="sidebar-menu">
						<ul>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-home2"></i>
									<span class="menu-text">Dashboards</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="index.html">Dashboard</a>
										</li>
										<li>
											<a href="dashboard2.html">Dashboard 2</a>
										</li>
										<li>
											<a href="dashboard3.html">Dashboard 3</a>
										</li>
										<li>
											<a href="dashboard4.html">Dashboard 4</a>
										</li>
									</ul>
								</div>
							</li>
							<li>
								<a href="widgets.html">
									<i class="icon-circular-graph"></i>
									<span class="menu-text">Widgets</span>
								</a>
							</li>
							<li>
								<a href="graph-widgets.html" class="current-page">
									<i class="icon-line-graph"></i>
									<span class="menu-text">Graph Widgets</span>
								</a>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-calendar1"></i>
									<span class="menu-text">Calendars</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="calendar.html">Daygrid View</a>
										</li>
										<li>
											<a href="calendar-external-draggable.html">External Draggable</a>
										</li>
										<li>
											<a href="calendar-google.html">Google Calendar</a>
										</li>
										<li>
											<a href="calendar-list-view.html">List View</a>
										</li>
										<li>
											<a href="calendar-selectable.html">Selectable</a>
										</li>
										<li>
											<a href="calendar-week-numbers.html">Week Numbers</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown active">
								<a href="#">
									<i class="icon-layers2"></i>
									<span class="menu-text">Layouts</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="default-layout.html">Default Layout</a>
										</li>
										<li>
											<a href="layout-option2.html">Layout Option 2</a>
										</li>
										<li>
											<a href="layout-option3.html">Layout Option 3</a>
										</li>
										<li>
											<a href="layout-dark-header.html">Dark Header</a>
										</li>
										<li>
											<a href="layout-sidebar-mini.html">Sidebar Mini</a>
										</li>
										<li>
											<a href="slim-sidebar.html">Slim Layout</a>
										</li>
										<li>
											<a href="layout-daterange.html">Layout Date Range</a>
										</li>
										<li>
											<a href="cards.html">Cards</a>
										</li>
										<li>
											<a href="grid.html" class="current-page">Grid</a>
										</li>
										<li>
											<a href="grid-doc.html">Grid Doc</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-book-open"></i>
									<span class="menu-text">Pages</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="account-settings.html">Account Settings</a>
										</li>										
										<li>
											<a href="faq.html">Faq</a>
										</li>
										<li>
											<a href="gallery.html">Gallery</a>
										</li>
										<li>
											<a href="invoice.html">Invoice</a>
										</li>
										<li>
											<a href="pricing.html">Pricing Plans</a>
										</li>
										<li>
											<a href="search-results.html">Search Results</a>
										</li>	
										<li>
											<a href="timeline.html">Timeline</a>
										</li>
										<li>
											<a href="user-profile.html">User Profile</a>
										</li>
									</ul>
								</div>
							</li>		
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-edit1"></i>
									<span class="menu-text">Forms</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="datepickers.html">Datepickers</a>
										</li>
										<li>
											<a href="editor.html">Editor</a>
										</li>
										<li>
											<a href="form-inputs.html">Inputs</a>
										</li>
										<li>
											<a href="input-groups.html">Input Groups</a>
										</li>
										<li>
											<a href="check-radio.html">Check Boxes</a>
										</li>
										<li>
											<a href="input-masks.html">Input Masks</a>
										</li>
										<li>
											<a href="input-tags.html">Input Tags</a>
										</li>
										<li>
											<a href="range-sliders.html">Range Sliders</a>
										</li>
										<li>
											<a href="wizard.html">Wizards</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-box"></i>
									<span class="menu-text">jQuery Components</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="accordion.html">Accordion</a>
										</li>
										<li>
											<a href="accordion-icons.html">Accordion Icons</a>
										</li>
										<li>
											<a href="accordion-arrows.html">Accordion Arrows</a>
										</li>
										<li>
											<a href="accordion-lg.html">Accordion Large</a>
										</li>
										<li>
											<a href="carousel.html">Carousels</a>
										</li>
										<li>
											<a href="modals.html">Modals</a>
										</li>
										<li>
											<a href="alerts.html">Notifications</a>
										</li>
										<li>
											<a href="tooltips.html">Tooltips</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-star2"></i>
									<span class="menu-text">UI Elements</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="avatars.html">Avatars</a>
										</li>
										<li>
											<a href="buttons.html">Buttons</a>
										</li>
										<li>
											<a href="button-groups.html">Button Groups</a>
										</li>
										<li>
											<a href="dropdowns.html">Dropdowns</a>
										</li>
										<li>
											<a href="icons.html">Icons</a>
										</li>	
										<li>
											<a href="jumbotron.html">Jumbotron</a>
										</li>
										<li>
											<a href="labels-badges.html">Labels &amp; Badges</a>
										</li>
										<li>
											<a href="list-items.html">List Items</a>
										</li>
										<li>
											<a href="pagination.html">Paginations</a>
										</li>
										<li>
											<a href="progress.html">Progress Bars</a>
										</li>
										<li>
											<a href="pills.html">Pills</a>
										</li>
										<li>
											<a href="spinners.html">Spinners</a>
										</li>
										<li>
											<a href="typography.html">Typography</a>
										</li>					
										<li>
											<a href="images.html">Thumbnails</a>
										</li>
										<li>
											<a href="text-avatars.html">Text Avatars</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-border_all"></i>
									<span class="menu-text">Tables</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="data-tables.html">Data Tables</a>
										</li>
										<li>
											<a href="custom-tables.html">Custom Tables</a>
										</li>
										<li>
											<a href="default-table.html">Default Table</a>
										</li>
										<li>
											<a href="table-bordered.html">Table Bordered</a>
										</li>
										<li>
											<a href="table-hover.html">Table Hover</a>
										</li>
										<li>
											<a href="table-striped.html">Table Striped</a>
										</li>
										<li>
											<a href="table-small.html">Table Small</a>
										</li>
										<li>
											<a href="table-colors.html">Table Colors</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-pie-chart1"></i>
									<span class="menu-text">Graphs</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="apex-graphs.html">Apex Graphs</a>
										</li>
										<li>
											<a href="morris-graphs.html">Morris Graphs</a>
										</li>
										<li>
											<a href="vector-maps.html">Vector Maps</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-unlock"></i>
									<span class="menu-text">Authentication</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="login.html">Login</a>
										</li>
										<li>
											<a href="signup.html">Signup</a>
										</li>
										<li>
											<a href="forgot-pwd.html">Forgot Password</a>
										</li>
										<li>
											<a href="error.html">404</a>
										</li>
										<li>
											<a href="error2.html">505</a>
										</li>
										<li>
											<a href="coming-soon.html">Coming Soon</a>
										</li>
									</ul>
								</div>
							</li>
						</ul>
					</div>
					<!-- sidebar menu end -->

				</div>
				<!-- Sidebar content end -->
				
			</nav>
			<!-- Sidebar wrapper end -->

			<!-- Page content start  -->
			<div class="page-content">

				<!-- Header start -->
				<header class="header">
					<div class="toggle-btns">
						<a id="toggle-sidebar" href="#">
							<i class="icon-menu"></i>
						</a>
						<a id="pin-sidebar" href="#">
							<i class="icon-menu"></i>
						</a>
					</div>
					<div class="header-items">
						<!-- Custom search start -->
						<div class="custom-search">
							<input type="text" class="search-query" placeholder="Search here ...">
							<i class="icon-search1"></i>
						</div>
						<!-- Custom search end -->

						<!-- Header actions start -->
						<ul class="header-actions">
							<li class="dropdown d-none d-sm-block">
								<a href="#" id="notifications" data-toggle="dropdown" aria-haspopup="true">
									<i class="icon-calendar1"></i>
								</a>
								<div class="dropdown-menu dropdown-menu-right lrg" aria-labelledby="notifications">
									<div class="dropdown-menu-header">
										Events (10)
									</div>
									<ul class="header-notifications">
										<li>
											<a href="#">
												<div class="user-img away">
													<img src="img/user6.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Abbott</div>
													<div class="noti-details">Membership has been ended.</div>
													<div class="noti-date">Oct 20, 07:30 pm</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img busy">
													<img src="img/user13.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Braxten</div>
													<div class="noti-details">Approved new design.</div>
													<div class="noti-date">Oct 10, 12:00 am</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img online">
													<img src="img/user19.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Larkyn</div>
													<div class="noti-details">Check out every table in detail.</div>
													<div class="noti-date">Oct 15, 04:00 pm</div>
												</div>
											</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="dropdown d-none d-sm-block">
								<a href="#" id="notifications" data-toggle="dropdown" aria-haspopup="true">
									<i class="icon-star2"></i>
									<span class="count-label blue"></span>
								</a>
								<div class="dropdown-menu dropdown-menu-right lrg" aria-labelledby="notifications">
									<div class="dropdown-menu-header">
										Bookmarks (21)
									</div>
									<div class="customScroll5">
										<ul class="bookmarks p-3">
											<li>
												<a href="#">Bootstrap admin template</a>
											</li>
											<li>
												<a href="#">Images resources</a>
											</li>
											<li>
												<a href="#">Best admin templates 2020</a>
											</li>
											<li>
												<a href="#">Javascript libraries</a>
											</li>
											<li>
												<a href="#">Angular widgets</a>
											</li>
											<li>
												<a href="#">UX library</a>
											</li>
											<li>
												<a href="#">Bootstrap admin template</a>
											</li>
											<li>
												<a href="#">Images resources</a>
											</li>
											<li>
												<a href="#">Best admin templates 2020</a>
											</li>
											<li>
												<a href="#">Javascript libraries</a>
											</li>
											<li>
												<a href="#">Angular widgets</a>
											</li>
											<li>
												<a href="#">UX library</a>
											</li>
											<li>
												<a href="#">Bootstrap admin template</a>
											</li>
											<li>
												<a href="#">Images resources</a>
											</li>
											<li>
												<a href="#">Best admin templates 2020</a>
											</li>
											<li>
												<a href="#">Javascript libraries</a>
											</li>
											<li>
												<a href="#">Angular widgets</a>
											</li>
											<li>
												<a href="#">UX library</a>
											</li>
										</ul>
									</div>
								</div>
							</li>
							<li class="dropdown d-none d-sm-block">
								<a href="#" id="notifications" data-toggle="dropdown" aria-haspopup="true">
									<i class="icon-bell"></i>
									<span class="count-label"></span>
								</a>
								<div class="dropdown-menu dropdown-menu-right lrg" aria-labelledby="notifications">
									<div class="dropdown-menu-header">
										Notifications (40)
									</div>
									<ul class="header-notifications">
										<li>
											<a href="#">
												<div class="user-img away">
													<img src="img/user21.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Abbott</div>
													<div class="noti-details">Membership has been ended.</div>
													<div class="noti-date">Oct 20, 07:30 pm</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img busy">
													<img src="img/user10.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Braxten</div>
													<div class="noti-details">Approved new design.</div>
													<div class="noti-date">Oct 10, 12:00 am</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img online">
													<img src="img/user6.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Larkyn</div>
													<div class="noti-details">Check out every table in detail.</div>
													<div class="noti-date">Oct 15, 04:00 pm</div>
												</div>
											</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="dropdown user-settings">
								<a href="#" id="userSettings" data-toggle="dropdown" aria-haspopup="true">
									<img src="img/user2.png" class="user-avatar" alt="Avatar">
								</a>
								<div class="dropdown-menu dropdown-menu-right" aria-labelledby="userSettings">
									<div class="header-profile-actions">
										<div class="header-user-profile">
											<div class="header-user">
												<img src="img/user2.png" alt="Admin Template">
											</div>
											<h5>Yuki Hayashi</h5>
											<p>Super User</p>
										</div>
										<a href="user-profile.html"><i class="icon-user1"></i> My Profile</a>
										<a href="account-settings.html"><i class="icon-settings1"></i> Account Settings</a>
										<a href="login.html"><i class="icon-log-out1"></i> Sign Out</a>
									</div>
								</div>
							</li>
						</ul>						
						<!-- Header actions end -->
					</div>
				</header>
				<!-- Header end -->

				<!-- Main container start -->
				<div class="main-container">

					<!-- Page header start -->
					<div class="page-header">
						
						<!-- Breadcrumb start -->
						<ol class="breadcrumb">
							<li class="breadcrumb-item">Grid</li>
						</ol>
						<!-- Breadcrumb end -->

						<!-- App actions start -->
						<div class="app-actions">
							<button type="button" class="btn">Today</button>
							<button type="button" class="btn">Yesterday</button>
							<button type="button" class="btn">7 days</button>
							<button type="button" class="btn">15 days</button>
							<button type="button" class="btn active">30 days</button>
						</div>
						<!-- App actions end -->

					</div>
					<!-- Page header end -->

					<!-- One column in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Single Column</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-12">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->


									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-12"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->







					<!-- Two columns in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Two Columns</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-6">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-6">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->






					<!-- Three columns in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Three Columns</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>  
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->







					<!-- Four columns in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Four Columns</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-3">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-3">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-3">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-3">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-3"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-3"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-3"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-3"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->







					<!-- Six columns in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Six Columns</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>  
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->






					<!-- Twelve columns in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Twelve Columns</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-1">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-1"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>  
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- Different columns in a row start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Different Column Sizes</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-6">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-5">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-3">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-5"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-3"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>  
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->






					<!-- Justify Content Start -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Justify content start</div>
									<div class="card-sub-title">
										Use justify-content utilities on flexbox containers to change the alignment of flex items on the main axis (the x-axis to start, y-axis if flex-direction: column). Choose from start (browser default), end, center, between, or around.
									</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters justify-content-start">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters justify-content-start"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->






					<!-- Justify Content End -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Justify content end</div>
									<div class="card-sub-title">
										Use justify-content utilities on flexbox containers to change the alignment of flex items on the main axis (the x-axis to start, y-axis if flex-direction: column). Choose from start (browser default), end, center, between, or around.
									</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters justify-content-end">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters justify-content-end"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- Justify Content End -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Justify content center</div>
									<div class="card-sub-title">
										Use justify-content utilities on flexbox containers to change the alignment of flex items on the main axis (the x-axis to start, y-axis if flex-direction: column). Choose from start (browser default), end, center, between, or around.
									</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters justify-content-center">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters justify-content-center"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- Justify Content Between -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Justify content between</div>
									<div class="card-sub-title">
										Use justify-content utilities on flexbox containers to change the alignment of flex items on the main axis (the x-axis to start, y-axis if flex-direction: column). Choose from start (browser default), end, center, between, or around.
									</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters justify-content-between">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters justify-content-between"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->






					<!-- Justify Content Between -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Justify content around</div>
									<div class="card-sub-title">
										Use justify-content utilities on flexbox containers to change the alignment of flex items on the main axis (the x-axis to start, y-axis if flex-direction: column). Choose from start (browser default), end, center, between, or around.
									</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters justify-content-around">
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-2">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters justify-content-around"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- No Gutters Between Columns -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">No Gutters</div>
									<div class="card-sub-title">The gutters between columns in our predefined grid classes can be removed with .no-gutters. This removes the negative margins from .row and the horizontal padding from all immediate children columns.</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row no-gutters">
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-4">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row no-gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Mix and Match Columns -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Column sizes at different breakpoints</div>
									<div class="card-sub-title">Resize the screen to view column sizes.</div>
								</div>
								<div class="card-body">
									
									<!-- Row start -->
									<div class="row gutters">
										<div class="col-xl-4 col-lg-6 col-md-6 col-sm-4 col-12">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-xl-4 col-lg-6 col-md-6 col-sm-4 col-12">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
										<div class="col-xl-4 col-lg-12 col-md-12 col-sm-4 col-12">

											<!-- Card start -->
											<div class="card highlight">
												<div class="card-body">

												</div>
											</div>
											<!-- Card end -->

										</div>
									</div>
									<!-- Row end -->

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-xl-4 col-lg-6 col-md-6 col-sm-4 col-12"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-xl-4 col-lg-6 col-md-6 col-sm-4 col-12"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-xl-4 col-lg-12 col-md-12 col-sm-4 col-12"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->

				</div>
				<!-- Main container end -->

				<!-- Container fluid start -->
				<div class="container-fluid">
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">
							<!-- Footer start -->
							<div class="footer">
								Copyright <a href="http://www.bootstrapmb.com">Tycoon</a> Admin 2020
							</div>
							<!-- Footer end -->
						</div>
					</div>
					<!-- Row end -->
				</div>
				<!-- Container fluid end -->

			</div>
			<!-- Page content end -->

		</div>
		<!-- Page wrapper end -->

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>


		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>
		
		<!-- Prism -->
		<script src="vendor/prism/prism.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>