﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>Tycoon Admin Template  - Grid Documentation</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
		<!-- Prism css -->
		<link rel="stylesheet" href="vendor/prism/prism.css" />

	</head>

	<body>
		
		<!-- Page wrapper start -->
		<div class="page-wrapper">
			
			<!-- Sidebar wrapper start -->
			<nav id="sidebar" class="sidebar-wrapper">

				<!-- Sidebar brand start  -->
				<div class="sidebar-brand">
					<a href="index.html" class="logo">Tycöòn</a>
				</div>
				<!-- Sidebar brand end  -->
				
				<!-- Quick links start -->
				<div class="quick-links-container">
					<h6>Quick Links</h6>
					<div class="quick-links">
						<a href="dashboard2.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Sales">
							<i class="icon-line-graph"></i>
						</a>
						<a href="dashboard3.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Reports">
							<i class="icon-triangle"></i>
						</a>
						<a href="widgets.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Widgets">
							<i class="icon-layers2"></i>
						</a>
						<a href="graph-widgets.html" class="bg-info" data-toggle="tooltip" data-placement="top" title="" data-original-title="Graph Widgets">
							<i class="icon-pie-chart1"></i>
						</a>
						<a href="account-settings.html" class="bg-danger" data-toggle="tooltip" data-placement="top" title="" data-original-title="Settings">
							<i class="icon-settings1"></i>
						</a>
						<a href="login.html" class="bg-danger" data-toggle="tooltip" data-placement="top" title="" data-original-title="Logout">
							<i class="icon-power1"></i>
						</a>
					</div>
				</div>
				<!-- Quick links end -->

				<!-- Sidebar content start -->
				<div class="sidebar-content">

					<!-- sidebar menu start -->
					<div class="sidebar-menu">
						<ul>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-home2"></i>
									<span class="menu-text">Dashboards</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="index.html">Dashboard</a>
										</li>
										<li>
											<a href="dashboard2.html">Dashboard 2</a>
										</li>
										<li>
											<a href="dashboard3.html">Dashboard 3</a>
										</li>
										<li>
											<a href="dashboard4.html">Dashboard 4</a>
										</li>
									</ul>
								</div>
							</li>
							<li>
								<a href="widgets.html">
									<i class="icon-circular-graph"></i>
									<span class="menu-text">Widgets</span>
								</a>
							</li>
							<li>
								<a href="graph-widgets.html" class="current-page">
									<i class="icon-line-graph"></i>
									<span class="menu-text">Graph Widgets</span>
								</a>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-calendar1"></i>
									<span class="menu-text">Calendars</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="calendar.html">Daygrid View</a>
										</li>
										<li>
											<a href="calendar-external-draggable.html">External Draggable</a>
										</li>
										<li>
											<a href="calendar-google.html">Google Calendar</a>
										</li>
										<li>
											<a href="calendar-list-view.html">List View</a>
										</li>
										<li>
											<a href="calendar-selectable.html">Selectable</a>
										</li>
										<li>
											<a href="calendar-week-numbers.html">Week Numbers</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown active">
								<a href="#">
									<i class="icon-layers2"></i>
									<span class="menu-text">Layouts</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="default-layout.html">Default Layout</a>
										</li>
										<li>
											<a href="layout-option2.html">Layout Option 2</a>
										</li>
										<li>
											<a href="layout-option3.html">Layout Option 3</a>
										</li>
										<li>
											<a href="layout-dark-header.html">Dark Header</a>
										</li>
										<li>
											<a href="layout-sidebar-mini.html">Sidebar Mini</a>
										</li>
										<li>
											<a href="slim-sidebar.html">Slim Layout</a>
										</li>
										<li>
											<a href="layout-daterange.html">Layout Date Range</a>
										</li>
										<li>
											<a href="cards.html">Cards</a>
										</li>
										<li>
											<a href="grid.html">Grid</a>
										</li>
										<li>
											<a href="grid-doc.html" class="current-page">Grid Doc</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-book-open"></i>
									<span class="menu-text">Pages</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="account-settings.html">Account Settings</a>
										</li>										
										<li>
											<a href="faq.html">Faq</a>
										</li>
										<li>
											<a href="gallery.html">Gallery</a>
										</li>
										<li>
											<a href="invoice.html">Invoice</a>
										</li>
										<li>
											<a href="pricing.html">Pricing Plans</a>
										</li>
										<li>
											<a href="search-results.html">Search Results</a>
										</li>	
										<li>
											<a href="timeline.html">Timeline</a>
										</li>
										<li>
											<a href="user-profile.html">User Profile</a>
										</li>
									</ul>
								</div>
							</li>		
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-edit1"></i>
									<span class="menu-text">Forms</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="datepickers.html">Datepickers</a>
										</li>
										<li>
											<a href="editor.html">Editor</a>
										</li>
										<li>
											<a href="form-inputs.html">Inputs</a>
										</li>
										<li>
											<a href="input-groups.html">Input Groups</a>
										</li>
										<li>
											<a href="check-radio.html">Check Boxes</a>
										</li>
										<li>
											<a href="input-masks.html">Input Masks</a>
										</li>
										<li>
											<a href="input-tags.html">Input Tags</a>
										</li>
										<li>
											<a href="range-sliders.html">Range Sliders</a>
										</li>
										<li>
											<a href="wizard.html">Wizards</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-box"></i>
									<span class="menu-text">jQuery Components</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="accordion.html">Accordion</a>
										</li>
										<li>
											<a href="accordion-icons.html">Accordion Icons</a>
										</li>
										<li>
											<a href="accordion-arrows.html">Accordion Arrows</a>
										</li>
										<li>
											<a href="accordion-lg.html">Accordion Large</a>
										</li>
										<li>
											<a href="carousel.html">Carousels</a>
										</li>
										<li>
											<a href="modals.html">Modals</a>
										</li>
										<li>
											<a href="alerts.html">Notifications</a>
										</li>
										<li>
											<a href="tooltips.html">Tooltips</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-star2"></i>
									<span class="menu-text">UI Elements</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="avatars.html">Avatars</a>
										</li>
										<li>
											<a href="buttons.html">Buttons</a>
										</li>
										<li>
											<a href="button-groups.html">Button Groups</a>
										</li>
										<li>
											<a href="dropdowns.html">Dropdowns</a>
										</li>
										<li>
											<a href="icons.html">Icons</a>
										</li>	
										<li>
											<a href="jumbotron.html">Jumbotron</a>
										</li>
										<li>
											<a href="labels-badges.html">Labels &amp; Badges</a>
										</li>
										<li>
											<a href="list-items.html">List Items</a>
										</li>
										<li>
											<a href="pagination.html">Paginations</a>
										</li>
										<li>
											<a href="progress.html">Progress Bars</a>
										</li>
										<li>
											<a href="pills.html">Pills</a>
										</li>
										<li>
											<a href="spinners.html">Spinners</a>
										</li>
										<li>
											<a href="typography.html">Typography</a>
										</li>					
										<li>
											<a href="images.html">Thumbnails</a>
										</li>
										<li>
											<a href="text-avatars.html">Text Avatars</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-border_all"></i>
									<span class="menu-text">Tables</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="data-tables.html">Data Tables</a>
										</li>
										<li>
											<a href="custom-tables.html">Custom Tables</a>
										</li>
										<li>
											<a href="default-table.html">Default Table</a>
										</li>
										<li>
											<a href="table-bordered.html">Table Bordered</a>
										</li>
										<li>
											<a href="table-hover.html">Table Hover</a>
										</li>
										<li>
											<a href="table-striped.html">Table Striped</a>
										</li>
										<li>
											<a href="table-small.html">Table Small</a>
										</li>
										<li>
											<a href="table-colors.html">Table Colors</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-pie-chart1"></i>
									<span class="menu-text">Graphs</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="apex-graphs.html">Apex Graphs</a>
										</li>
										<li>
											<a href="morris-graphs.html">Morris Graphs</a>
										</li>
										<li>
											<a href="vector-maps.html">Vector Maps</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="sidebar-dropdown">
								<a href="#">
									<i class="icon-unlock"></i>
									<span class="menu-text">Authentication</span>
								</a>
								<div class="sidebar-submenu">
									<ul>
										<li>
											<a href="login.html">Login</a>
										</li>
										<li>
											<a href="signup.html">Signup</a>
										</li>
										<li>
											<a href="forgot-pwd.html">Forgot Password</a>
										</li>
										<li>
											<a href="error.html">404</a>
										</li>
										<li>
											<a href="error2.html">505</a>
										</li>
										<li>
											<a href="coming-soon.html">Coming Soon</a>
										</li>
									</ul>
								</div>
							</li>
						</ul>
					</div>
					<!-- sidebar menu end -->

				</div>
				<!-- Sidebar content end -->
				
			</nav>
			<!-- Sidebar wrapper end -->

			<!-- Page content start  -->
			<div class="page-content">

				<!-- Header start -->
				<header class="header">
					<div class="toggle-btns">
						<a id="toggle-sidebar" href="#">
							<i class="icon-menu"></i>
						</a>
						<a id="pin-sidebar" href="#">
							<i class="icon-menu"></i>
						</a>
					</div>
					<div class="header-items">
						<!-- Custom search start -->
						<div class="custom-search">
							<input type="text" class="search-query" placeholder="Search here ...">
							<i class="icon-search1"></i>
						</div>
						<!-- Custom search end -->

						<!-- Header actions start -->
						<ul class="header-actions">
							<li class="dropdown d-none d-sm-block">
								<a href="#" id="notifications" data-toggle="dropdown" aria-haspopup="true">
									<i class="icon-calendar1"></i>
								</a>
								<div class="dropdown-menu dropdown-menu-right lrg" aria-labelledby="notifications">
									<div class="dropdown-menu-header">
										Events (10)
									</div>
									<ul class="header-notifications">
										<li>
											<a href="#">
												<div class="user-img away">
													<img src="img/user6.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Abbott</div>
													<div class="noti-details">Membership has been ended.</div>
													<div class="noti-date">Oct 20, 07:30 pm</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img busy">
													<img src="img/user13.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Braxten</div>
													<div class="noti-details">Approved new design.</div>
													<div class="noti-date">Oct 10, 12:00 am</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img online">
													<img src="img/user19.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Larkyn</div>
													<div class="noti-details">Check out every table in detail.</div>
													<div class="noti-date">Oct 15, 04:00 pm</div>
												</div>
											</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="dropdown d-none d-sm-block">
								<a href="#" id="notifications" data-toggle="dropdown" aria-haspopup="true">
									<i class="icon-star2"></i>
									<span class="count-label blue"></span>
								</a>
								<div class="dropdown-menu dropdown-menu-right lrg" aria-labelledby="notifications">
									<div class="dropdown-menu-header">
										Bookmarks (21)
									</div>
									<div class="customScroll5">
										<ul class="bookmarks p-3">
											<li>
												<a href="#">Bootstrap admin template</a>
											</li>
											<li>
												<a href="#">Images resources</a>
											</li>
											<li>
												<a href="#">Best admin templates 2020</a>
											</li>
											<li>
												<a href="#">Javascript libraries</a>
											</li>
											<li>
												<a href="#">Angular widgets</a>
											</li>
											<li>
												<a href="#">UX library</a>
											</li>
											<li>
												<a href="#">Bootstrap admin template</a>
											</li>
											<li>
												<a href="#">Images resources</a>
											</li>
											<li>
												<a href="#">Best admin templates 2020</a>
											</li>
											<li>
												<a href="#">Javascript libraries</a>
											</li>
											<li>
												<a href="#">Angular widgets</a>
											</li>
											<li>
												<a href="#">UX library</a>
											</li>
											<li>
												<a href="#">Bootstrap admin template</a>
											</li>
											<li>
												<a href="#">Images resources</a>
											</li>
											<li>
												<a href="#">Best admin templates 2020</a>
											</li>
											<li>
												<a href="#">Javascript libraries</a>
											</li>
											<li>
												<a href="#">Angular widgets</a>
											</li>
											<li>
												<a href="#">UX library</a>
											</li>
										</ul>
									</div>
								</div>
							</li>
							<li class="dropdown d-none d-sm-block">
								<a href="#" id="notifications" data-toggle="dropdown" aria-haspopup="true">
									<i class="icon-bell"></i>
									<span class="count-label"></span>
								</a>
								<div class="dropdown-menu dropdown-menu-right lrg" aria-labelledby="notifications">
									<div class="dropdown-menu-header">
										Notifications (40)
									</div>
									<ul class="header-notifications">
										<li>
											<a href="#">
												<div class="user-img away">
													<img src="img/user21.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Abbott</div>
													<div class="noti-details">Membership has been ended.</div>
													<div class="noti-date">Oct 20, 07:30 pm</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img busy">
													<img src="img/user10.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Braxten</div>
													<div class="noti-details">Approved new design.</div>
													<div class="noti-date">Oct 10, 12:00 am</div>
												</div>
											</a>
										</li>
										<li>
											<a href="#">
												<div class="user-img online">
													<img src="img/user6.png" alt="User">
												</div>
												<div class="details">
													<div class="user-title">Larkyn</div>
													<div class="noti-details">Check out every table in detail.</div>
													<div class="noti-date">Oct 15, 04:00 pm</div>
												</div>
											</a>
										</li>
									</ul>
								</div>
							</li>
							<li class="dropdown user-settings">
								<a href="#" id="userSettings" data-toggle="dropdown" aria-haspopup="true">
									<img src="img/user2.png" class="user-avatar" alt="Avatar">
								</a>
								<div class="dropdown-menu dropdown-menu-right" aria-labelledby="userSettings">
									<div class="header-profile-actions">
										<div class="header-user-profile">
											<div class="header-user">
												<img src="img/user2.png" alt="Admin Template">
											</div>
											<h5>Yuki Hayashi</h5>
											<p>Super User</p>
										</div>
										<a href="user-profile.html"><i class="icon-user1"></i> My Profile</a>
										<a href="account-settings.html"><i class="icon-settings1"></i> Account Settings</a>
										<a href="login.html"><i class="icon-log-out1"></i> Sign Out</a>
									</div>
								</div>
							</li>
						</ul>						
						<!-- Header actions end -->
					</div>
				</header>
				<!-- Header end -->

				<!-- Main container start -->
				<div class="main-container">

					<!-- Page header start -->
					<div class="page-header">
						
						<!-- Breadcrumb start -->
						<ol class="breadcrumb">
							<li class="breadcrumb-item">Grid Documentation</li>
						</ol>
						<!-- Breadcrumb end -->

						<!-- App actions start -->
						<div class="app-actions">
							<button type="button" class="btn">Today</button>
							<button type="button" class="btn">Yesterday</button>
							<button type="button" class="btn">7 days</button>
							<button type="button" class="btn">15 days</button>
							<button type="button" class="btn active">30 days</button>
						</div>
						<!-- App actions end -->

					</div>
					<!-- Page header end -->

					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-body">
									<h2>How it Works?</h2>
									<p>Bootstrap’s grid system uses a series of containers, rows, and columns to layout and align content. It’s built with flexbox and is fully responsive. Below is an example and an in-depth look at how the grid comes together.</p>
								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->



					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Columns and gutters</div>
									<div class="card-sub-title">
										Number of Default Columns = 12 <br/>
										Column Default Gutter Width = 30px
									</div>
									<div class="card-title mt-5">Grid options</div>
									<div class="card-sub-title">
										While Bootstrap uses ems or rems for defining most sizes, pxs are used for grid breakpoints and container widths. This is because the viewport width is in pixels and does not change with the font size. See how aspects of the Bootstrap grid system work across multiple devices with a handy table
									</div>
								</div>
								<div class="card-body">

									<table class="table">
									  <thead>
									    <tr>
									      <th></th>
									      <th class="text-center">
									        Extra small<br>
									        <small>&lt;576px</small>
									      </th>
									      <th class="text-center">
									        Small<br>
									        <small>≥576px</small>
									      </th>
									      <th class="text-center">
									        Medium<br>
									        <small>≥768px</small>
									      </th>
									      <th class="text-center">
									        Large<br>
									        <small>≥992px</small>
									      </th>
									      <th class="text-center">
									        Extra large<br>
									        <small>≥1200px</small>
									      </th>
									    </tr>
									  </thead>
									  <tbody>
									    <tr>
									      <td class="text-nowrap" scope="row">Max container width</td>
									      <td>None (auto)</td>
									      <td>540px</td>
									      <td>720px</td>
									      <td>960px</td>
									      <td>1140px</td>
									    </tr>
									    <tr>
									      <td class="text-nowrap" scope="row">Class prefix</td>
									      <td><code>.col-</code></td>
									      <td><code>.col-sm-</code></td>
									      <td><code>.col-md-</code></td>
									      <td><code>.col-lg-</code></td>
									      <td><code>.col-xl-</code></td>
									    </tr>
									    <tr>
									      <td class="text-nowrap" scope="row"># of columns</td>
									      <td colspan="5">12</td>
									    </tr>
									    <tr>
									      <td class="text-nowrap" scope="row">Gutter width</td>
									      <td colspan="5">30px (15px on each side of a column)</td>
									    </tr>
									    <tr>
									      <td class="text-nowrap" scope="row">Nestable</td>
									      <td colspan="5">Yes</td>
									    </tr>
									    <tr>
									      <td class="text-nowrap" scope="row">Column ordering</td>
									      <td colspan="5">Yes</td>
									    </tr>
									  </tbody>
									</table>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Equal-width</div>
									<div class="card-sub-title">
										For example, here are two grid layouts that apply to every device and viewport, from xs to xl. Add any number of unit-less classes for each breakpoint you need and every column will be the same width.
									</div>
								</div>
								<div class="card-body">

									<div class="grid-container">
										<div class="row">
											<div class="col">
												<div class="column">
													1 of 2
												</div>
											</div>
											<div class="col">
												<div class="column">
													2 of 2
												</div>
											</div>
											<div class="col">
												<div class="column">
													3 of 3
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>1 of 2<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>2 of 2<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>3 of 3<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span></code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->



					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Columns can be broken into multiple lines</div>
									<div class="card-sub-title">
										Equal-width columns can be broken into multiple lines, but there was a Safari flexbox bug that prevented this from working without an explicit flex-basis or border. There are workarounds for older browser versions, but they shouldn’t be necessary if you’re up-to-date.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row">
											<div class="col">
												<div class="column">
													Column
												</div>
											</div>
											<div class="col">
												<div class="column">
													Column
												</div>
											</div>
											<div class="w-100"></div>
											<div class="col">
												<div class="column">
													Column
												</div>
											</div>
											<div class="col">
												<div class="column">
													Column
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"w-100"</span><span class="nt">&gt;&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>Column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span></code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->



					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Setting one column width</div>
									<div class="card-sub-title">
										Auto-layout for flexbox grid columns also means you can set the width of one column and have the sibling columns automatically resize around it. You may use predefined grid classes (as shown below), grid mixins, or inline widths. Note that the other columns will resize no matter the width of the center column.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row">
											<div class="col">
												<div class="column">
													1 of 3
												</div>
											</div>
											<div class="col-6">
												<div class="column">
													2 of 3 (wider)
												</div>
											</div>
											<div class="col">
												<div class="column">
													3 of 3
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>1 of 3<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6"</span><span class="nt">&gt;</span>2 of 3 (wider)<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>3 of 3<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Variable width content</div>
									<div class="card-sub-title">
										Use col-{breakpoint}-auto classes to size columns based on the natural width of their content.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row">
											<div class="col-lg-2">
												<div class="column">
													1 of 3
												</div>
											</div>
											<div class="col-auto">
												<div class="column">
													Auto width
												</div>
											</div>
											<div class="col-lg-2">
												<div class="column">
													3 of 3
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-lg-2"</span><span class="nt">&gt;</span>1 of 3<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-auto"</span><span class="nt">&gt;</span>Auto width<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-lg-2"</span><span class="nt">&gt;</span>3 of 3<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Variable width content</div>
									<div class="card-sub-title">
										Use col-{breakpoint}-auto classes to size columns based on the natural width of their content.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row">
									    <div class="col">
									      <div class="column">
													1 of 3
												</div>
									    </div>
									    <div class="col-auto">
									    	<div class="column">
													Auto width
												</div>
									    </div>
									    <div class="col-lg-2">
									      <div class="column">
													3 of 3
												</div>
									    </div>
									  </div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>1 of 3<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-auto"</span><span class="nt">&gt;</span>Auto width<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-lg-2"</span><span class="nt">&gt;</span>3 of 3<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Breakpoints</div>
									<div class="card-sub-title">
										For grids that are the same from the smallest of devices to the largest, use the .col and .col-* classes. Specify a numbered class when you need a particularly sized column; otherwise, feel free to stick to .col
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row">
											<div class="col">
												<div class="column">
													col
												</div>
											</div>
											<div class="col">
												<div class="column">
													col
												</div>
											</div>
											<div class="col">
												<div class="column">
													col
												</div>
											</div>
											<div class="col">
												<div class="column">
													col
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Breakpoints</div>
									<div class="card-sub-title">
										For grids that are the same from the smallest of devices to the largest, use the .col and .col-* classes. Specify a numbered class when you need a particularly sized column; otherwise, feel free to stick to .col
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row">
											<div class="col-2">
												<div class="column">
													col-2
												</div>
											</div>
											<div class="col-4">
												<div class="column">
													col-4
												</div>
											</div>
											<div class="col-4">
												<div class="column">
													col-4
												</div>
											</div>
											<div class="col-2">
												<div class="column">
													col-2
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-2"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Align items start</div>
									<div class="card-sub-title">
										Use flexbox alignment utilities to vertically and horizontally align columns. Internet Explorer 10-11 do not support vertical alignment of flex items when the flex container has a min-height as shown below.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row align-items-start h-150">
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row align-items-start h-150"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Align items center</div>
									<div class="card-sub-title">
										Use flexbox alignment utilities to vertically and horizontally align columns. Internet Explorer 10-11 do not support vertical alignment of flex items when the flex container has a min-height as shown below.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row align-items-center h-150">
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row align-items-center h-150"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>

						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Align items end</div>
									<div class="card-sub-title">
										Use flexbox alignment utilities to vertically and horizontally align columns. Internet Explorer 10-11 do not support vertical alignment of flex items when the flex container has a min-height as shown below.
									</div>
								</div>
								<div class="card-body">
									
									<div class="grid-container">
										<div class="row align-items-end h-150">
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col">
												<div class="column">
													Col
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row align-items-end h-150"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Vertical Alignment</div>
									<div class="card-sub-title">
										Use flexbox alignment utilities to vertically and horizontally align columns. Internet Explorer 10-11 do not support vertical alignment of flex items when the flex container has a min-height as shown below.
									</div>
								</div>
								<div class="card-body">
									<div class="grid-container">
										<div class="row h-150">
											<div class="col align-self-start">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col align-self-center">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col align-self-end">
												<div class="column">
													Col
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row h-150"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col align-items-start"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col align-items-center"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col align-items-end"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Horizontal Alignment</div>
									<div class="card-sub-title">
										Use flexbox alignment utilities to vertically and horizontally align columns. Internet Explorer 10-11 do not support vertical alignment of flex items when the flex container has a min-height as shown below.
									</div>
								</div>
								<div class="card-body">
									

									<div class="row gutters">
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											
											<div class="card">
												<div class="card-body">
													<div class="grid-container">
														<div class="row justify-content-start">
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row justify-content-start"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
												</div>
											</div>

										</div>
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											
											<div class="card">
												<div class="card-body">
													<div class="grid-container">
														<div class="row justify-content-center">
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row justify-content-center"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
												</div>
											</div>											
										</div>
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<div class="card">
												<div class="card-body">
													<div class="grid-container">
														<div class="row justify-content-end">
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row justify-content-end"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
												</div>
											</div>
										</div>
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

											<div class="card">
												<div class="card-body">
													<div class="grid-container">
														<div class="row justify-content-around">
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row justify-content-around"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
												</div>
											</div>

										</div>
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<div class="card m-0">
												<div class="card-body">
													<div class="grid-container">
														<div class="row justify-content-between">
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
															<div class="col-4">
																<div class="column">
																	Col 4
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row justify-content-between"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
												</div>
											</div>
										</div>
									</div>
									<!-- Row end -->


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">No gutters</div>
									<div class="card-sub-title">
										The gutters between columns in our predefined grid classes can be removed with .no-gutters. This removes the negative margins from .row and the horizontal padding from all immediate children columns.
									</div>
								</div>
								<div class="card-body">
									<div class="grid-container">
										<div class="row no-gutters">
											<div class="col-8">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col-4">
												<div class="column">
													Col
												</div>
											</div>
										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row no-gutters"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-8"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->





					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Column breaks</div>
									<div class="card-sub-title">
										Breaking columns to a new line in flexbox requires a small hack: add an element with width: 100% wherever you want to wrap your columns to a new line. Normally this is accomplished with multiple .rows, but not every implementation method can account for this.
									</div>
								</div>
								<div class="card-body">
									<div class="grid-container">
										<div class="row">
											<div class="col-4">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col-4">
												<div class="column">
													Col
												</div>
											</div>

											<!-- Force next columns to break to new line -->
    									<div class="w-100"></div>

    									<div class="col-4">
												<div class="column">
													Col
												</div>
											</div>
											<div class="col-4">
												<div class="column">
													Col
												</div>
											</div>

										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>

  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"w-100"</span><span class="nt">&gt;</span>.w-100 Force next columns to break to new line<span class="nt">&lt;/div&gt;</span>

  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

								</div>
							</div>
							<!-- Card end -->

						</div>
						<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Column break at specific breakpoints</div>
									<div class="card-sub-title">
										Breaking columns to a new line in flexbox requires a small hack: add an element with width: 100% wherever you want to wrap your columns to a new line. Normally this is accomplished with multiple .rows, but not every implementation method can account for this.
									</div>
								</div>
								<div class="card-body">
											
									<div class="grid-container">
										<div class="row">
											<div class="col-6 col-sm-3">
												<div class="column">
													Column
												</div>
											</div>
											<div class="col-6 col-sm-3">
												<div class="column">
													Column
												</div>
											</div>

											<!-- Force next columns to break to new line at md breakpoint and up -->
    									<div class="w-100 d-none d-md-block"></div>

    									<div class="col-6 col-sm-3">
												<div class="column">
													Column
												</div>
											</div>
											<div class="col-6 col-sm-3">
												<div class="column">
													Column
												</div>
											</div>

										</div>
									</div>

									<!-- Code Syntax -->
									<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6 col-sm-3"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6 col-sm-3"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>

  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"w-100 d-none d-md-block"</span><span class="nt">&gt;</span>.w-100 Force next columns to break to new line at md breakpoint and up<span class="nt">&lt;/div&gt;</span>

  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-6"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->




					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Offsetting columns</div>
									<div class="card-sub-title">
										You can offset grid columns in two ways: our responsive .offset- grid classes and our margin utilities. Grid classes are sized to match columns while margins are more useful for quick layouts where the width of the offset is variable.
									</div>
								</div>
								<div class="card-body">
											
									<div class="row gutters">
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<div class="card">
												<div class="card-body">
													
													<div class="grid-container">
														<div class="row">
															<div class="col-md-4">
																<div class="column">
																	col-md-4
																</div>
															</div>
															<div class="col-md-4 offset-md-4">
																<div class="column">
																	col-md-4 offset-md-4
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-4 offset-md-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

												</div>
											</div>
										</div>
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<div class="card">
												<div class="card-body">
													
													<div class="grid-container">
														<div class="row">
															<div class="col-md-3 offset-md-3">
																<div class="column">
																	col-md-3 offset-md-3
																</div>
															</div>
															<div class="col-md-3 offset-md-3">
																<div class="column">
																	col-md-3 offset-md-3
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-3 offset-md-3"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-3 offset-md-3"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

												</div>
											</div>
										</div>
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<div class="card">
												<div class="card-body">
													
													<div class="grid-container">
														<div class="row">
															<div class="col-md-6 offset-md-3">
																<div class="column">
																	col-md-6 offset-md-3
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-6 offset-md-3"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

												</div>
											</div>
										</div>
									</div>

								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->







					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">Margin utilities</div>
									<div class="card-sub-title">
										With the move to flexbox in v4, you can use margin utilities like .mr-auto to force sibling columns away from one another.


									</div>
								</div>
								<div class="card-body">
									
									<div class="row gutters">
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<div class="card">
												<div class="card-body">
													
													<div class="grid-container">
														<div class="row">
															<div class="col-md-4">
																<div class="column">
																	.col-md-4
																</div>
															</div>
															<div class="col-md-4 ml-auto">
																<div class="column">
																	.col-md-4 .ml-auto
																</div>
															</div>
														</div>
													</div>

													<!-- Code Syntax -->
													<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-4"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-4 ml-auto"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
											
											</div>
										</div>
									</div>


									<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
										<div class="card">
											<div class="card-body">
												
												<div class="grid-container">
													<div class="row">
														<div class="col-md-5 ml-md-auto">
															<div class="column">
																.col-md-5 .ml-md-auto
															</div>
														</div>
														<div class="col-md-5 ml-md-auto">
															<div class="column">
																.col-md-5 .ml-md-auto
															</div>
														</div>
													</div>
												</div>

												<!-- Code Syntax -->
												<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-5 ml-md-auto"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-md-5 ml-md-auto"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>
											</div>
										</div>
									</div>


									<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
										<div class="card">
											<div class="card-body">
												
												<div class="grid-container">
													<div class="row">
														<div class="col-auto mr-auto">
															<div class="column">
																.col-auto .mr-auto
															</div>
														</div>
														<div class="col-auto">
															<div class="column">
																.col-auto
															</div>
														</div>
													</div>
												</div>

												<!-- Code Syntax -->
												<figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"row"</span><span class="nt">&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-auto mr-auto"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
	<span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"col-auto"</span><span class="nt">&gt;</span>column<span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></figure>

												</div>
											</div>
										</div>
									</div>							


								</div>
							</div>
							<!-- Card end -->

						</div>
					</div>
					<!-- Row end -->

				</div>
				<!-- Main container end -->

				<!-- Container fluid start -->
				<div class="container-fluid">
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-12">
							<!-- Footer start -->
							<div class="footer">
								Copyright <a href="http://www.bootstrapmb.com">Tycoon</a> Admin 2020
							</div>
							<!-- Footer end -->
						</div>
					</div>
					<!-- Row end -->
				</div>
				<!-- Container fluid end -->

			</div>
			<!-- Page content end -->

		</div>
		<!-- Page wrapper end -->

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>


		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>
		
		<!-- Prism -->
		<script src="vendor/prism/prism.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>