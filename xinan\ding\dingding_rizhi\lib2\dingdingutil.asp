<!--#include file="json.asp"-->
<!--#include file="functions.asp"-->
<%
	'Method: POST或者GET
	'HttpUrl: URL
	'data: 请求参数
	Function PostHttpPageJSON(Method, HttpUrl, data)
		dim rststr
		rststr=PostHttpPage(Method,HttpUrl,data)
		LogMessage "Method -> " & Method & " / HttpUrl -> " & HttpUrl
		'LogMessage "Response2 -> " & rststr
		'response.write rststr
		'response.end
		Set PostHttpPageJSON = New aspJSON
		PostHttpPageJSON.loadJSON(rststr)
	End Function


'Method: POST或者GET
'HttpUrl: URL
'data: 请求参数
Function PostHttpPageJSONWithHeader(method, httpUrl, accessToken, data)
    LogMessage "PostHttpPageJSONWithHeader"
    dim rststr
    rststr=PostHttpPageWithHeader(Method,HttpUrl,accessToken,data)
    LogMessage "Method -> " & Method & " / HttpUrl -> " & HttpUrl
    'LogMessage "Response2 -> " & rststr
    'response.write rststr
    'response.end
    Set PostHttpPageJSONWithHeader = New aspJSON
    PostHttpPageJSONWithHeader.loadJSON(rststr)
End Function

	'获取access_token
	Function GetAccessToken(cid,csceret,akey,asecret)
		LogMessage "corpid -> " & cid & " / corpsecret -> " & "???" & " / appkey -> " & akey & " / appsecret -> ??? "
		if cid<>"" and csceret <> "" then
			Set snstokenObj = PostHttpPageJSON("GET","https://oapi.dingtalk.com/gettoken?corpid=" & cid & "&corpsecret=" & csceret,"")
			if snstokenObj.data("errcode")=0 then
				GetAccessToken = snstokenObj.data("access_token")
			end if
		else
			Set snstokenObj = PostHttpPageJSON("GET","https://oapi.dingtalk.com/gettoken?appkey=" & akey & "&appsecret=" & asecret,"")
			GetAccessToken = snstokenObj.data("access_token")
		end if
	End Function

	'获取某个审批流程实例的详细信息A
	Function GetInstanceDetailJSON(instanceId,access_token)
		dim detail_url
		dim detail_data
		detail_data="{""process_instance_id"":""" & instanceId & """}"
		detail_url = "https://oapi.dingtalk.com/topapi/processinstance/get?access_token="&access_token
		Set GetInstanceDetailJSON = PostHttpPageJSON("POST",detail_url,detail_data)
		errorCode = GetInstanceDetailJSON.data("errcode")
		if errorCode = 0 then
			With GetInstanceDetailJSON.data("process_instance")
				.Add "instance_id", instanceId
			End With
			dim originator_userid
			'查询申请人姓名
			originator_userid = GetInstanceDetailJSON.data("process_instance").item("originator_userid")
			Set userinfo = PostHttpPageJSON("GET","https://oapi.dingtalk.com/user/get?access_token="& access_token & "&userid=" & originator_userid,"")
			if userinfo.data("errcode") = 0 then
				With GetInstanceDetailJSON.data("process_instance")
					.Add "originator_username", userinfo.data("name")
				End With
			end if
		end if
	End Function

	'提交钉钉流程
	'=======参数=======
	'processCode         流程模板CODE
	'formComponentValues 钉钉表单参数
	'originatorUserId    钉钉用户id
	'deptId              钉钉部门id
	'access_token        access_token
	'=======返回=======
	'errorcode        0表示成功，其他表示失败
	'errmsg          失败原因
	Function SubmitProcess(processCode,formComponentValues,originatorUserId,deptId,access_token)
		dim processinstance_url
		dim processinstance_data
		processinstance_url = "https://oapi.dingtalk.com/topapi/processinstance/create?access_token="&access_token

		Set params = New aspJSON
		params.loadJSON("{""form_component_values"":" & formComponentValues & "}")

		Set processinstance_data = New aspJSON
		With processinstance_data.data
			.Add "process_code", processCode
			.Add "originator_user_id", originatorUserId
			.Add "dept_id", deptId
			.Add "form_component_values", params.data("form_component_values")
		End With

		Set SubmitProcess = PostHttpPageJSON("POST",processinstance_url,processinstance_data.JSONoutput())
	End Function

	'批量获取审批实例id
	'processCode         流程模板CODE
	'params              请求参数
	'access_token        access_token
	Function ListProcessInstances(processCode,params,access_token)
		dim processinstance_list_url
		processinstance_list_url = "https://oapi.dingtalk.com/topapi/processinstance/listids?access_token="&access_token
		Set ListProcessInstances = PostHttpPageJSON("POST",processinstance_list_url,params.JSONoutput())
	End Function

	'注册回调接口
	'params              请求参数
	'access_token        access_token
	Function RegisterCallBackUrl(params,access_token)
		dim register_call_back_url
		register_call_back_url = "https://oapi.dingtalk.com/call_back/register_call_back?access_token="&access_token
		Set RegisterCallBackUrl = PostHttpPageJSON("POST",register_call_back_url,params.JSONoutput())
	End Function

	'删除回调接口
	'access_token        access_token
	Function DeleteCallBackUrl(access_token)
		dim delete_call_back_url
		delete_call_back_url = "https://oapi.dingtalk.com/call_back/delete_call_back?access_token="&access_token
		Set DeleteCallBackUrl = PostHttpPageJSON("GET",delete_call_back_url,"")
	End Function

	'获取用户信息
	'access_token        access_token
	Function GetUserInfo(userId,access_token)
		dim url
		url = "https://oapi.dingtalk.com/user/get?access_token=" & access_token & "&userid=" & userId
		Set GetUserInfo = PostHttpPageJSON("GET",url,"")
		LogMessage GetUserInfo.JSONoutput()
	End Function

	'获取部门信息
	'access_token        access_token
	Function GetDeptInfo(deptId,access_token)
		dim url
		url = "https://oapi.dingtalk.com/department/get?access_token=" & access_token & "&id=" & deptId
		Set GetDeptInfo = PostHttpPageJSON("GET",url,"")
	End Function

	'获取角色信息'
	Function GetJueSeInfo(access_token)
	dim url
		url = "https://oapi.dingtalk.com/topapi/role/list?access_token=" & access_token
		Set GetJueSeInfo = PostHttpPageJSON("post",url,"")
	end Function

	'获取角色下的员工列表
	Function GetRolePeopleList(role_id,access_token)
		dim url
		url = "https://oapi.dingtalk.com/topapi/role/simplelist?access_token=" & access_token & "&role_id=" & role_id
		Set GetRolePeopleList = PostHttpPageJSON("post",url,"")
	end Function

	'获取表单元素值
	Function GetFormValue(formComponentValues,formItemName)
		Set params = New aspJSON
		dim itemvalue
		itemvalue=""
		params.loadJSON("{""form_component_values"":" & formComponentValues & "}")
		For Each itemIndex In params.data("form_component_values")
			Set paramItem = params.data("form_component_values").item(itemIndex)
			if formItemName = paramItem.item("name") then
				itemvalue = paramItem.item("value")
				exit for
			end if
		Next
		GetFormValue = itemvalue
	end Function

	'批量查询钉钉日志
	 'params              请求参数
	 'access_token        access_token
	 Function ListLogs(params,access_token)
		dim log_list_url
	    log_list_url = "https://oapi.dingtalk.com/topapi/report/list?access_token=" & access_token
	    Set ListLogs = PostHttpPageJSON("POST",log_list_url,params.JSONoutput())
	 End Function

	'根据指定条件查询跟进记录数据
	'params              请求参数
	'access_token        access_token
	Function FollowrecordQuery(params,access_token)
		dim url
		url = "https://oapi.dingtalk.com/topapi/crm/objectdata/followrecord/query?access_token=" & access_token
		LogMessage "FollowrecordQuery url -> " & url
		Set FollowrecordQuery = PostHttpPageJSON("POST",url,params.JSONoutput())
	End Function

	'根据手机号查询钉钉用户ID
	'params 参数
	'access_token token
	Function QueryUserInfo(params,access_token)
		dim queryUrl
		queryUrl = "https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=" & access_token
		LogMessage "QueryUserByMobile url -> " & queryUrl
		Set QueryUserInfo = PostHttpPageJSON("POST", queryUrl, params.JSONoutput())
		'response.write QueryUserInfo.JSONoutput()
		'response.end
		LogMessage QueryUserInfo.JSONoutput()
		'response.end
	End Function

%>
