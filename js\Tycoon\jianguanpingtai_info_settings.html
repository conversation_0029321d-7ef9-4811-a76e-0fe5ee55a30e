﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>基本信息修改</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">
		<!-- Chat css -->
		<link rel="stylesheet" href="css/chat.css">
			<!-- Data Tables -->
			<link rel="stylesheet" href="vendor/datatables/dataTables.bs4.css" />
			<link rel="stylesheet" href="vendor/datatables/dataTables.bs4-custom.css" />
			<link href="vendor/datatables/buttons.bs.css" rel="stylesheet" />

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
	</head>

	<body>

		<!-- Loading starts -->
		<div id="loading-wrapper">
			<div class="spinner-border" role="status">
				<span class="sr-only">Loading...</span>
			</div>
		</div>
		<!-- Loading ends -->
		
	
			
			<!-- Page content start  -->
			<div class="page-content">				
				
				
				<!-- Main container start -->
				<div class="main-container">
					
				
					
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">
										<h5>基本信息修改</h5>
									</div>
								</div>
								<div class="card-body">
									<form>
										<div class="form-group">
											<label>单位LOGO</label>
											<input class="form-control">
										</div>
										<div class="form-group">
											<label>单位全称</label>
											<input class="form-control" value="原单位名字">
										</div>
										<div class="form-group">
											<label>单位简称</label>
											<input class="form-control">
										</div>
										<button class="btn btn-info">提交</button>
									</form>
								</div>
							</div>
						</div>
						
					</div>
					<!-- Row end -->
					
					
					
					
					
					
					
					

				</div>
				<!-- Main container end -->

			
				

				
			</div>
			<!-- Page content end -->

	
			

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>
		<!-- Data Tables -->
		<script src="vendor/datatables/dataTables.min.js"></script>
		<script src="vendor/datatables/dataTables.bootstrap.min.js"></script>

		<!-- Custom Data tables -->
		<script src="vendor/datatables/custom/custom-datatables.js"></script>
		<script src="vendor/datatables/custom/fixedHeader.js"></script>

		<!-- Download / CSV / Copy / Print -->
		<script src="vendor/datatables/buttons.min.js"></script>
		<script src="vendor/datatables/jszip.min.js"></script>
		<script src="vendor/datatables/vfs_fonts.js"></script>
		<script src="vendor/datatables/html5.min.js"></script>
		<script src="vendor/datatables/buttons.print.min.js"></script>

		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>

		<!-- Polyfill JS -->
		<script src="vendor/polyfill/polyfill.min.js"></script>
		<script src="vendor/polyfill/class-list.min.js"></script>

		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		
		<!-- Peity Charts -->
		<script src="vendor/peity/peity.min.js"></script>
		<script src="vendor/peity/custom-peity.js"></script>
		
		<!-- Circleful Charts -->
		<script src="vendor/circliful/circliful.min.js"></script>
		<script src="vendor/circliful/circliful.custom.js"></script>
		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>

		

		<!-- Bar Graphs -->
		<script src="vendor/apex/examples/bar/basic-bar-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-graph-grouped.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph-full-width.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-negative-values.js"></script>

		<!-- Candlestick Graphs -->
		<script src="vendor/apex/examples/candlestick/basic-candlestick-graph.js"></script>

		<!-- Column Graphs -->
		<script src="vendor/apex/examples/column/basic-column-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-graph-datalables.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph-fullheight.js"></script>

		<!-- Line Graphs -->
		<script src="vendor/apex/examples/line/basic-line-graph.js"></script>
		<script src="vendor/apex/examples/line/line-with-data-labels.js"></script>
		<script src="vendor/apex/examples/line/stepline.js"></script>

		<!-- Donut Graphs -->
		<script src="vendor/apex/examples/pie/basic-donut-graph.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>