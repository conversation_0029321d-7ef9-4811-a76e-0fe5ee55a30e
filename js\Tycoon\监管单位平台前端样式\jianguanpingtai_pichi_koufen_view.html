﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>检查扣分分析</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">
		<!-- Chat css -->
		<link rel="stylesheet" href="css/chat.css">
			<!-- Data Tables -->
			<link rel="stylesheet" href="vendor/datatables/dataTables.bs4.css" />
			<link rel="stylesheet" href="vendor/datatables/dataTables.bs4-custom.css" />
			<link href="vendor/datatables/buttons.bs.css" rel="stylesheet" />

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
	</head>

	<body>
			<!-- Page content start  -->
			<div class="page-content">				
				<!-- Main container start -->
				<div class="main-container">
					
					<!-- Row start -->
					<div class="row gutters">
						
					
					
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							
							

							<div class="card">
								<div class="card-header">
									<div class="card-title">本次检查得分列表</div>
								</div>
								<div class="card-body">
										<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
											<table id="copy-print-csv" class="table custom-table">
												<thead>
													<tr>
														<th>序号</th>
														<th>检查日期</th>
														<th>检查单位</th>
														<th>得分</th>
														<th>操作</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td>1</td>
														<td>2022年5月4日</td>
														<td>医院乙</td>
														<td>100分</td>
														<td>
															<a href="#" class="btn btn-info btn-sm btn-rounded"  data-original-title="修改">
																<i class="icon-edit"></i>修改
															</a>
																<a href="#" class="btn btn-danger btn-sm btn-rounded"  data-original-title="删除">
																	<i class="icon-cancel"></i>删除
																</a>
														</td>
													</tr>
													<tr>
														<td>1</td>
														<td>2022年5月4日</td>
														<td>医院乙</td>
														<td>100分</td>
														<td>
															<a href="#" class="btn btn-info btn-sm btn-rounded"  data-original-title="修改">
																<i class="icon-edit"></i>修改
															</a>
																<a href="#" class="btn btn-danger btn-sm btn-rounded"  data-original-title="删除">
																	<i class="icon-cancel"></i>删除
																</a>
														</td>
													</tr><tr>
														<td>1</td>
														<td>2022年5月4日</td>
														<td>医院乙</td>
														<td>100分</td>
														<td>
															<a href="#" class="btn btn-info btn-sm btn-rounded"  data-original-title="修改">
																<i class="icon-edit"></i>修改
															</a>
																<a href="#" class="btn btn-danger btn-sm btn-rounded"  data-original-title="删除">
																	<i class="icon-cancel"></i>删除
																</a>
														</td>
													</tr><tr>
														<td>1</td>
														<td>2022年5月4日</td>
														<td>医院乙</td>
														<td>100分</td>
														<td>
															<a href="#" class="btn btn-info btn-sm btn-rounded"  data-original-title="修改">
																<i class="icon-edit"></i>修改
															</a>
																<a href="#" class="btn btn-danger btn-sm btn-rounded"  data-original-title="删除">
																	<i class="icon-cancel"></i>删除
																</a>
														</td>
													</tr>
													
													
												</tbody>
											</table>

											
										</div>
								</div>
							</div>



							
							
						</div>
						
					</div>
					<!-- Row end -->
				</div>
				<!-- Main container end -->
			</div>
			<!-- Page content end -->
		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>
		<!-- Data Tables -->
		<script src="vendor/datatables/dataTables.min.js"></script>
		<script src="vendor/datatables/dataTables.bootstrap.min.js"></script>

		<!-- Custom Data tables -->
		<script src="vendor/datatables/custom/custom-datatables.js"></script>
		<script src="vendor/datatables/custom/fixedHeader.js"></script>

		<!-- Download / CSV / Copy / Print -->
		<script src="vendor/datatables/buttons.min.js"></script>
		<script src="vendor/datatables/jszip.min.js"></script>
		<script src="vendor/datatables/vfs_fonts.js"></script>
		<script src="vendor/datatables/html5.min.js"></script>
		<script src="vendor/datatables/buttons.print.min.js"></script>

		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>

		<!-- Polyfill JS -->
		<script src="vendor/polyfill/polyfill.min.js"></script>
		<script src="vendor/polyfill/class-list.min.js"></script>

		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		
		<!-- Peity Charts -->
		<script src="vendor/peity/peity.min.js"></script>
		<script src="vendor/peity/custom-peity.js"></script>
		
		<!-- Circleful Charts -->
		<script src="vendor/circliful/circliful.min.js"></script>
		<script src="vendor/circliful/circliful.custom.js"></script>
		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>

		

		<!-- Bar Graphs -->
		<script src="vendor/apex/examples/bar/basic-bar-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-graph-grouped.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph-full-width.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-negative-values.js"></script>

		<!-- Candlestick Graphs -->
		<script src="vendor/apex/examples/candlestick/basic-candlestick-graph.js"></script>

		<!-- Column Graphs -->
		<script src="vendor/apex/examples/column/basic-column-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-graph-datalables.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph-fullheight.js"></script>

		<!-- Line Graphs -->
		<script src="vendor/apex/examples/line/basic-line-graph.js"></script>
		<script src="vendor/apex/examples/line/line-with-data-labels.js"></script>
		<script src="vendor/apex/examples/line/stepline.js"></script>

		<!-- Donut Graphs -->
		<script src="vendor/apex/examples/pie/basic-donut-graph.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>