<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../../upload2019/lib/uploadutil.asp"-->
<!--#include file="../../shujuku.asp"-->


<% 
if  session("name")="" then
 %>

对不起你还没有登陆，请<a href="../../login.asp">登陆</a>
<%
else 
%>
<% 
qread=""
qadd=""
qedit=""
qdel=""
sql="select * from 准入控制管理系统 where 用户='"&session("mingzhi")&"' and (系统名称='票据系统' )"
rs.open sql,conn,1,2
if not rs.eof then 
	qread=rs("可读取权限")
	qadd=rs("可添加权限")
	qedit=rs("可修改权限")
	qdel=rs("可删除权限")
end if
rs.close
'给管理员设权限
 if session("group")="vip" then
	 qread=true
	 qadd=true
	 qedit=true
	 qdel=true
 end if
if qread=false or qread="" then 
response.write "<script language='javascript'>"
	response.write "alert('对不起，你没有该系统的准入权限！');"
	response.write "location.href='javascript:history.go(-1)';"
	response.write "</script>"
	response.end
	
end if
  

		Response.Charset="UTF-8"
		Response.ContentType="application/json"
	   	Dim f_Name,f_SaveName,f_Path,f_Size,f_Ext,f_Err,f_Save,f_Time
		dim FileUpload , FormName
		FormName = "ssi-upload"'文件域名称
		set FileUpload = New UpLoadClass
		FileUpload.Charset="UTF-8"
		'FileUpload.SavePath="upload/" '项目相对路径，此处表示应用程序目录下的upload文件夹
		FileUpload.SavePath="upfiles_caiwubu/"
		FileUpload.FileType="jpg/png/gif/txt/bmp/ps/doc/xls/ppt/docx/xlsx/pptx/pdf/et/wps/zip/rar/7z/tar/gz/jpeg"
		FileUpload.Open() '开始执行上传程序
		 
		dim name,xmid,leixing,folder,xmxitong
		
		 
		f_Err = FileUpload.Form(FormName & "_Err") '获取上传状态
		IF f_Err = 0 Then '上传成功
			f_Name = FileUpload.Form(FormName & "_Name")'原文件名
			f_SaveName = FileUpload.Form(FormName)'保存文件名
			f_Path = FileUpload.SavePath'保存路径
			f_Size = FileUpload.Form(FormName & "_Size")'文件大小
			f_Ext = FileUpload.Form(FormName & "_Ext")'文件类型
			f_Time = Now()'保存时间
			name = session("mingzhi")
			xmid=FileUpload.Form("xmid")'项目id'
			a1=FileUpload.Form("a1") 
			a2=FileUpload.Form("a2") 
			a3=FileUpload.Form("a3") 
			a5=FileUpload.Form("a5") 
			a8=FileUpload.Form("a8") 
			ysp_id=FileUpload.Form("ysp_id") 
			
			neirong=FileUpload.Form("neirong") 

			'提交到数据库里
			 
			 sql = "select top 1 * from 收发票管理"
            rsxa.open sql, connxa, 3, 2
            rsxa.addnew
			if  a1<>"" then 
            	rsxa("开票日期") = a1 ' 使用之前获取的 a1 值
			end if 
			if a2<>"" then 
           	 rsxa("开票金额") = a2 ' 使用之前获取的 a2 值
			end if 

            rsxa("单位") = a3 ' 使用之前获取的 a3 值
            rsxa("开票内容") = neirong ' 使用之前获取的 neirong 值
            rsxa("发票税种") = a5 ' 使用之前获取的 a5 值
            if ysp_id<>0 and ysp_id<>"" then 
			rsxa("应收发票id")=ysp_id
			end if 
          	rsxa("发票号") = a8
            
            rsxa("imgUrl") = "upfiles_caiwubu/"&f_SaveName
            rsxa("录入人") = session("mingzhi")
            rsxa("录入时间") = now()
            
            If xmid <> "" Then ' 使用之前获取的 xmid 值
                rsxa("lt合同id") = xmid
            Else
                rsxa("lt合同id") = Null
            End If

            rsxa.update
            rsxa.close

			'在自动添加到合同文件柜里
			sql="select top 1 * from 合同管理_20_财务文件柜"
			rserp.open sql,connerp,3,2
			rserp.addnew
			rserp("文件名")=a3&"开的发票金额"&a2
			rserp("分类")="财务-发票与收据"
			rserp("文件说明")=NUll
			rserp("文件大小")=f_Size
			rserp("内部url")="../xinan/shoufapiao/upfiles_caiwubu/"&f_SaveName
			rserp("文件类型")=f_Ext
			rserp("录入人")=session("mingzhi")
			rserp("录入时间")=now()
			rserp("项目管理表id")=xmid
			rserp.update
			rserp.close




			 
			'response.write "<script language='javascript'>alert('添加成功！');location.href='YYB_CCB_shoufapiao.asp';</script>"
			response.end

		Else 
			Response.Write("{""err"":" & f_Err & "}") '返回上传失败原因
		End IF
		set FileUpload = nothing

 
  

   
 

 

 
%>
 
 
<%end if%>