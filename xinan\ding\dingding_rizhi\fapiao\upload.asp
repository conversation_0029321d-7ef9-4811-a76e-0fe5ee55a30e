<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="uploadutil.asp"-->
<%
' 确保上传目录存在
Dim objFSO
Set objFSO = Server.CreateObject("Scripting.FileSystemObject")
Dim uploadDir
' 修改为实际使用的上传目录，与FileUpload.SavePath保持一致
uploadDir = Server.MapPath("/xinan/shoufapiao/upfiles_caiwubu")
WriteLog "上传目录路径: " & uploadDir

' 检查目录是否存在
On Error Resume Next
Dim folderExists
folderExists = objFSO.FolderExists(uploadDir)
If Err.Number <> 0 Then
    WriteLog "检查目录存在失败: " & Err.Description & ", 错误代码: " & Err.Number
    Response.Write "{""success"":false,""error"":""检查目录失败: " & Err.Description & ", 错误代码: " & Err.Number & """}"
    Response.End
End If
On Error Goto 0

If Not folderExists Then
    ' 尝试创建目录
    On Error Resume Next
    WriteLog "尝试创建目录: " & uploadDir
    
    ' 先检查父目录是否存在
    Dim parentFolder
    parentFolder = objFSO.GetParentFolderName(uploadDir)
    WriteLog "父目录: " & parentFolder
    
    If Not objFSO.FolderExists(parentFolder) Then
        WriteLog "父目录不存在，尝试创建父目录"
        objFSO.CreateFolder(parentFolder)
        If Err.Number <> 0 Then
            WriteLog "创建父目录失败: " & Err.Description & ", 错误代码: " & Err.Number
            Response.Write "{""success"":false,""error"":""创建父目录失败: " & Err.Description & ", 错误代码: " & Err.Number & """}"
            Response.End
        End If
    End If
    
    ' 创建目标目录
    objFSO.CreateFolder(uploadDir)
    If Err.Number <> 0 Then
        WriteLog "创建目录失败: " & Err.Description & ", 错误代码: " & Err.Number
        Response.Write "{""success"":false,""error"":""创建目录失败: " & Err.Description & ", 错误代码: " & Err.Number & """}"
        Response.End
    End If
    WriteLog "成功创建上传目录"
    On Error Goto 0
End If

Set objFSO = Nothing

' 检查是否是表单提交
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    ' 设置响应为JSON格式
    Response.Charset="UTF-8"
    Response.ContentType="application/json"
    
    Dim f_Name,f_SaveName,f_Path,f_Size,f_Ext,f_Err,f_Save,f_Time
    dim FileUpload, FormName
    FormName = "fileToUpload"'文件域名称
    set FileUpload = New UpLoadClass
    FileUpload.Charset="UTF-8"
    FileUpload.SavePath="/xinan/shoufapiao/upfiles_caiwubu/"
    ' 设置最大文件大小为 50MB
    FileUpload.MaxSize = 50 * 1024 * 1024
    FileUpload.FileType="jpg/png/gif/txt/bmp/ps/doc/xls/ppt/docx/xlsx/pptx/pdf/et/wps/zip/rar/7z/tar/gz/jpeg"
    
    ' 添加错误处理
    On Error Resume Next
    FileUpload.Open() '开始执行上传程序
    If Err.Number <> 0 Then
        Response.Write "{""error"":""上传初始化失败: " & Err.Description & """}"
        Response.End
    End If
    On Error Goto 0

    f_Err = FileUpload.Form(FormName & "_Err") '获取上传状态
    IF f_Err = 0 Then '上传成功
        f_Name = FileUpload.Form(FormName & "_Name")'原文件名
        f_SaveName = FileUpload.Form(FormName)'保存文件名
        f_Path = FileUpload.SavePath'保存路径
        f_Size = FileUpload.Form(FormName & "_Size")'文件大小
        f_Ext = FileUpload.Form(FormName & "_Ext")'文件类型
        f_Time = Now()'保存时间

        ' 记录上传文件信息
        WriteLog "文件上传成功：" & f_Name & ", 保存为：" & f_SaveName & ", 类型：" & f_Ext & ", 大小：" & f_Size

        ' 先调用文件上传接口
        Dim uploadResult
        Set uploadResult = UploadFile(f_Path & f_SaveName)

        
        
        If uploadResult("success") Then
            ' 调用工作流程接口，传入fileId
            Dim apiResult
            Set apiResult = CallWorkflowAPI(f_Path & f_SaveName, f_Name, f_Ext, uploadResult("fileId"))
            
            ' 根据API调用结果返回响应
            If apiResult("success") Then
                Response.Write "{""success"":true,""filename"":""" & f_SaveName & """,""originalname"":""" & f_Name & """,""apiResponse"":" & apiResult("data") & "}"
            Else
                Response.Write "{""success"":false,""error"":""" & Server.HTMLEncode(apiResult("message")) & """}"
            End If
            
            Set apiResult = Nothing
        Else
            Response.Write "{""success"":false,""error"":""文件上传失败: " & Server.HTMLEncode(uploadResult("message")) & """}"
        End If
        
        Set uploadResult = Nothing
    Else
        ' 输出更详细的错误信息
        Dim errMsg
        Select Case f_Err
            Case 1
                errMsg = "文件大小超出限制（最大50MB）"
            Case 2
                errMsg = "文件类型不允许"
            Case 3
                errMsg = "文件大小超出限制且类型不允许"
            Case Else
                errMsg = "未知错误"
        End Select
        Response.Write "{""success"":false,""error"":""" & errMsg & """}"
    End If

    Set FileUpload = Nothing
    Response.End ' 确保在返回JSON后结束响应
Else
    ' 非POST请求，显示上传表单
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文件上传</title>
    <style>
        .upload-form {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .message {
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 4px solid #5cb85c;
        }
        .error {
            border-left: 4px solid #d9534f;
        }
        .file-input {
            margin: 15px 0;
        }
        .submit-btn {
            background-color: #5cb85c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .submit-btn:hover {
            background-color: #4cae4c;
        }
    </style>
</head>
<body>
    <div class="upload-form">
        <h2>文件上传</h2>
        
        <form method="post" enctype="multipart/form-data">
            <div class="file-input">
                <label for="fileToUpload">选择文件：</label>
                <input type="file" name="fileToUpload" id="fileToUpload" required>
            </div>
            <div>
                <input type="submit" value="上传文件" class="submit-btn">
            </div>
        </form>
        
        <div style="margin-top: 15px; font-size: 14px; color: #666;">
            <p>支持的文件类型：JPG、JPEG、PNG、PDF</p>
            <p>最大文件大小：5MB</p>
        </div>
    </div>
</body>
</html>
<%
End If



Function WriteLog(logMessage)
    Dim fs, logFile, logPath, dateStr
    
    ' 生成日期字符串作为日志文件名的一部分
    dateStr = Year(Date()) & Right("0" & Month(Date()), 2) & Right("0" & Day(Date()), 2)
    logPath = Server.MapPath("upload_log_" & dateStr & ".txt")
    
    ' 创建文件系统对象
    Set fs = Server.CreateObject("Scripting.FileSystemObject")
    
    On Error Resume Next
    
    ' 以追加方式打开日志文件（如果不存在则创建）
    Set logFile = fs.OpenTextFile(logPath, 8, True)
    
    If Err.Number <> 0 Then
        ' 如果打开文件失败，尝试创建新文件
        Set logFile = fs.CreateTextFile(logPath, True)
        If Err.Number <> 0 Then
            ' 如果还是失败，则退出函数
            Set fs = Nothing
            Exit Function
        End If
    End If
    
    ' 写入日志内容
    logFile.WriteLine Now() & " - " & logMessage
    
    ' 关闭文件
    logFile.Close
    Set logFile = Nothing
    Set fs = Nothing
    
    On Error Goto 0
End Function

' 解析markdown中的JSON内容
Function ExtractJsonFromMarkdown(markdownText)
    Dim startPos, endPos, jsonText
    
    ' 查找 ```json 和 ``` 之间的内容
    startPos = InStr(markdownText, "```json" & vbLf)
    If startPos > 0 Then
        startPos = startPos + 7  ' 跳过 ```json\n
        endPos = InStr(startPos, markdownText, "```")
        If endPos > startPos Then
            jsonText = Mid(markdownText, startPos, endPos - startPos)
            ' 去除首尾空白
            jsonText = Trim(jsonText)
            ExtractJsonFromMarkdown = jsonText
        Else
            ExtractJsonFromMarkdown = ""
        End If
    Else
        ExtractJsonFromMarkdown = ""
    End If
End Function

Function ParseStringToJSON(inputStr)
    Dim dict, pairs, pair, key, value, i
    Set dict = CreateObject("Scripting.Dictionary")
    
    ' 按 | 分割字符串
    pairs = Split(inputStr, "|")
    
    ' 遍历每个键值对
    For i = 0 To UBound(pairs)
        pair = Split(pairs(i), ":")
        If UBound(pair) = 1 Then
            key = Trim(pair(0))
            value = Trim(pair(1))
            dict(key) = value
        End If
    Next
    
    ' 返回字典对象
    Set ParseStringToJSON = dict
End Function

Function ExtractTextContent(jsonStr)
    WriteLog "1111开始解析发票内容，输入JSON: " & jsonStr
    
    ' 提取原始text内容
    Dim textContent
    textContent = ExtractJsonValue(jsonStr, "text")
    WriteLog "提取的原始内容: " & textContent

    textContent = Mid(textContent, 2)
    textContent = Left(textContent, Len(textContent) - 1)
    WriteLog "提取的原始内容2: " & textContent
    
    If textContent = "" Then
        ExtractTextContent = "{""success"":false,""message"":""无法提取text内容""}"
        Exit Function
    End If

    response.write textContent
    response.end
    
    ' 解析字段
    Dim fields, pairs, result
    fields = Split(textContent, "|")
    result = "{"
    dim keyVal
    keyVal = ""
    
    Dim i, pair
    For i = 0 To UBound(fields)
        If i > 0 Then result = result & ","
        
        pair = Split(fields(i), ":")
        If UBound(pair) >= 1 Then
            ' 去掉可能存在的多余空格
            pair(0) = Trim(pair(0))
            pair(1) = Trim(pair(1))
            ' 解码Unicode并添加到JSON中
            keyVal = keyVal & """" & pair(0) & """:""" & DecodeUnicode(pair(1)) & ""","
        End If
    Next

    
    
    result = result & keyVal & "}"
    WriteLog "解析后的JSON结果: " & result
    
    ExtractTextContent = result
End Function

' Unicode解码函数
Function DecodeUnicode(str)
    Dim regex, matches, match
    Set regex = New RegExp
    regex.Global = True
    regex.Pattern = "\\u([0-9a-fA-F]{4})"
    
    Set matches = regex.Execute(str)
    
    Dim result
    result = str
    
    For Each match in matches
        result = Replace(result, match.Value, ChrW(CLng("&H" & match.SubMatches(0))))
    Next
    
    DecodeUnicode = result
End Function

' 修改CallWorkflowAPI函数中相关部分
Function CallWorkflowAPI(filePath, originalFileName, fileExt, fileId)
    ' 构建完整的文件URL
    Dim fullFileUrl
    fullFileUrl = "http://193.168.1.31:12380/fapiao/" & filePath
    
    WriteLog "开始调用工作流API"
    WriteLog "文件路径: " & fullFileUrl
    WriteLog "原始文件名: " & originalFileName 
    WriteLog "文件类型: " & fileExt
    WriteLog "文件ID: " & fileId

    ' 根据文件扩展名确定文件类型
    Dim fileType
    fileExt = UCase(fileExt) ' 转换为大写以便比较
    
    Select Case fileExt
        ' 文档类型
        Case "TXT", "MD", "MARKDOWN", "PDF", "HTML", "XLSX", "XLS", "DOCX", "CSV", "EML", "MSG", "PPTX", "PPT", "XML", "EPUB"
            fileType = "document"
        ' 图片类型
        Case "JPG", "JPEG", "PNG", "GIF", "WEBP", "SVG"
            fileType = "image"
        ' 音频类型
        Case "MP3", "M4A", "WAV", "WEBM", "AMR"
            fileType = "audio"
        ' 视频类型
        Case "MP4", "MOV", "MPEG", "MPGA"
            fileType = "video"
        ' 其他类型
        Case Else
            fileType = "custom"
    End Select

    WriteLog "文件类型映射: " & fileType

    Dim result, http, jsonData
    Set result = CreateObject("Scripting.Dictionary")
    
    On Error Resume Next
    
    Set http = CreateObject("MSXML2.ServerXMLHTTP.6.0")
    
    ' 设置API调用参数
    Dim apiUrl : apiUrl = "http://**************/v1/workflows/run"
    WriteLog "API URL: " & apiUrl
    
    http.Open "POST", apiUrl, False
    http.setRequestHeader "Content-Type", "application/json"
    http.setRequestHeader "Authorization", "Bearer app-e76frkl7f0BsNJDLRRLKXGTI"
    
    ' 构建请求JSON,使用文件ID和动态文件类型
    jsonData = "{""inputs"":{""file"":{""transfer_method"":""local_file"",""upload_file_id"":""" & _
               fileId & """,""type"":""" & fileType & """}},""response_mode"":""blocking"",""user"":""system""}"
    
    WriteLog "请求数据: " & jsonData
    
    http.Send jsonData
    
    If Err.Number = 0 And http.Status = 200 Then
        WriteLog "API调用成功，状态码: " & http.Status
        WriteLog "响应内容: " & http.responseText
        
        ' 解析返回的JSON
        Dim outputText, parsedJson
        WriteLog "开始解析响应JSON"
        
        ' 提取data字段
        outputText = ExtractJsonValue(http.responseText, "data")
        WriteLog "提取data字段结果: " & outputText
        
        ' 提取outputs字段
        outputText = ExtractJsonValue(outputText, "outputs")
        WriteLog "提取outputs字段结果: " & outputText
        
        If outputText <> "" Then
            ' 使用新的方法提取text字段内容
            outputText = ParseStringToJSON(outputText)
            WriteLog "提取text字段结果: " & outputText
            
            If outputText <> "" Then
                result("success") = True
                result("data") = outputText
                WriteLog "JSON解析成功"
            Else
                result("success") = False
                result("message") = "无法解析返回的JSON内容"
                WriteLog "错误: 无法提取text内容"
            End If
        Else
            result("success") = False
            result("message") = "响应中未找到outputs字段"
            WriteLog "错误: 未找到outputs字段"
        End If
    Else
        Dim errMsg : errMsg = "API调用失败：" & Err.Description & " (HTTP状态码：" & http.Status & ")"
        WriteLog errMsg
        result("success") = False
        result("message") = errMsg
    End If
    
    Set http = Nothing
    On Error Goto 0
    
    WriteLog "工作流API调用结束"
    Set CallWorkflowAPI = result
End Function

' 添加文件上传函数
Function UploadFile(filePath)
    WriteLog "开始调用文件上传接口"
    WriteLog "文件路径: " & filePath
    WriteLog "完整服务器路径: " & Server.MapPath(filePath)

    Dim result
    Set result = CreateObject("Scripting.Dictionary")

    On Error Resume Next

    ' 读取文件内容
    Dim objStream
    Set objStream = Server.CreateObject("ADODB.Stream")
    If Err.Number <> 0 Then
        WriteLog "创建ADODB.Stream对象失败：" & Err.Description & ", 错误代码: " & Err.Number
        result("success") = False
        result("message") = "创建文件流失败: " & Err.Description
        Set UploadFile = result
        Exit Function
    End If

    objStream.Type = 1 ' 二进制模式
    objStream.Open
    
    WriteLog "尝试加载文件: " & Server.MapPath(filePath)
    objStream.LoadFromFile Server.MapPath(filePath)

    If Err.Number <> 0 Then
        WriteLog "读取文件失败：" & Err.Description & ", 错误代码: " & Err.Number
        result("success") = False
        result("message") = "读取文件失败: " & Err.Description
        Set UploadFile = result
        Exit Function
    End If

    ' 获取文件内容
    objStream.Position = 0
    Dim fileContents : fileContents = objStream.Read
    objStream.Close
    Set objStream = Nothing

    ' 准备HTTP请求
    Dim http
    Set http = Server.CreateObject("MSXML2.ServerXMLHTTP.6.0")

    ' 生成boundary（采用随机数生成方式）
    Randomize
    Dim boundary
    boundary = "----WebKitFormBoundary" & Hex(Int(Rnd() * &HFFFFFF)) & Hex(Int(Rnd() * &HFFFFFF))

    ' 获取文件名（兼容正斜杠和反斜杠）
    Dim fileName, pos1, pos2
    pos1 = InStrRev(filePath, "/")
    pos2 = InStrRev(filePath, "\")
    If pos1 > pos2 Then
        fileName = Mid(filePath, pos1 + 1)
    Else
        fileName = Mid(filePath, pos2 + 1)
    End If

    ' 构建 multipart/form-data 请求体
    Dim requestBody
    requestBody = "--" & boundary & vbCrLf & _
        "Content-Disposition: form-data; name=""file""; filename=""" & fileName & """" & vbCrLf & _
        "Content-Type: application/octet-stream" & vbCrLf & vbCrLf

    ' 使用二进制流拼接请求体
    Dim binStream
    Set binStream = Server.CreateObject("ADODB.Stream")
    binStream.Type = 1 ' 二进制模式
    binStream.Open

    ' 写入表单头部
    binStream.Write StringToBinary(requestBody)
    ' 写入文件内容
    binStream.Write fileContents
    ' 写入结束边界
    binStream.Write StringToBinary(vbCrLf & "--" & boundary & "--" & vbCrLf)

    ' 获取完整的二进制数据
    binStream.Position = 0
    Dim postData : postData = binStream.Read
    binStream.Close
    Set binStream = Nothing

    ' 发送HTTP请求
    http.Open "POST", "http://**************/v1/files/upload", False
    http.setRequestHeader "Content-Type", "multipart/form-data; boundary=" & boundary
    http.setRequestHeader "Authorization", "Bearer app-e76frkl7f0BsNJDLRRLKXGTI"
    http.Send postData

    If Err.Number <> 0 Then
        WriteLog "上传请求失败：" & Err.Description
        result("success") = False
        result("message") = "上传请求失败: " & Err.Description
    ElseIf http.Status = 201 Then
        ' 解析JSON响应获取文件ID
        Dim responseText : responseText = http.responseText
        WriteLog "文件上传成功，响应：" & responseText
        
        ' 提取文件ID并去除多余的双引号和空格
        Dim fileId
        fileId = ExtractJsonValue(responseText, "id")
        fileId = Trim(Replace(Replace(fileId, """", ""), """", "")) ' 去除头尾的双引号和空格
        
        result("success") = True
        result("data") = responseText
        result("fileId") = fileId
    Else
        WriteLog "上传失败，状态码：" & http.Status & "，响应：" & http.responseText
        result("success") = False
        result("message") = "上传失败: HTTP " & http.Status
    End If

    Set http = Nothing
    Set UploadFile = result
End Function

' 添加辅助函数用于从JSON中提取值
Function ExtractJsonValue(jsonStr, key)
    Dim startPos, endPos, value
    Dim braceCount, inQuote, i, ch
    
    startPos = InStr(jsonStr, """" & key & """:")
    If startPos > 0 Then
        startPos = startPos + Len(key) + 3 ' 跳过 "key":
        
        ' 检查是否是字符串（带引号）
        If Mid(jsonStr, startPos, 1) = """" Then
            ' 处理字符串值
            startPos = startPos + 1
            endPos = startPos
            inQuote = True
            
            ' 特殊处理包含换行符的字符串
            For i = startPos To Len(jsonStr)
                ch = Mid(jsonStr, i, 1)
                If ch = """" And Mid(jsonStr, i-1, 1) <> "\" Then
                    endPos = i
                    Exit For
                End If
            Next
            
            value = Mid(jsonStr, startPos, endPos - startPos)
        Else
            ' 处理对象或其他值
            braceCount = 0
            inQuote = False
            endPos = startPos
            
            For i = startPos To Len(jsonStr)
                ch = Mid(jsonStr, i, 1)
                
                ' 处理引号
                If ch = """" Then
                    inQuote = Not inQuote
                ElseIf Not inQuote Then
                    ' 只在不在引号内时计数括号
                    If ch = "{" Then
                        braceCount = braceCount + 1
                    ElseIf ch = "}" Then
                        braceCount = braceCount - 1
                    ElseIf ch = "," And braceCount = 0 Then
                        ' 只有在括号计数为0时才结束
                        endPos = i - 1
                        Exit For
                    End If
                End If
                
                ' 如果括号计数归零且遇到结束括号，说明对象结束
                If braceCount = 0 And ch = "}" Then
                    endPos = i
                    Exit For
                End If
            Next
            
            value = Mid(jsonStr, startPos, endPos - startPos + 1)
        End If
        
        ' 去掉可能存在的首尾引号
        If Left(value, 1) = """" And Right(value, 1) = """" Then
            value = Mid(value, 2, Len(value) - 2)
        End If
        
        ExtractJsonValue = value
    Else
        ExtractJsonValue = ""
    End If
End Function

' 将字符串转换为二进制数据的函数
Function StringToBinary(str)
    Dim stream
    Set stream = Server.CreateObject("ADODB.Stream")
    stream.Type = 2 ' 文本模式
    stream.Charset = "utf-8"
    stream.Open
    stream.WriteText str
    stream.Position = 0
    stream.Type = 1 ' 切换为二进制模式
    StringToBinary = stream.Read
    stream.Close
    Set stream = Nothing
End Function

%>