#chat-circle {
	position: fixed;
	bottom: 15px;
	right: 20px;
	background: #017ae1;
	width: 50px;
	height: 50px;  
	border-radius: 50%;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 21px;
	box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.5), 0 5px 1px -5px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.1);
}
#chat-circle img {
	margin: 0;
	width: 21px;
	height: 21px;
}
.chat-box {
	display: none;
	background: #ffffff;
	position: fixed;
	right: 20px;
	bottom: 5px;
	width: 350px;
	max-width: 85vw;
	border-radius: 5px;
	z-index: 1000;
}
.chat-box-toggle {
	line-height: 100%;
}
.chat-box-toggle i {
	cursor: pointer;
	font-size: 18px;
	font-weight: 700;
}
.chat-box-header {
	background: #2e323c;
	color: #ffffff;
	font-size: 16px;
	font-weight: 600;
	padding: 15px 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: 5px 5px 0 0;
}
.chat-box-body {
	position: relative;
	border: 1px solid #e1e5f1;
	border-top: 0;
}
.chat-box-body:after {
	content: "";
	background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTAgOCkiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMS4yNSIgY3g9IjE3NiIgY3k9IjEyIiByPSI0Ii8+PHBhdGggZD0iTTIwLjUuNWwyMyAxMW0tMjkgODRsLTMuNzkgMTAuMzc3TTI3LjAzNyAxMzEuNGw1Ljg5OCAyLjIwMy0zLjQ2IDUuOTQ3IDYuMDcyIDIuMzkyLTMuOTMzIDUuNzU4bTEyOC43MzMgMzUuMzdsLjY5My05LjMxNiAxMC4yOTIuMDUyLjQxNi05LjIyMiA5LjI3NC4zMzJNLjUgNDguNXM2LjEzMSA2LjQxMyA2Ljg0NyAxNC44MDVjLjcxNSA4LjM5My0yLjUyIDE0LjgwNi0yLjUyIDE0LjgwNk0xMjQuNTU1IDkwcy03LjQ0NCAwLTEzLjY3IDYuMTkyYy02LjIyNyA2LjE5Mi00LjgzOCAxMi4wMTItNC44MzggMTIuMDEybTIuMjQgNjguNjI2cy00LjAyNi05LjAyNS0xOC4xNDUtOS4wMjUtMTguMTQ1IDUuNy0xOC4xNDUgNS43IiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMS4yNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+PHBhdGggZD0iTTg1LjcxNiAzNi4xNDZsNS4yNDMtOS41MjFoMTEuMDkzbDUuNDE2IDkuNTIxLTUuNDEgOS4xODVIOTAuOTUzbC01LjIzNy05LjE4NXptNjMuOTA5IDE1LjQ3OWgxMC43NXYxMC43NWgtMTAuNzV6IiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMS4yNSIvPjxjaXJjbGUgZmlsbD0iIzAwMCIgY3g9IjcxLjUiIGN5PSI3LjUiIHI9IjEuNSIvPjxjaXJjbGUgZmlsbD0iIzAwMCIgY3g9IjE3MC41IiBjeT0iOTUuNSIgcj0iMS41Ii8+PGNpcmNsZSBmaWxsPSIjMDAwIiBjeD0iODEuNSIgY3k9IjEzNC41IiByPSIxLjUiLz48Y2lyY2xlIGZpbGw9IiMwMDAiIGN4PSIxMy41IiBjeT0iMjMuNSIgcj0iMS41Ii8+PHBhdGggZmlsbD0iIzAwMCIgZD0iTTkzIDcxaDN2M2gtM3ptMzMgODRoM3YzaC0zem0tODUgMThoM3YzaC0zeiIvPjxwYXRoIGQ9Ik0zOS4zODQgNTEuMTIybDUuNzU4LTQuNDU0IDYuNDUzIDQuMjA1LTIuMjk0IDcuMzYzaC03Ljc5bC0yLjEyNy03LjExNHpNMTMwLjE5NSA0LjAzbDEzLjgzIDUuMDYyLTEwLjA5IDcuMDQ4LTMuNzQtMTIuMTF6bS04MyA5NWwxNC44MyA1LjQyOS0xMC44MiA3LjU1Ny00LjAxLTEyLjk4N3pNNS4yMTMgMTYxLjQ5NWwxMS4zMjggMjAuODk3TDIuMjY1IDE4MGwyLjk0OC0xOC41MDV6IiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMS4yNSIvPjxwYXRoIGQ9Ik0xNDkuMDUgMTI3LjQ2OHMtLjUxIDIuMTgzLjk5NSAzLjM2NmMxLjU2IDEuMjI2IDguNjQyLTEuODk1IDMuOTY3LTcuNzg1LTIuMzY3LTIuNDc3LTYuNS0zLjIyNi05LjMzIDAtNS4yMDggNS45MzYgMCAxNy41MSAxMS42MSAxMy43MyAxMi40NTgtNi4yNTcgNS42MzMtMjEuNjU2LTUuMDczLTIyLjY1NC02LjYwMi0uNjA2LTE0LjA0MyAxLjc1Ni0xNi4xNTcgMTAuMjY4LTEuNzE4IDYuOTIgMS41ODQgMTcuMzg3IDEyLjQ1IDIwLjQ3NiAxMC44NjYgMy4wOSAxOS4zMzEtNC4zMSAxOS4zMzEtNC4zMSIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utd2lkdGg9IjEuMjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPjwvZz48L3N2Zz4=');
	opacity: 0.1;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	position: absolute;
	z-index: -1;   
}
.chat-input {
	border: 1px solid #e1e5f1;
	border-top: 0;
	border-radius: 0 0 5px 5px;
}
#chat-input:focus {
	outline: 0;
}
#chat-input {
	background: transparent;
	width: 100%;
	position: relative;
	height: 48px;
	padding: 10px 60px 10px 10px;
	resize: none;
	font-size: 13px;
	border: 0;  
	overflow: hidden;
}
.chat-input > form {
	margin-bottom: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
#chat-input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
	color: #9fa8b9;
}
#chat-input::-moz-placeholder { /* Firefox 19+ */
	color: #9fa8b9;
}
#chat-input:-ms-input-placeholder { /* IE 10+ */
	color: #9fa8b9;
}
#chat-input:-moz-placeholder { /* Firefox 18- */
	color: #9fa8b9;
}
.chat-submit {
	background:  transparent;
	border: none;
	border-radius: 50%;
	color: #017ae1;
	width: 50px;
	height: 35px;
	font-size: 1.5rem;
}
.chat-logs {
	padding: 15px;
}
.chat-logs .chat-msg {
	display: flex;
	align-items: flex-start;
	flex-direction: row;
	margin: 0 0 10px 0;
}
.chat-logs .chat-msg img.user {
	width: 45px;
	height: 45px;
	border-radius: 50%;	
}
.chat-logs .chat-msg.self {
	flex-direction: row;
}
.chat-logs .chat-msg.user {
	flex-direction: row-reverse;
}
.chat-logs .chat-msg.self img.user {
	margin: 0 10px 0 0;
}
.chat-logs .chat-msg.user img.user {
	margin: 0 0 0 10px;
}
.chat-logs .chat-msg .chat-msg-text {
	color: #ffffff;
	padding: 10px 15px 10px 15px;
	border-radius: 30px;
	font-size: 12px;
}
.chat-logs .chat-msg.self .chat-msg-text {
	background: #eaecf3;
	color: #2e323c;
}
.chat-logs .chat-msg.user .chat-msg-text {
	background: #017ae1;	
}