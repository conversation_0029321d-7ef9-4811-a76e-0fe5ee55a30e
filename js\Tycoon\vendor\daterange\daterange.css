.daterangepicker {
	position: absolute;
	color: inherit;
	background-color: #fff;
	border-radius: 4px;
	width: 280px;
	max-width: none;
	padding: 0;
	margin-top: 0;
	top: 100px;
	left: 20px;
	z-index: 3001;
	display: none;
	font-size: .9rem;
	line-height: 1em;
	box-shadow: 20px 30px 30px rgba(0, 0, 0, 0.2);
}

.daterangepicker:after {
	position: absolute;
	display: inline-block;
	border-bottom-color: rgba(0, 0, 0, 0.2);
	content: '';
	top: -9px;
	border-right: 9px solid transparent;
	border-bottom: 9px solid #fff;
	border-left: 9px solid transparent;
}

.daterangepicker.opensleft:before {
	right: 9px;
}

.daterangepicker.opensleft:after {
	right: 10px;
}

.daterangepicker.openscenter:before {
	left: 0;
	right: 0;
	width: 0;
	margin-left: auto;
	margin-right: auto;
}

.daterangepicker.openscenter:after {
	left: 0;
	right: 0;
	width: 0;
	margin-left: auto;
	margin-right: auto;
}

.daterangepicker.opensright:before {
	left: 9px;
}

.daterangepicker.opensright:after {
	left: 10px;
}

.daterangepicker.drop-up {
	margin-top: -7px;
}

.daterangepicker.drop-up:before {
	top: initial;
	bottom: -7px;
	border-bottom: initial;
	border-top: 7px solid #ccc;
}

.daterangepicker.drop-up:after {
	top: initial;
	bottom: -6px;
	border-bottom: initial;
	border-top: 6px solid #fff;
}

.daterangepicker.single .daterangepicker .ranges, .daterangepicker.single .drp-calendar {
	float: none;
}

.daterangepicker.single .drp-selected {
	display: none;
}

.daterangepicker.show-calendar .drp-calendar {
	display: block;
}

.daterangepicker.show-calendar .drp-buttons {
	display: block;
}

.daterangepicker.auto-apply .drp-buttons {
	display: none;
}

.daterangepicker .drp-calendar {
	display: none;
	max-width: 270px;
}

.daterangepicker .drp-calendar.left {
	padding: 8px 0 8px 8px;
}

.daterangepicker .drp-calendar.right {
	padding: 8px;
}

.daterangepicker .drp-calendar.single .calendar-table {
	border: none;
}

.daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {
	color: #fff;
	border: solid black;
	border-width: 0 2px 2px 0;
	border-radius: 0;
	display: inline-block;
	padding: 6px;
	vertical-align: middle;
	margin-right: 7px;
}

.daterangepicker .calendar-table .next span {
	transform: rotate(-45deg);
	-webkit-transform: rotate(-45deg);
}

.daterangepicker .calendar-table .prev span {
	transform: rotate(135deg);
	-webkit-transform: rotate(135deg);
	margin-left: 7px;
  margin-right: 0;
}

.daterangepicker .calendar-table th, .daterangepicker .calendar-table td {
	white-space: nowrap;
	text-align: center;
	vertical-align: middle;
	min-width: 32px;
	width: 32px;
	height: 24px;
	line-height: 22px;
	font-size: 12px;
	border-radius: 4px;
	padding: 5px;
	white-space: nowrap;
	cursor: pointer;
	color: #61657b;
}

.daterangepicker .calendar-table {
	border: 1px solid #fff;
	border-radius: 4px;
	background-color: #fff;
}

.daterangepicker .calendar-table table {
	width: 100%;
	margin: 0;
	border-spacing: 0;
	border-collapse: collapse;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
	background-color: #f4f5fb;
	color: inherit;
}

.daterangepicker td.week, .daterangepicker th.week {
	font-size: 80%;
	color: #ccc;
}

.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
	background-color: #fff;
	border-color: transparent;
	color: #999;
}

.daterangepicker td.in-range {
	background-color: #f4f5fb;
	color: #000;
	border-radius: 0;
}

.daterangepicker td.start-date {
	border-radius: 4px 0 0 4px;
}

.daterangepicker td.end-date {
	border-radius: 0 4px 4px 0;
}

.daterangepicker td.start-date.end-date {
	border-radius: 4px;
}

.daterangepicker td.active, .daterangepicker td.active:hover {
	background-color: #00bb42;
	color: #fff;
}

.daterangepicker th.month {
	width: auto;
}

.daterangepicker td.disabled, .daterangepicker option.disabled {
	color: #999;
	cursor: not-allowed;
	text-decoration: line-through;
}

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
	font-size: 12px;
	padding: 1px;
	height: auto;
	margin: 0;
	cursor: default;
}

.daterangepicker select.monthselect {
	margin-right: 2%;
	width: 56%;
}

.daterangepicker select.yearselect {
	width: 40%;
}

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
	width: 50px;
	margin: 0 auto;
	background: #eee;
	border: 1px solid #eee;
	padding: 2px;
	outline: 0;
	font-size: 12px;
}

.daterangepicker .calendar-time {
	text-align: center;
	margin: 4px auto 0 auto;
	line-height: 30px;
	position: relative;
}

.daterangepicker .calendar-time select.disabled {
	color: #ccc;
	cursor: not-allowed;
}

.daterangepicker .drp-buttons {
	clear: both;
	text-align: right;
	padding: 8px;
	display: none;
	line-height: 12px;
	vertical-align: middle;
	background: #f4f5fb;
	border-top: 1px solid #eaebf3;
	border-radius: 0 0 4px 4px;
}

.daterangepicker .drp-selected {
	display: inline-block;
	font-size: 12px;
	padding-right: 8px;
}
@media (max-width: 767px) {
	.daterangepicker .drp-selected {
		margin: 0 0 10px 0;
		padding: 0;
		display: block;
		text-align: center;
	}
}

.daterangepicker .drp-buttons .btn {
	margin-left: 8px;
	font-size: .7rem;
	padding: .2rem 1rem;
	border-radius: 50px;
}

.daterangepicker.show-ranges .drp-calendar.left {
	border-left: 1px solid #eaebf3;
}

.daterangepicker .ranges {
	float: none;
	text-align: left;
	margin: 10px 0;
}

.daterangepicker.show-calendar .ranges {
	margin-top: 8px;
}

.daterangepicker .ranges ul {
	list-style: none;
	margin: 0 auto;
	padding: 0;
	width: 100%;
}

.daterangepicker .ranges li {
	font-size: .8rem;
	padding: .6rem 1rem;
	cursor: pointer;
	transition: all 0.2s ease-in;
}

.daterangepicker .ranges li:hover {
	background-color: #f4f5fb;
}

.daterangepicker .ranges li.active {
	background: #017ae1;
	color: #ffffff;
}

/*  Larger Screen Styling */
@media (min-width: 564px) {
	.daterangepicker {
		width: auto;
	}
	.daterangepicker .ranges ul {
		width: 140px;
	}
	.daterangepicker.single .ranges ul {
		width: 100%;
	}
	.daterangepicker.single .drp-calendar.left {
		clear: none;
	}
	.daterangepicker.single.ltr .ranges,
	.daterangepicker.single.ltr .drp-calendar {
		float: left;
	}
	.daterangepicker.single.rtl .ranges,
	.daterangepicker.single.rtl .drp-calendar {
		float: right;
	}
	.daterangepicker.ltr {
		direction: ltr;
		text-align: left;
	}
	.daterangepicker.ltr .drp-calendar.left {
		clear: left;
		margin-right: 0;
	}
	.daterangepicker.ltr .drp-calendar.left .calendar-table {
		border-right: none;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	.daterangepicker.ltr .drp-calendar.right {
		margin-left: 0;
	}
	.daterangepicker.ltr .drp-calendar.right .calendar-table {
		border-left: none;
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}
	.daterangepicker.ltr .drp-calendar.left .calendar-table {
		padding-right: 8px;
	}
	.daterangepicker.ltr .ranges,
	.daterangepicker.ltr .drp-calendar {
		float: left;
	}
	.daterangepicker.rtl {
		direction: rtl;
		text-align: right;
	}
	.daterangepicker.rtl .drp-calendar.left {
		clear: right;
		margin-left: 0;
	}
	.daterangepicker.rtl .drp-calendar.left .calendar-table {
		border-left: none;
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}
	.daterangepicker.rtl .drp-calendar.right {
		margin-right: 0;
	}
	.daterangepicker.rtl .drp-calendar.right .calendar-table {
		border-right: none;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	.daterangepicker.rtl .drp-calendar.left .calendar-table {
		padding-left: 12px;
	}
	.daterangepicker.rtl .ranges,
	.daterangepicker.rtl .drp-calendar {
		text-align: right;
		float: right;
	}
}

@media (min-width: 730px) {
	.daterangepicker .ranges {
		width: auto;
	}
	.daterangepicker.ltr .ranges {
		float: left;
	}
	.daterangepicker.rtl .ranges {
		float: right;
	}
	.daterangepicker .drp-calendar.left {
		clear: none !important;
	}
}