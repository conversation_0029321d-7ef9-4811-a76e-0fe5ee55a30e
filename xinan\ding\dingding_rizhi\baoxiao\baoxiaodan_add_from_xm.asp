<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--include virtual="/shujuku.asp"-->
<!--#include file="../../../shujuku.asp"-->


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script src="/xinan/ding/js/jquery-1.11.3.min.js" type="text/javascript"></script>
<script src="/xinan/ding/js/json2.js" type="text/javascript"></script>
<title>费用报销申请</title>
<link rel="stylesheet" type="text/css" href="/xls.css" />
<!-- 引入 Bootstrap -->
<script src="/js/bootstrap/jquery.min.js"></script>
<link href="/js/bootstrap/css/bootstrap.min.css" rel="stylesheet">		
 <!-- 包括所有已编译的插件 -->
<script src="/js/bootstrap/js/bootstrap.min.js"></script>
<script src="/js/bootstrap/js/sco.tooltip.js"></script>
<!--下拉列表插件bootstrap-select-->
<link rel="stylesheet" type="text/css" href="/js/bootstrap/bootstrap-select/bootstrap-select.min.css" />
<script src="/js/bootstrap/bootstrap-select/bootstrap-select.min.js"></script>
 <script language="JavaScript">
<!--
function change(obj,i) {
he=parseInt(obj.style.height);
if (he>=80&&he<=400)
   obj.style.height=he+i+'px';
else 
   obj.style.height='80px';
}


function CheckNum(){
   if((event.keyCode<48||event.keyCode>57)&&event.keyCode!=46) event.returnValue=false; 
   }
//-->






</script>
</head>
<% 
if Session("name")="" then
%>
 对不起你还没有登陆，请<a href="../../index.asp">登陆</a>
 
<%
else 
%>
<body>
	<%
	'读取售前项目的所有信息'
	baifangid=request("bfid")
	xmid=request("xmid")
	riqi=request("riqi")
	'售前项目 xmleixing=shouqian  售后项目 xmleixing=shouhou'
	xmleixing=request("xmleixing")
	'要读取拜访记录表的日期、客户单位、项目名称'
	'防止sql注入；过滤一些字符
	xmid=Replace(Replace(Replace(Replace(Replace(xmid,"'",""),"or",""),"and",""),"--",""),";","") 
	if xmleixing="shouqian" then
		sql="select 项目名称,客户单位全称,项目销售负责人,重要级别,实施开始日期,预计签单金额,预计签单时间 from 技术部测试管理表 where id="&xmid&""
		rs2.open sql,conn2,1,2
		xmname=rs2(0)
		kh=rs2(1)
		xs=rs2(2)
		jibie=rs2("重要级别")
		xm_start_riqi=rs2("实施开始日期")
		yusuan_jine=rs2("预计签单金额")
		yusuan_riqi=rs2("预计签单时间")
		rs2.close
		sql="select 实施情况 from 技术部测试管理表 where id="&baifangid&""
		rs2.open sql,conn2,1,2
		baifangnr=rs2(0)
		rs2.close
		
		'查询该项目花销多少了，申请人个人花销多少了'
		sql="select sum(支出)  as heji,count(id)as chishu from caiwu_baoxiao where 项目售前id="&xmid&" and 出纳是否支出=1  "
		rs2.open sql,conn2,1,2
		if not rs2.eof and rs2(0)>0 then
			feiyong1=rs2(0)
			chishu1=rs2(1)
		else
			feiyong1=0
			chishu1=0
		end if
		rs2.close
		sql="select sum(支出总计)  as heji,count(id)as chishu from caiwu_baoxiao_cailv where 项目售前id="&xmid&" and 出纳是否支出=1  "
		rs2.open sql,conn2,1,2
		if not rs2.eof and rs2(0)>0 then
			feiyong2=rs2(0)
			chishu2=rs2(1)
		else
			feiyong2=0
			chishu2=0
		end if
		rs2.close
		feiyong=feiyong1+feiyong2	
		chishu=chishu1+chishu2	
	end if
	if xmleixing="shouhou" then
		sql="select 项目名称,客户单位id,项目销售负责人,重要级别,实施开始日期 from 技术部项目管理表 where id="&xmid&""
		rs2.open sql,conn2,1,2
		xmname=rs2(0)
		khid=rs2(1)
		xs=rs2(2)
		jibie=rs2("重要级别")
		xm_start_riqi=rs2("实施开始日期")
		yusuan_jine=""
		yusuan_riqi=""
		rs2.close
		sql="select 单位全称 from 客户单位信息 where id="&khid&""
		rs2.open sql,conn2,1,2
		kh=rs2(0)
		rs2.close
		sql="select 实施情况 from 技术部项目管理表 where id="&baifangid&""
		rs2.open sql,conn2,1,2
		baifangnr=rs2(0)
		rs2.close
		'查询该项目花销多少了，申请人个人花销多少了'
		sql="select sum(支出)  as heji,count(id)as chishu from caiwu_baoxiao where 项目售后id="&xmid&" and 出纳是否支出=1  "
		rs2.open sql,conn2,1,2
		if not rs2.eof and rs2(0)>0 then
			feiyong1=rs2(0)
			chishu1=rs2(1)
		else
			feiyong1=0
			chishu1=0
		end if
		rs2.close
		sql="select sum(支出总计)  as heji,count(id)as chishu from caiwu_baoxiao_cailv where 项目售后id="&xmid&" and 出纳是否支出=1  "
		rs2.open sql,conn2,1,2
		if not rs2.eof and rs2(0)>0 then
			feiyong2=rs2(0)
			chishu2=rs2(1)
		else
			feiyong2=0
			chishu2=0
		end if
		rs2.close
		feiyong=feiyong1+feiyong2	
		chishu=chishu1+chishu2	
	end if
	
	
	%>
	<script language="javascript" type="text/javascript">
	
	function selectOnchang(obj){ 
	//获取被选中的option标签选项 
	//alert(obj.selectedIndex);
	//var val=$("#xmxuanzhe").val();
	var val=obj.selectedIndex;  
	if(val == 1 ) {
								$("#shouqianid").collapse({toggle: true});
								}
	else if(val == 2 ) {
								$("#shouhouid").collapse({toggle: true});
								};
	//if(val == 1 ) {
	//					$("#shouqianid").collapse({toggle: true}); 
	//					}
	//else if(val == 2 ) {
	//					$("#shouhouid").collapse({toggle: true});
	//					}
	//else if(val == 3 ) {
	//					$("#shouqianid").collapse({toggle: true});
	//					}
	//else if(val == 4 ) {
	//					$("#shouhouid").collapse({toggle: true});
	//					};
	}
</script>
<!---
<div class="row">
	<div class="btn-group" role="group" aria-label="...">
	  <button type="button" class="btn btn-default" onclick="location.href='../../main.asp'">返回首页</button>
	  <button type="button" class="btn btn-primary"onclick="location.href='baoxiaodan_add.asp'">费用报销单</button>
	  <button type="button" class="btn btn-default"onclick="location.href='baoxiaodan_cailv_add.asp'">差旅报销单</button>
	</div>
</div>
-->	
	

	
	<div class="col-md-2"></div>
	<div class="col-md-8">
    	<div class="panel panel-primary ">
    	  <div class="panel-heading">费用报销单</div>
    	  <div class="panel-body">
			  <form id="myform" name="myform" >	
		<table  width="100%">
			<tr>
				<td style="width:180px" align="right" >花销日期：<font color="red">(必填)</font></td>
				<td style="width:auto;" align="left">
                	<input  id="hxrq" class="form-control" type="date" style="width:300px;height:25px" />
				</td>
			</tr>
            <tr>
				<td  align="right">费用类型：<font color="red">(必填)</font></td>
				<td align="left">
                <!--	<input type="text" id="fylx" style="width:300px;height:25px" />-->
					<script language="JavaScript">
											<%
											'二级数据保存到数组===========================当点击供应商名字时出现对应的联系人信息====================================================
											sql="select * from 财务科目分类 where 收支='支'" 
											rs.open sql,conn,1,1
											%>
											var subval2 = new Array();
											
											<%
											
											count2 = 0
											do while not rs.eof
											%>
											subval2[<%=count2%>] = new Array('<%=rs("一级科目名称")%>','<%=rs("二级科目名称")%>','<%=rs("二级科目名称")%>')
											<%
											count2 = count2 + 1
											rs.movenext
											loop
											rs.close
											%>
											
											function changeselect1(locationid)
											{
											    document.myform.city.length = 0;
											    document.myform.city.options[0] = new Option('请选择二级科目','');
											    for (i=0; i<subval2.length; i++)
											    {
											        if (subval2[i][0] == locationid)
											        {document.myform.city.options[document.myform.city.length] = new Option(subval2[i][2],subval2[i][1]);}
											    }
											}
											
											
											
											</SCRIPT> 
												<%
											'一级菜单
											'Dim count1,rsClass1,sqlClass1
											'set rsClass1=server.createobject("adodb.recordset")
											sql="select 一级科目名称 from 财务科目分类 where 收支='支'and (一级科目名称='管理费用' or 一级科目名称='业务费用') group by 一级科目名称" 
											rs.open sql,conn,1,1
											%>
											<select name="province" onChange="changeselect1(this.value)" id="fylx" style="width:300px;"   class="form-control">
											<option>请选择一级科目</option>
											<%
											count1 = 0
											do while not rs.eof
											response.write"<option value="&rs("一级科目名称")&">"&rs("一级科目名称")&"</option>"
											count1 = count1 + 1
											rs.movenext
											loop
											rs.close
											%>
											</select>
											
											<!-- 二级联动菜单 开始 -->
												
												
												<%
											'二级菜单，数据库在java里面写的
											%>
											
											<select name="city" id="fylx2"style="width:300px;"  class="form-control"> 
											<option>请选择二级科目</option>
											</select>	
				</td>
			</tr>
          <!--  <tr>
				<td height="40px" align="right">费用二级类型：</td>
				<td align="left">
                	<input type="text" id="fylx2" style="width:300px;height:25px" />
				</td>
			</tr>-->
            <tr>
				<td  align="right">有无发票：<font color="red">(必填)</font></td>
				<td align="left">
                    <select id="fp" style="width:300px;" class="form-control" >
                            <option value="有发票" selected="selected">有发票</option>
                            <option value="无发票">无发票</option>
                    </select>
				</td>
			</tr>
            <tr>
				<td  align="right">费用金额：</td>
				<td align="left">
                	<input type="text" id="fyje" style="width:300px;height:35px" class="form-control"  onkeypress="javascript:CheckNum();"  value="0.00" onfocus="if (value =='0.00'){value =''}" onblur="if (value ==''){value='0.00'}" />
				</td>
			</tr>
            <tr>
				<td  align="right">事由说明：<font color="red">(必填)</font></td>
				<td align="left">
                	<!--<input type="text" id="sysm" style="width:300px;height:25px"  />-->
					<script type="text/javascript" charset="UTF-8" src="/js/ueditor/ueditor.config.js"></script>
					<script type="text/javascript" charset="UTF-8" src="/js/ueditor/ueditor.all.min.js"> </script>
					<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
					<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
					<script type="text/javascript" charset="UTF-8" src="/js/ueditor/lang/zh-cn/zh-cn.js"></script>
					<script id="shuoming" name="shuoming" type="text/plain" style="width:100%;height:100px;">
					<%
					if xmleixing="shouqian" then xmlx="售前" end if
					if xmleixing="shouhou" then xmlx="售后" end if
					%>
						时间：<br>
					地点：<br>
					对象：<br>
					花销原因：<br>
					花销明细单：<br>
					-------------------------<br>
					【<%=riqi%>】-【拜访记录id:<%=baifangid%>】拜访记录:<%=left(baifangnr,100)%>
					
					</script>
					<script type="text/javascript">
					var ue = UE.getEditor('shuoming');
					</script>
					<% 
					zhaiyao="以下内容不得修改，违者不报销！<br>【"&xmlx&"项目id:"&xmid&"】-【销售负责人:"&xs&"】-【"&kh&"】-【"&xmname&"】【星级:"&jibie&"】-【立项日期:"&xm_start_riqi&"】-【预算:"&yusuan_jine&"w,预计签单时间"&yusuan_riqi&"】-【项目累计支出:"&chishu&"单，累计"&feiyong&"元】"
					%>
					<input name="zhaiyao" id="zhaiyao" type="hidden" value="<%=zhaiyao%>" />

				</td>
			</tr>
            <tr>
				<td height="40px" align="right">项目归属：<font color="red">(必填)</font></td>
				<td align="left">
					<select class="form-control" name="xmxuanzhe" id="xmxuanzhe" onchange="selectOnchang(this)" >
					  <%if xmleixing="shouqian" then%>
					    				   
					    <option value="1" selected="selected" >输入售前工作记录id</option>
					    
					  <%end if%>
					  <%if xmleixing="shouhou" then%>
					    					    
					    <option value="2" selected="selected">输入售后工作记录id</option>
					  <%end if%>
					</select>
					<%if xmleixing="shouqian" then%>
						<input name="shouqianxm" id="shouqianxm" type="hidden" value="<%=xmid%>" />
						<div id="shouqianid"  >输入<strong>售前</strong>项目工作记录id号
						<input name="genzonghao" id="genzonghao" type="text"  value="<%=baifangid%>" class="form-control"     style="width: 69px" readonly="readonly" />
						</div>
					<%end if%>
					<%if xmleixing="shouhou" then%>
						<input name="shouhouxm" id="shouhouxm" type="hidden" value="<%=xmid%>" />
						<div id="shouhouid"  >输入<strong>售后</strong>项目工作记录id号
						<input name="genzonghao2" id="genzonghao2" type="text"onkeypress="javascript:CheckNum();"   value="<%=baifangid%>"  class="form-control"   style="width: 69px" readonly="readonly" />
						
						</div>
					<%end if%>
					
					
					
					
				</td>
			</tr>
			 <tr>
				<td align="right">所在部门：</td>
				<td align="left">
					<script type="text/javascript">
						$(function(){
							//查询自己所在的部门
								$.ajax({
									 type: "POST",
									 url: "/xinan/ding/getdepts.asp",
									 data: {},
									 dataType: "json",
									 success: function(data){
										if(data.errcode==0){
											if(data && data.dept_list && data.dept_list.length>0){
												for(var i=0;i<data.dept_list.length;i++){
													var dept = data.dept_list[i];
													$("#dept").append('<option value="'+dept.id+'">'+dept.name+'</option>');
												}
											}
										}else{
											alert(data.errmsg || 'error')
										}
										
									 },
									 error:function(e){
										console.log(e);
									 }
								});
						});
					</script>
			    	 <select name="dept" id="dept" style="width:300px;height:25px">
			    	   
			    	</select>
				</td>
			</tr>
			
			
		</table>
		</form>
		<div style="text-align:center">
			<input name="xmname" id="xmname" type="hidden" value="<%=xmname%>" />
			<input name="zhangben" id="zhangben" type="hidden" value="<%=request("zhangben")%>" />

			<%  
			'若是售前项目费用报销+1或2星项目+已报销操作了1000元,则不能再报销'  
				If xmleixing="shouqian" And (jibie=1 Or jibie=2) And feiyong>=1000 Then   
					' 当满足条件时，输出警告信息，并通过JavaScript禁用提交按钮  
				%>  
					<script type="text/javascript">  
						document.getElementById('submit').disabled = true;  
					</script>  
					<h3 class='text-danger'>该项目为一星二星项目,费用报销已超规定金额,需销售负责人上调项目级别,否则无法报销!</h3>  
				<%  
				End If   
			%>  
			<%  
			'若是售前项目费用报销+3星项目+已报销操作了3000元,则不能再报销'  
				If xmleixing="shouqian" And (jibie=3 ) And feiyong>=3000 Then   
					' 当满足条件时，输出警告信息，并通过JavaScript禁用提交按钮  
				%>  
					<script type="text/javascript">  
						document.getElementById('submit').disabled = true;  
					</script>  
					<h3 class='text-danger'>该项目为三星项目,费用报销已超规定金额,需销售负责人上调项目级别,否则无法报销!</h3>  
				<%  
				End If   
			%>  
			<button type="submit" id="submit"  class="btn btn-default">提交</button> 
			
			
			<!-- <font color="red">提醒：不填写项目拜访id号无法提交！</font> -->
		   
		</div>
			</div>
		</div>
	
	</div>
	<div class="col-md-2"></div>
    <div class="col-md-12" align="center">
    
    	<img alt="" src="../../images/2019/oa报销示意图.jpg" /></div>    
        
	<script type="text/javascript">
		$(function(){
			$("#submit").click(function(){
				//按钮禁用
				$('#submit').attr('disabled', true);
				var hxrq = $.trim($("#hxrq").val());
				var fylx = $.trim($("#fylx").val());
				var fylx2 = $.trim($("#fylx2").val());
				var fp = $.trim($("#fp").val());
				var fyje = $.trim($("#fyje").val());
				var zhaiyao=$.trim($("#zhaiyao").val());
				//var sysm = $.trim($("#shuoming").val());
				var sysm=UE.getEditor('shuoming').getContent()+zhaiyao;
				var xmgs = $.trim($("#xmxuanzhe").val());
				var mydept = $.trim($("#dept").val());
				var shxmid=$.trim($("#shouhouxm").val());
				var sqxmid=$.trim($("#shouqianxm").val());
				var shxmbaifangid=$.trim($("#genzonghao2").val());
				var sqxmbaifangid=$.trim($("#genzonghao").val());
				var xmname=$.trim($("#xmname").val());
				var zhangben=$.trim($("#zhangben").val());

				// 替换换行相关的HTML标签为换行符\n  
				var plainText = sysm  
					.replace(/<br\s*\/?>/gi, '\n') // 替换 <br>, <br/>, 和 <br />
					.replace(/<\/?(p|div|section|article|aside|header|footer|nav|figure|figcaption)[^>]*>/gi, '\n') // 替换其他常见的具有换行功能的标签  
					.replace(/\n+/g, '\n') // 合并连续的换行符  
					.replace(/(<img[^>]+src=")\/([^"]+")/g, '$1https://oa.work.secdriver.com/$2') //把<img src="/ 替换为<img src="https://oa.work.secdriver.com/
					.trim(); // 去除首尾的空白字符  
				sysm=plainText



				if (hxrq=="") {
				   alert("请输入费用日期");
					return false;
				};
				if (fylx=="请选择一级科目") {
				   alert("请选择一级科目");
					return false;
				};
				if (fylx2=="请选择二级科目") {
				   alert("请选择二级科目");
					return false;
				};
				if (xmgs=="0") {
				   alert("请输入拜访跟踪单id号");
					return false;
				};
				//if (xmgs=="1"){xmname="已关联售前项目"	};
				//if (xmgs=="2"){xmname="已关联售后项目"	};
				//if (xmgs=="3"){xmname="已关联售前项目"	};
				//if (xmgs=="4"){xmname="已关联售后项目"	};		
				//var shuoming = UE.getEditor('shuoming').getContent()+zhaiyao;
				var shuoming = sysm;
				var baifangid=0
				$("#submit").hide();//点击提交按钮后，隐藏提交按钮 防止重复提交
				//如果是输入的售前拜访表id不为空,这赋值
				if(xmgs=="1"&& sqxmbaifangid!=""){
				 baifangid=parseInt(sqxmbaifangid)
				};
				if(xmgs=="2"&& shxmbaifangid!=""){
				 baifangid=parseInt(shxmbaifangid)
				};
			
			
				//钉钉模板Code
				//{name: '拜访表id',value: baifangid}
				var formComponentValues = [
					{name: '花销日期',value: hxrq},
					{name: '费用类型',value: fylx},
					{name: '费用二级类型',value: fylx2},
					{name: '有无发票',value: fp},
					{name: '费用金额',value: fyje},
					{name: '事由说明',value: shuoming},
					{name: '项目归属',value: xmname},
					{name: '拜访表id',value: baifangid}
					
					
				];
				var sqlValues=[
					{name: '售前拜访表id',value: sqxmbaifangid},
					{name: '售后拜访表id',value: shxmbaifangid},
					{name: '售前项目id',value: sqxmid},
					{name: '售后项目id',value: shxmid},
					{name: 'zhangben',value: zhangben},
					{name: '项目选择',value: xmgs}
				];
				$.ajax({
					 type: "POST",
					 url: "submitproc.asp",
					 data: {
						 formComponentValues:JSON.stringify(formComponentValues),
						 procType:'plan',
						 mydeptid:mydept,
						 sqlValues:JSON.stringify(sqlValues)
					 },
					 dataType: "json",
					 success: function(data){
						 if(data.errcode==0){
							alert("钉钉审批流程发起成功");
							window.location.href='../../shenpi_me.asp?look=myfeiyong';
						}else{
							alert(data.errmsg || 'error');
							//按钮启用
							$('#submit').attr('disabled', false);
						}
						
					 },
					 error:function(e){
						console.log(e);
					 }
				});
			});
		});
	</script>
</body>
</html>
<%end if%>