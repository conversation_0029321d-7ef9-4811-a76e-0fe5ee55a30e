<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../../shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->
<%
	'存储到日志表
	'参数：
	'    ids：            日志记录的ID，多个则以英文逗号分隔
	'返回：
	'	 errcode	      返回码
	'    errmsg	          对返回码的文本描述内容
	Response.Charset="utf-8"
	Response.ContentType="application/json"
	Set rstJSON = New aspJSON
	With rstJSON.data
    	.Add "errcode", -1
		.Add "errmsg", "fail"
	End With
	'on error resume next 
	'set conn = GetSQLServerConnection()'获取数据库连接
	'sum是计算导入售前项目的数量 sum2 是导入售后项目的数量 exist 是售前项目表已存在相同日志的数量 exist2 是售后项目已存在相关日志的数量'
	dim logsstr '日志详细信息
	logsstr = request("logs")
	dim sum,success,exist,exist2,sum2,success2,sum3,success3,exist3
	exist=0 
	exist2=0 
	exist3=0 
	sum=0
	sum2=0
	sum3=0
	success=0
	success2=0
	success3=0

	'最新跟进日期,最新跟进人,项目类型(1-售前 or 2-售后),项目id(售前or售后)
	dim latestTrackTime, tmpTrackTime, latestTrackUserId, projectType, projectId

	if logsstr <> "" then
		logsstr = "{""list"":" & logsstr & "}"
		Set oJSON = New aspJSON
		oJSON.loadJSON(logsstr)
		For Each phonenr In oJSON.data("list")
    		Set logs = oJSON.data("list").item(phonenr)
		  	'检查是否已经入过库
			
			Set rst=  CreateObject("ADODB.Recordset") 
			set rs3=server.createobject("adodb.recordset")
			set rs3.activeconnection=conn2
			set rs4=server.createobject("adodb.recordset")
			set rs4.activeconnection=conn2
			set rs5=server.createobject("adodb.recordset")
			set rs5.activeconnection=conn2
			set rs6=server.createobject("adodb.recordset")
			set rs6.activeconnection=conn2
			dim n
			'先取一下员工的姓名拼音需要放入到下面的数据库里'
			sql="SELECT name,mingzhi from user2011 where mingzhi='"&logs.item("creator_name")&"'"
			rs2.open sql,conn2,1,2
			if not rs2.eof then
				name_pinyin=rs2(0)
				mingzhi=rs2(1)
			else
				name_pinyin=logs.item("creator_name")
				mingzhi=logs.item("creator_name")
			end if
			rs2.close

			'日志跟进日期
			tmpTrackTime = logs.item("create_time")
			if latestTrackTime = "" then
				latestTrackTime = tmpTrackTime
				latestTrackUserId = mingzhi
			else
				'比较最新跟进日期
				If CDate(latestTrackTime) < CDate(tmpTrackTime) Then
					latestTrackTime = tmpTrackTime
					latestTrackUserId = mingzhi
				end if
			end if
			
			'先导入到oa的工作汇报系统里'
            sql="SELECT [钉钉日志id] FROM [工作汇报系统] where [钉钉日志id]='" & logs.item("report_id") & "'"
            rs6.open sql,conn2,1,2
            iRowCount = rs6.recordcount
            if iRowCount = 0 then
                    leixing="每日"
                    biaoti=year(logs.item("create_time"))&"-"&month(logs.item("create_time"))&"-"&day(logs.item("create_time"))&"-"&logs.item("creator_name")&"-工作日报"
                sql = "insert into [工作汇报系统]([报告人],[录入人],[计划提交日期],[总结提交日期],[总结内容],[分类],[钉钉日志id],[计划标题]) values (" & _
                "'" & logs.item("creator_name") & "','" & logs.item("creator_name") & "','" & logs.item("create_time") & "','" & logs.item("bussiness_time") & _
                "','" & logs.item("contents") & "','" & leixing & _
                "','" & logs.item("report_id") & "','" & biaoti& "')"
                conn2.Execute(sql)
                success3 = success3 + 1
            else
                exist3 = exist3 + 1
            end if
            sum3 = sum3 + 1
			'======================================================================'
			
			
			'如果售前项目id选择的话写入售前项目表 若售后项目id也选择的话写入售后项目表'
			if logs.item("shouqianXM_id")<>"请选择" then
				sql="SELECT [钉钉日志id] FROM [技术部测试管理表] where [钉钉日志id]='" & logs.item("report_id") & "'"
				rs3.open sql,conn2,1,2
				iRowCount = rs3.recordcount
				if iRowCount = 0 then
					if logs.item("template_name")<>"日报" then
						leixing="日报"
					else
						leixing=logs.item("template_name")
					end if
					sql = "insert into [技术部测试管理表]([录入人],[实施人],[录入日期],[实施日期],[实施情况],[拜访形式],[钉钉日志id],[aa_id]) values (" & _
					"'" & name_pinyin & "','" & logs.item("creator_name") & "','" & logs.item("create_time") & "','" & logs.item("bussiness_time") & _
					"','" & logs.item("contents") & "','" & leixing & _
					"','" & logs.item("report_id") & "','" & logs.item("shouqianXM_id") & "')"
					conn2.Execute(sql)
					success = success + 1

					'更新项目的最新实施日期'
					sql="update [技术部测试管理表] set 实施日期='"& logs.item("bussiness_time") &"' where id="& logs.item("shouqianXM_id")
					LogMessage "更新项目的最新实施日期 -> " & sql
					conn2.Execute(sql)

				else
					exist = exist + 1
				end if
				sum = sum + 1
				projectType = 1
				projectId = logs.item("shouqianXM_id")
			end if
			'======================================================================'
			if logs.item("shouhouXM_id")<>"请选择" then
				sql="SELECT [钉钉日志id] FROM [技术部项目管理表] where [钉钉日志id]='" & logs.item("report_id") & "'"
				rs4.open sql,conn2,1,2
				iRowCount2 = rs4.recordcount
				if iRowCount2 = 0 then
					'先取一下项目当前阶段的名称,然后再复制新日报里'
					sql="SELECT 目前阶段 from 技术部项目管理表 where id="&logs.item("shouhouXM_id")&" "
					rs5.open sql,conn2,1,2
					if not rs5.eof then
						xm_jieduan=rs5(0)
					end if
					rs5.close
					if logs.item("template_name")<>"日报" then
						leixing="日报"
					else
						leixing=logs.item("template_name")
					end if
					sql = "insert into [技术部项目管理表]([录入人],[实施人],[录入日期],[实施日期],[实施情况],[拜访形式],[钉钉日志id],[aa_id],[目前阶段]) values (" & _
					"'" & name_pinyin & "','" & logs.item("creator_name") & "','" & logs.item("create_time") & "','" & logs.item("bussiness_time") & _
					"','" & logs.item("contents") & "','" & leixing & _
					"','" & logs.item("report_id") & "','" & logs.item("shouhouXM_id") & "','" & xm_jieduan & "')"
					conn2.Execute(sql)
					success2 = success2 + 1
					
					'更新项目的最新实施日期'
					sql="update [技术部项目管理表] set 实施日期='"& logs.item("bussiness_time") &"' where id="& logs.item("shouhouXM_id")
					LogMessage "更新项目的最新实施日期 -> " & sql
					conn2.Execute(sql)
				else
					exist2 = exist2 + 1
				end if
				sum2 = sum2 + 1
				
				projectType = 2
				projectId = logs.item("shouhouXM_id")
			end if

			if logs.item("template_name") = "跟进记录" then
				sql="update [saler_work_record] set status=1 where instance_id='"& logs.item("report_id") &"'"
				LogMessage "更新跟进记录的状态 -> " & sql
				conn2.Execute(sql)
			end if
			
			'sum = sum + 1
		Next
	else
		err.number=-1
		err.Description="日志记录不能为空"
	end if

	dim companyId

	''更新客户单位表跟进信息,首先查询对应项目,售前or售后项目信息,查找项目关联的单位id,最后更新单位信息
	if projectId <> "" then
		if projectType = 1 then
			sql="select [id],[客户单位id] from [技术部测试管理表] where id = "&projectId
			LogMessage "售前项目查询, sql -> " & sql
			'rst.open sql,conn,1,2
			rs2.open sql,conn2,1,2
			iRowCount = rs2.recordCount
			if iRowCount > 0 then
				companyId = rs2("客户单位id")
			end if
			rs2.close
		else
			sql="select [id],[客户单位id] from [技术部项目管理表] where id = "&projectId
			LogMessage "售后项目查询, sql -> " & sql
			rs2.open sql,conn2,1,2
			iRowCount = rs2.recordCount
			if iRowCount > 0 then
				companyId = rs2("客户单位id")
			end if
			rs2.close
		end if

		'更新客户单位表信息
		set rsRecord=server.createobject("adodb.recordset")
		set rsRecord.activeconnection=conn2
		if not isNull(companyId) and companyId <> "" then
			sql="select [id],[最新跟进人],[最新跟进日期] from [客户单位信息] where id="& companyId
			LogMessage "更新项目的最新跟进日期,最新跟进人 -> " & sql
			rsRecord.open sql,conn2,3,2
			if not rsRecord.eof then 
				latestTrackTime = Replace(latestTrackTime,"+"," ")
				rsRecord("最新跟进人") = latestTrackUserId
				if isNull(rsRecord("最新跟进日期")) then
					rsRecord("最新跟进日期") = latestTrackTime
				else
					if CDate(rsRecord("最新跟进日期")) < CDate(latestTrackTime) then
						rsRecord("最新跟进日期") = latestTrackTime
					end if
				end if
				rsRecord.update
			end if
			rsRecord.close
		end if
	end if
	
	if err.number=0 then
		rstJSON.data("errcode")=0
		'rstJSON.data("errmsg")="汇报系统成功导入"&success3&",售前项目日志总共操作" & sum & "条记录，执行成功" & success & "条," & exist & "条记录数据中已存在;售后项目日志总共操作"& sum2 & "条记录，执行成功" & success2 & "条," & exist2 & "条记录数据中已存在!"

		rstJSON.data("errmsg")="汇报系统成功导入["& success3 &"]条,售前项目系统成功导入[" & success & "]条;售后项目系统成功导入[" & success2 & "]条!"
	else
		rstJSON.data("errcode")=err.number
		rstJSON.data("errmsg")=err.Description
	end if
	
	conn2.close
	Response.Write rstJSON.JSONoutput() 
%>