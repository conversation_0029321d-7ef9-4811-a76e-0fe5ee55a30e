<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->

<%
'获取URL参数
project_id = Request.QueryString("project_id")
project_type = Request.QueryString("project_type")  '新增：项目类型参数（pre=售前，base=售后）
shangjia_date = Request.QueryString("shangjia_date")
xiajia_date = Request.QueryString("xiajia_date")
shangjia_person = Request.QueryString("shangjia_person")
xiajia_person = Request.QueryString("xiajia_person")

'检查必填参数
If project_id = "" Then
    Response.Write "{""code"":-1, ""msg"":""缺少项目ID参数""}"
    Response.End
End If

If project_type = "" Then
    Response.Write "{""code"":-1, ""msg"":""缺少项目类型参数""}"
    Response.End
End If

'根据项目类型确定表名
Dim table_name
If LCase(project_type) = "pre" Then
    table_name = "[技术部测试管理表]"  '售前项目表
ElseIf LCase(project_type) = "base" Then
    table_name = "[技术部项目管理表]"  '售后项目表
Else
    Response.Write "{""code"":-1, ""msg"":""项目类型参数错误，请使用pre或base""}"
    Response.End
End If

'先检查要更新的记录是否存在
Dim rsCheck, sql
sql = "SELECT COUNT(*) FROM " & table_name & " WHERE ID=" & project_id
Set rsCheck = conn.Execute(sql)
If rsCheck(0) = 0 Then
    Response.Write "{""code"":-1,""msg"":""要更新的记录不存在""}"
    Response.End
End If
Set rsCheck = Nothing

'构建更新SQL
Dim update_sql
Dim update_fields()
Dim field_count
field_count = 0

'动态构建更新字段
If shangjia_date <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[sop上架日期] = '" & shangjia_date & "'"
    field_count = field_count + 1
End If

If xiajia_date <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[sop下架日期] = '" & xiajia_date & "'"
    field_count = field_count + 1
End If

If shangjia_person <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[sop上架人] = '" & shangjia_person & "'"
    field_count = field_count + 1
End If

If xiajia_person <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[sop下架人] = '" & xiajia_person & "'"
    field_count = field_count + 1
End If

'检查是否有字段需要更新
If field_count = 0 Then
    Response.Write "{""code"":-1,""msg"":""没有需要更新的字段""}"
    Response.End
End If

'构建完整的更新SQL
update_sql = "UPDATE " & table_name & " SET " & Join(update_fields, ", ") & " WHERE ID = " & project_id
'Response.Write update_sql
'Response.End


'执行更新操作
On Error Resume Next
conn.Execute update_sql
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""更新失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

' 返回成功响应
Response.Write "{""code"":1,""msg"":""更新操作成功""}"
%>