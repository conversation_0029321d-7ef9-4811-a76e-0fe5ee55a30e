﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>每批次检查看板</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">
		<!-- Chat css -->
		<link rel="stylesheet" href="css/chat.css">

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
	</head>

	<body>

		<!-- Loading starts -->
		<div id="loading-wrapper">
			<div class="spinner-border" role="status">
				<span class="sr-only">Loading...</span>
			</div>
		</div>
		<!-- Loading ends -->
		
	
			
			<!-- Page content start  -->
			<div class="page-content">				
				
				
				<!-- Main container start -->
				<div class="main-container">

					<!-- Page header start -->
					<div class="page-header">
						
						<!-- Breadcrumb start -->
						<ol class="breadcrumb">
							<li class="breadcrumb-item">当前检查批次号:1号,检查批次名称:1-大数据安全检查</li>
						</ol>
						<!-- Breadcrumb end -->
						<div class="app-actions">
							<div class="custom-dropdown-group">
								<div class="dropdown show">
									<button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
										菜单
									</button>
									<div class="dropdown-menu dropdown-menu-right" >
										<a class="dropdown-item" href="jianguanpingtai_pichi_tongzhi_view.html">1、检查通知管理</a>
										<a class="dropdown-item" href="jianguanpingtai_pichi_info_view.html">2、检查信息管理</a>
										<a class="dropdown-item" href="jianguanpingtai_pichi_danwei_view.html">3、检查单位管理</a>
										<a class="dropdown-item" href="jianguanpingtai_pichi_tongbao_view.html">4、下发通报管理</a>
										<a class="dropdown-item" href="jianguanpingtai_pichi_defen_view.html">5、检查得分管理</a>
										

									</div>
								</div>
								
							</div>
						</div>

					</div>
					<!-- Page header end -->

					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-2 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-eye1"></i>
								</div>
								<div class="sale-num">
									<h3>50家</h3>
									<p>本次计划检查单位</p>
								</div>
							</div>
						</div>
						<div class="col-xl-2 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon danger">
									<i class="icon-people_outline"></i>
								</div>
								<div class="sale-num">
									<h3>30家</h3>
									<p>已完成检查单位</p>
								</div>
							</div>
						</div>
						<div class="col-xl-2 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon warning">
									<i class="icon-people"></i>
								</div>
								<div class="sale-num">
									<h3>20家</h3>
									<p>未检查单位数量</p>
								</div>
							</div>
						</div>
						<div class="col-xl-2 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon success">
									<i class="icon-activity"></i>
								</div>
								<div class="sale-num">
									<h3>99分</h3>
									<p>本次平均得分</p>
								</div>
							</div>
						</div>
						<div class="col-xl-2 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-battery-charging"></i>
								</div>
								<div class="sale-num">
									<h3>100%</h3>
									<p>检查进度</p>
								</div>
							</div>
						</div>
						<div class="col-xl-2 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon danger">
									<i class="icon-user1"></i>
								</div>
								<div class="sale-num">
									<h3>10份</h3>
									<p>下发通报数量</p>
								</div>
							</div>
						</div>
					</div>
					<!-- Row end -->
<!-- Row start -->
<div class="row gutters">
	<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
		<!-- Card start -->
		<div class="card">
			<div class="card-header">
				<div class="card-title"><span class="icon-volume-2"></span> 关于本次检查工作的通知</div>
			</div>
			<div class="card-body">
				<div class="customScroll5">
					各市区县有关单位：

					为做好“七一”前后全市教育系统网络安全工作，根据市委安排, 决定组织开展2021年全市教育系统网络安全专项抽查工作。现将有关事宜通知如下：

					一、检查目的

					查找薄弱环节和安全隐患，分析网络与信息系统面临的风险，评估网络与信息系统的安全状况，强化信息安全意识、规范信息安全管理。对评估发现的信息安全漏洞或隐患，要立即采取相关措施进行整改，提高网络与信息系统的安全保障能力，切实保障全系统网络与信息系统的安全运行。

					二、检查对象及分组安排

					此次检查分五组同时进行，每组两人，由市电教馆和安全支撑单位各一人组成，刘鸿和田中原组成检查一组，负责检查直属学校、市中区和天桥区（第一人为检查组长，下同）。李尊刚和孔令舟组成检查二组，负责检查历下区、历城区和高新区。李冠强和巩立龙组成检查三组，负责检查莱芜区、钢城区和南山区。张承强和吴金函组成检查四组，负责检查槐荫区、长清区和平阴县。朱大闯和胡永新组成检查五组，负责检查章丘区、济阳区和商河县。区县教体局必查，同时每区县（直属学校）至少抽查两所学校。

					三、检查时间

					检查时间为6月23日-6月29日，具体时间安排由组长和检查对象确定。

					四、检查流程

					1、焦作市电教馆检查组长简要介绍网络安全检查工作的目的和检查过程，并听取被检查单位的网络安全进展情况。

					2、检查小组按照分工，开展现场检查工作，通过人员访谈、资料调阅、登录设备检查、工具扫描、人工测试等手段，全面发现问题，并对发现的问题进行汇总并给出解决建议。

					3、根据以上检查结果，检查组对当天检查情况进行汇总、综合分析，判定被检查单位互联网系统当前的安全状态，输出《xx单位安全检查小结》。

					4、总结会议。由技术人员通报检查情况，由检查组长补充或重点讲评。检查组将被检查单位签字确认的《xx单位安全检查小结》带回并留存。

					5、检查组按照检查发现的问题，撰写网络安全检查报告，下发至被检查单位，限期整改。被检查单位在收到报告后5个工作日内反馈整改情况至焦作市教育局。

						五、检查内容

						（一）安全管理

						评估指标、评价要素和评价标准参见附件1。

						（二）漏洞扫描

						检查组针对被检查单位的互联网系统涉及的服务器、网络设备、安全设备的IP地址进行扫描，主要从系统层面、web层面发现这些系统存在的漏洞，并对扫描过程中发现的高危和易于利用的漏洞进行验证，确保扫描结果真实可靠。

						通过扫描发现这些漏洞，提交给被检查单位的相关运维人员，督促整改，并做好整改后的复测工作。

						扫描范围：被检查单位互联网系统涉及的IP地址。

						在本项工作中，检查组输出《xx网站漏扫扫描报告》。

						（三）渗透测试

						针对被检查单位的互联网系统，检查组将通过渗透测试，挖掘其存在的漏洞，提出可落地的整改建议。

						渗透测试范围：被检查单位重要网站类系统。

						在本项工作中，检查组输出《xx单位互联网系统渗透测试报告》。

						（四）基线检查

						检查组对互联网系统涉及的主机、中间件、网络设备、安全设备的基线进行安全检查，找出配置层面存在的安全隐患。

						系统的网络设备和主机的安全性评估应主要考虑以下几个方面：

						n  是否最优的划分了VLAN和不同的网段，保证了每个用户的最小权限原则；

						n  内外网之间、重要的网段之间是否进行了必要的隔离措施；

						n  路由器、交换机等网络设备的配置是否最优，是否配置了安全参数；

						n  安全设备的接入方式是否正确，是否最大化的利用了其安全功能而又占系统资源最小，是否影响业务和系统的正常运行；

						n  主机服务器的安全配置策略是否严谨有效。

						许多安全设备如防火墙、入侵检测等设备也是人工评估的主要对象。因为这些安全系统的作用是为网络和应用系统提供必要的保护，其安全性也必然关系到网络和应用系统的安全性是否可用、可控和可信。目前还没有针对安全系统进行安全评估的系统和工具，只能通过手工的方式进行安全评估。

						安全系统的安全评估内容主要包括：

						n  安全系统是否配置最优，实现其最优功能和性能，保证网络系统的正常运行；

						n  安全系统自身的保护机制是否实现；

						n  安全系统的管理机制是否安全；

						n  安全系统为网络提供的保护措施，且这些措施是否正常和正确；

						n  安全系统是否定期升级或更新；

						n  安全系统是否存在漏洞或后门。

						在本项工作中，检查组输出《xx单位基线检查报告》。

						六、检查结果

						（一）综合评定

						根据以上检查结果，检查组对当天检查情况进行汇总、综合分析，判定被检查单位互联网系统当前的安全状态。

						在本项工作中，检查组输出《xx单位安全检查小结》。

						（二）整改通知

						检查组按照检查发现的问题，撰写整改通知书，限期整改。由焦作市教育局以函件通报的形式发给被检查单位。

						各区县和直属学校请于6月22日前将检查联系人及联系电话报送至指定邮箱。

						联系人：刘

						联系电话：

						电子邮箱：jndjg

						附件：1、网络安全管理评估标准表

						2、现场检查小结

						3、安全基线检查表

						

						

						焦作市网信办

						2021年6月21日


					
					
				</div>
			</div>
		</div>
		<!-- Card end -->
	</div>
	<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
		<!-- Card start -->
		<div class="card">
			<div class="card-header">
				<div class="card-title"><span class="icon-flag2"></span> 本次检查基本信息</div>
			</div>
			<div class="card-body">
				<div class="customScroll5" >
					<table class="table">
						<tbody>
							<tr>
								<td>检查负责人</td><td>李处长</td>
							</tr>
							<tr>
								<td>检查成员</td><td>黎明、刘凯、加龙</td>
							</tr>
							<tr>
								<td>检查分组</td><td>2组</td>
							</tr>
							<tr>
								<td>检查起至日期</td><td>2022年5月1日-2022年5月11日</td>
							</tr>
							<tr>
								<td>计划检查数量</td><td>50家</td>
							</tr>
							<tr>
								<td>实际检查数量</td><td>50家</td>
							</tr>
							<tr>
								<td>检查平均数量</td><td>100分</td>
							</tr>
							
						</tbody>
					</table>
					
				</div>
			</div>
		</div>
		<!-- Card end -->
	</div>
	<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
		<!-- Card start -->
		<div class="card">
			<div class="card-header">
				<div class="card-title"><span class="icon-ring_volume"></span> 下发检查通报 </div>
			</div>
			<div class="card-body">
				<div class="customScroll5" >
					<table class="table">
						<thead>
							<tr>
								<th>下发日期</th>
								<th>通报单位</th>
								<th>通报文稿</th>
								<th>签收人</th>
								<th>签收日期</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>2022年5月11日</td>
								<td>医院乙</td>
								<td class="text-info">关于202205批次检查的通报(点击查看明细)</td>
								<td>李明</td>
								<td>2022年5月11日</td>
							</tr>
							<tr>
								<td>2022年5月11日</td>
								<td>医院乙</td>
								<td class="text-info">关于202205批次检查的通报(点击查看明细)</td>

								<td>李明</td>
								<td>2022年5月11日</td>
							</tr>
							<tr>
								<td>2022年5月11日</td>
								<td>医院乙</td>
								<td class="text-info">关于202205批次检查的通报(点击查看明细)</td>

								<td>李明</td>
								<td>2022年5月11日</td>
							</tr>
							<tr>
								<td>2022年5月11日</td>
								<td>医院乙</td>
								<td class="text-info">关于202205批次检查的通报(点击查看明细)</td>

								<td>李明</td>
								<td>2022年5月11日</td>
							</tr>
							<tr>
								<td>2022年5月11日</td>
								<td>医院乙</td>
								<td class="text-info">关于202205批次检查的通报(点击查看明细)</td>

								<td>李明</td>
								<td>2022年5月11日</td>
							</tr>
							<tr>
								<td>2022年5月11日</td>
								<td>医院乙</td>
								<td class="text-info">关于202205批次检查的通报(点击查看明细)</td>

								<td>李明</td>
								<td>2022年5月11日</td>
							</tr>
							
							
						</tbody>
					</table>
					
				</div>
			</div>
		</div>
		<!-- Card end -->
	</div>
</div>

<!-- Row end -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">本次检查单位得分统计</div>
								</div>
								<div class="card-body">

									<!-- Row start -->
									<div class="row gutters align-items-center">
										<div class="col-xl-2 col-lg-3 col-md-12 col-sm-12 col-12">
											<div class="monthly-avg">
												<h5>检查单位</h5>
												<div class="avg-block">
													<h3 class="avg-total text-danger">25家</h3>
													<h6 class="avg-label">已完成检查单位数量</h6>
												</div>
												<div class="avg-block">
													<h3 class="avg-total text-info">50家</h3>
													<h6 class="avg-label">计划检查单位数量</h6>
												</div>
											</div>
										</div>
										<div class="col-xl-8 col-lg-6 col-md-12 col-sm-12 col-12">
											<div id="lineRevenueGraph"></div>
										</div>
										<div class="col-xl-2 col-lg-3 col-md-12 col-sm-12 col-12">
											<div class="monthly-avg">
												<h5>检查单位得分</h5>
												<div class="avg-block">
													<h3 class="avg-total text-danger">100分</h3>
													<h6 class="avg-label">最高分:医院甲</h6>
												</div>
												<div class="avg-block">
													<h3 class="avg-total text-info">58分</h3>
													<h6 class="avg-label">最低分:医院乙</h6>
												</div>
											</div>
										</div>
									</div>
									<!-- Row end -->

								</div>
							</div>
						</div>
						
					</div>
					<!-- Row end -->
					
					
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">本次检查单位常见问题类型得分</div>
								</div>
								<div class="card-body">

									<!-- Row start -->
									<div class="row gutters align-items-center">
										<div class="col-xl-2 col-lg-3 col-md-12 col-sm-12 col-12">
											<div class="monthly-avg">
												<h5>检查单位</h5>
												<div class="avg-block">
													<h3 class="avg-total text-danger">25家</h3>
													<h6 class="avg-label">已完成检查单位数量</h6>
												</div>
												<div class="avg-block">
													<h3 class="avg-total text-info">50家</h3>
													<h6 class="avg-label">计划检查单位数量</h6>
												</div>
											</div>
										</div>
										<div class="col-xl-8 col-lg-6 col-md-12 col-sm-12 col-12">
											<div id="basic-column-stack-graph-fullheight"></div>
										</div>
										<div class="col-xl-2 col-lg-3 col-md-12 col-sm-12 col-12">
											<div class="monthly-avg">
												<h5>检查单位得分</h5>
												<div class="avg-block">
													<h3 class="avg-total text-danger">100分</h3>
													<h6 class="avg-label">最高分:医院甲</h6>
												</div>
												<div class="avg-block">
													<h3 class="avg-total text-info">58分</h3>
													<h6 class="avg-label">最低分:医院乙</h6>
												</div>
											</div>
										</div>
									</div>
									<!-- Row end -->

								</div>
							</div>
						</div>
					</div>
					<!-- Row end -->
					<div class="row gutters">
						<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">检查单位得分排名(由高到低)</div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<div class="products-sold-container">
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">1</div>
													<div class="product-title">
														<div class="title">用户1</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">2</div>
													<div class="product-title">
														<div class="title">单位2</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">3</div>
													<div class="product-title">
														<div class="title">单位3</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">99分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">4</div>
													<div class="product-title">
														<div class="title">单位4</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">95分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">5</div>
													<div class="product-title">
														<div class="title">单位5</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">93分</div>
												</div>
											</div>
											
											
										
											
										</div>
									</div>
								</div>
							</div>
							<!-- Card end -->
						</div>
						<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">检查常见问题得分排名(由高到低)</div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<div class="products-sold-container">
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">1</div>
													<div class="product-title">
														<div class="title">用户1</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">2</div>
													<div class="product-title">
														<div class="title">单位2</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">3</div>
													<div class="product-title">
														<div class="title">单位3</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">99分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">4</div>
													<div class="product-title">
														<div class="title">单位4</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">95分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">5</div>
													<div class="product-title">
														<div class="title">单位5</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">93分</div>
												</div>
											</div>
											
											
										
											
										</div>
									</div>
								</div>
							</div>
							<!-- Card end -->
						</div>
						<div class="col-xl-4 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">常见问题分布</div>
								</div>
								<div class="card-body ">
									<div class="customScroll5">
										<div id="basic-donut-graph"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">检查时间轴 </div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<ul class="team-activity">
											<li class="product-list clearfix">
												<div class="product-time">
													<p class="date center-text">2022年5月10日</p>
													<span class="badge badge-danger">进行中</span>
												</div>
												<div class="product-info">
													<div class="activity">
														<h6>检查单位:医院甲</h6>
														<p>计划检查项100项,已完成40项</p>	
													</div>
													<div class="status">
														<div class="progress">
															<div class="progress-bar bg-danger" role="progressbar" aria-valuenow="49" aria-valuemin="0" aria-valuemax="100" style="width: 49%">
																<span class="sr-only">49% Complete (success)</span>
															</div>
														</div>
														<p>得分:49分</p>
													</div>
												</div>
											</li>
											<li class="product-list clearfix">
												<div class="product-time">
													<p class="date center-text">2022年4月1日</p>
													<span class="badge badge-info">已完成</span>
												</div>
												<div class="product-info">
													<div class="activity">
														<h6>检查单位:医院已</h6>
														<p>计划检查项100项,已完成100项</p>	
													</div>
													<div class="status">
														<div class="progress">
															<div class="progress-bar bg-info" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
																<span class="sr-only">100% Complete (success)</span>
															</div>
														</div>
														<p>得分:100分</p>
													</div>
												</div>
											</li>
											<li class="product-list clearfix">
												<div class="product-time">
													<p class="date center-text">2022年4月1日</p>
													<span class="badge badge-info">已完成</span>
												</div>
												<div class="product-info">
													<div class="activity">
														<h6>检查单位:医院乙</h6>
														<p>计划检查项100项,已完成100项</p>	
													</div>
													<div class="status">
														<div class="progress">
															<div class="progress-bar bg-info" role="progressbar" aria-valuenow="98" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
																<span class="sr-only">100% Complete (success)</span>
															</div>
														</div>
														<p>得分:98分</p>
													</div>
												</div>
											</li>
											<li class="product-list clearfix">
												<div class="product-time">
													<p class="date center-text">2022年4月1日</p>
													<span class="badge badge-info">已完成</span>
												</div>
												<div class="product-info">
													<div class="activity">
														<h6>检查单位:医院乙</h6>
														<p>计划检查项100项,已完成100项</p>	
													</div>
													<div class="status">
														<div class="progress">
															<div class="progress-bar bg-info" role="progressbar" aria-valuenow="98" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
																<span class="sr-only">100% Complete (success)</span>
															</div>
														</div>
														<p>得分:98分</p>
													</div>
												</div>
											</li>
											<li class="product-list clearfix">
												<div class="product-time">
													<p class="date center-text">2022年4月1日</p>
													<span class="badge badge-info">已完成</span>
												</div>
												<div class="product-info">
													<div class="activity">
														<h6>检查单位:医院乙</h6>
														<p>计划检查项100项,已完成100项</p>	
													</div>
													<div class="status">
														<div class="progress">
															<div class="progress-bar bg-info" role="progressbar" aria-valuenow="98" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
																<span class="sr-only">100% Complete (success)</span>
															</div>
														</div>
														<p>得分:98分</p>
													</div>
												</div>
											</li>
											
											
											
										</ul>
									</div>
								</div>
							</div>
						</div>
						<div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">检查报告 </div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<table id="basicExample" class="table custom-table">
											<thead>
												<tr>
													<th>序号</th>
													<th>检查单位</th>
													<th>得分</th>
													<th>报告下载</th>
												</tr>
											</thead>
											<tbody>
												<tr>
													<td>1</td>
													<td>医院乙</td>
													<td>98分</td>
													<td><span class="icon-download"></span></td>
												</tr>
												<tr>
													<td>2</td>
													<td>医院乙</td>
													<td>98分</td>
													<td><span class="icon-download"></span></td>
												</tr>
												<tr>
													<td>3</td>
													<td>医院乙</td>
													<td>98分</td>
													<td><span class="icon-download"></span></td>
												</tr>
												<tr>
													<td>4</td>
													<td>医院乙</td>
													<td>98分</td>
													<td><span class="icon-download"></span></td>
												</tr>
												<tr>
													<td>5</td>
													<td>医院乙</td>
													<td>98分</td>
													<td><span class="icon-download"></span></td>
												</tr>
											</tbody>

										</table>
										
									</div>
								</div>
							</div>
						</div>
						
					</div>
					<!-- Row end -->

					
					
					
					
					

				</div>
				<!-- Main container end -->

			
				

				
			</div>
			<!-- Page content end -->

	
			

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>


		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>

		<!-- Polyfill JS -->
		<script src="vendor/polyfill/polyfill.min.js"></script>
		<script src="vendor/polyfill/class-list.min.js"></script>

		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		
		<!-- Peity Charts -->
		<script src="vendor/peity/peity.min.js"></script>
		<script src="vendor/peity/custom-peity.js"></script>
		
		<!-- Circleful Charts -->
		<script src="vendor/circliful/circliful.min.js"></script>
		<script src="vendor/circliful/circliful.custom.js"></script>
		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>

		

		<!-- Bar Graphs -->
		<script src="vendor/apex/examples/bar/basic-bar-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-graph-grouped.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph-full-width.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-negative-values.js"></script>

		<!-- Candlestick Graphs -->
		<script src="vendor/apex/examples/candlestick/basic-candlestick-graph.js"></script>

		<!-- Column Graphs -->
		<script src="vendor/apex/examples/column/basic-column-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-graph-datalables.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph-fullheight.js"></script>

		<!-- Line Graphs -->
		<script src="vendor/apex/examples/line/basic-line-graph.js"></script>
		<script src="vendor/apex/examples/line/line-with-data-labels.js"></script>
		<script src="vendor/apex/examples/line/stepline.js"></script>

		<!-- Donut Graphs -->
		<script src="vendor/apex/examples/pie/basic-donut-graph.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>