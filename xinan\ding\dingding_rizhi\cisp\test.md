- 应急演练平台（王炯）
        - 湖南数据安全应急演练业务支撑
        - （1）参加湖南管局数据安全应急演练对接企业启动会议：会议明确时间节点、网络打通方式、虚拟环境部署方式等
        - （2）修复演练配置发布后并启动后演练节点配置顺序错乱问题：错用hashmap问题，该有用有序linkhashmap，并同步发布生产、测试、开发环境
        - （3）清洗最新优化版本环境数据创建应用一键部署包，并测试
        - （4）演练场景构建相关支撑（有关复杂docker启动命令平台支持情况解答、以及场景数据初始化方案讨论）
        - （5）沟通应急演练平台物理服务器最低要求配置（不考虑后续拓展以及更多复杂场景）：CPU：E5-2696 V3*2个，36核72线程 内存：32G*8=256G 硬盘：三星1T*2个 固态
        - （6）对接张鹏提供工业会联网场景所需资源（3台win7 + 2台centos）
        - （7）核查到货（5台）服务器的系统以及配置情况、服务器按标签分类、配置初始网络等；
        - （8）部署数据安全应急演练平台到五台物理服务器（软路由、应用服务、操作终端、靶机等资源配置，具体场景资源配置待分配）
- SOP管理平台 v3.0（金龙、若男）
    - 功能测试优化以及部分bug修复为主
        - 修改项目预警日志列表过滤状态，以确保只返回未处理的日志，删除设置view默认值的逻辑，改为根据实际传参情况处理
        - 修改新增预上架单中设备序列号过滤只在公司库房的状态，增加销售保管中的状态查询，修改接口查询方式，修改sql查询脚本
        - 梳理出调用非凡安全运营平台设备的接口，便于非凡开发新的接口能跟设备接口数据结构及调用方式尽量保持一致
        - 修复任务看板中的循环任务一直能够新增的问题，调整任务数量比较逻辑并优化查询条件， 修改 sopId 的代码行，改为设置 sopProjectId
        - 新增公司任务以及个人任务的所有数据的查询，去掉截止日期的过滤，增加新的判断查询逻辑
        - 添加今日测试到期的设备提醒功能，在测试延期中设备设置为不在继续测试时候，如果七天之后，设备到期时候，不会继续提醒，业务需要增加到期当天提醒设备运营人员
        - 修改定时任务执行失败的问题
        - 优化服务项名称的查询逻辑,修复了服务项名称查询中数据无法正常显示的逻辑问题。通过增加表关联数据的查询方式，来修复遗留的数据问题
        - 部署sop测试服务器，同步测试服务器的数据库结构与正式数据库保持一致，同步正式数据库的数据到测试数据库中，部署前端代码，调试测试服务器数据内容
        - 调试sop验证码识别问题，测试通过Tesseract方案能够解决问题，识别有问题，再次尝试自己训练验证码生成脚本
    - sop管理平台直连设备以达到预警目的的研发方向，非凡可提供okm一级平台的接口开发
- 数据泄露监测平台（若男）
    - 梳理数据泄露监测平台相关优化点与bug提交禅道
    - 修复修改数据泄露弹窗内不同输入框展示重叠等问题
    - 数据泄露详情页加入中文简述展示
    - 将原有组件式列表与详情拆分为详情页分别展示代码、文档 数据泄露等
    - 优化为路由式访问 支持可返回列表  搜索框联动同步修改完成
    - 根据不同页面传参将本地存储记录关键词与搜索词等 完成原功能恢复

- 数据安全风险评估系统 v3.0（李成、若男、博文、俊超、俊虎远程参会）
    - 组织完成第一个版本产品原型图的评审，后续需要对第一次评审结果进行修改；
    - 明确该项目的项目经理或产品经理，博文为第一牵头人，电信项目需求由俊虎负责；
- CCRC资质不符合项整改（李成）
    - 完成诸如：SVN服务器、git仓库、禅道项目及禅道bug的提交，其中包括拉取非凡wiki更新日志，编写自动脚本、部署管理系统等
        - 1.8-未制定版本迭代规则及版本迭代记录.docx
        - 1.10,1.11-配置管理文档.docx
        - 1.12-产品编译、生成、打包过程（翔宇，4.1章节）.docx
        - 1.14-使用SVN统一管理.docx
        - 1.15-使用禅道对产品的缺陷进行管理.docx
        - 1.16-使用SVN对灌装发布包进行管理.docx
        - 1.17-使用gitlab的tag功能对发布版本进行管理.docx
        - 2.8-测试岗 量化标准考核表.xlsx
        - 2.8-未明确区分针对研发、测试人员的考核指标.docx
        - 2.9-工具和设备清单台账.xlsx
        - 3.2-脆弱性评估人员岗位职责.xlsx
        - 4.1-关键件清单.docx
        - 4.2-第三方中间件、开源工具安全管理记录.xlsx
- 其他
    - 编写公司研发产品审计类系列文档，提交给财务部

本周计划：
- 完成第2版原型修改；风险监测后端优化；
- 效率方面，梳理出来节省人员工作量方面有哪些具体的功能？

1. 定时任务自动同步预上架设备信息
2. 自动同步sop项目信息，包括售前项目以及售后项目
3. 自动同步sop项目所绑定的客户单位数据
4. 自动同步处理上架审批流
5. 自动同步OA用户信息
6. 自动同步主机资产和网站资产
7. 自动同步流量监测告警数据
8. 自动同步蜜罐失陷主机、攻击者画像、时间轴告警数据
9. 自动同步阻断日志数据
10. 自动同步网站风险和网站风险汇总数据
11. 自动同步网站风险和网站风险汇总数据
12. 自动同步主机agent、主机风险台账
13. 自动同步系统资源、引擎状态
14. 用户离职交接批量数据替换
15. sop服务模板配置，大大简化配置的复杂度
16. 预警配置的通用配置模板，简化相同配置的多次操作
17. 设备最新授权日期自动同步

- 流程方面，平台上流程标准化方面，哪些实现了，哪些还没有实现，梳理出来已经实现的功能有哪些？
1. 实现从客户单位-》项目-》设备，层级关系数据管理，数据逻辑清晰展示
2. 项目下的任务看板管理，从sop服务模板的一件导入数据开始，然后可以给其他用户配置任务，之后就在公司任务中以及任务处理状况中，监测任务的执行的状态，以及每个员工的任务状况
3. 系统监测方面，通过自动拉取数据，然后根据预警配置阈值，来自动监测蜜罐以及流量监测的数据，超过阈值的数据，自动根据发送告警信息到钉钉

- 质量方面，标准化输出日、周报，还有哪些质量方面的控制、约束，比如定时任务、绩效任务等，梳理出来清单。
1. 从爬虫任务替换为新的api接口调用方式，能够提高数据的精准率，提高统计数据的效率
2. 从安全运营平台拉取过来的数据，通过动态配置，实现数据预警提醒
3. 通过发送的日报周报数据，做出日周报趋势图，通过折线趋势，来分析安全运营数据的变化曲线状况