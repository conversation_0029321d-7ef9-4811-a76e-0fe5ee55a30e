.bootstrap-tagsinput {
	width: 100%;
	padding: .375rem .75rem;
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #222222;
	background-color: #ffffff;
	background-clip: padding-box;
	border: 1px solid #cfd1d8;
	border-radius: .25rem;
	transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
.bootstrap-tagsinput input {
	border: none;
	box-shadow: none;
	outline: none;
	background-color: transparent;
	padding: 0 6px;
	color: #222222;
	margin: 0;
	width: auto;
	max-width: inherit;
}
.bootstrap-tagsinput.form-control input::-moz-placeholder {
	color: #777;
	opacity: 1;
}
.bootstrap-tagsinput.form-control input:-ms-input-placeholder {
	color: #777;
}
.bootstrap-tagsinput.form-control input::-webkit-input-placeholder {
	color: #777;
}
.bootstrap-tagsinput input:focus {
	border: none;
	box-shadow: none;
}
.bootstrap-tagsinput .tag {
	margin: 1px 2px 1px 0;
	color: #ffffff;
	background: #007ae1;
	font-size: .725rem;
	border-radius: 50px;
	font-weight: 400;
}
.bootstrap-tagsinput .tag [data-role="remove"] {
	margin-left: 5px;
	cursor: pointer;
}
.bootstrap-tagsinput .tag [data-role="remove"]:after {
	content: "x";
	padding: 0px 2px;
}
.bootstrap-tagsinput .tag [data-role="remove"]:hover {
	box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.bootstrap-tagsinput .tag [data-role="remove"]:hover:active {
	box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}







.tt-menu {
	min-width: 150px;
	background-color: #fff;
	border: 1px solid rgba(72, 94, 144, 0.16);
	padding: 5px;
	border-bottom-right-radius: 0.25rem;
	border-bottom-left-radius: 0.25rem;
	box-shadow: 0 6px 8px 2px rgba(28, 39, 60, 0.04);
}

.tt-suggestion {
	padding: 2px 10px;
}
.tt-suggestion:hover, .tt-suggestion:focus {
	cursor: pointer;
	background-color: #0168fa;
	color: #ffffff;
}