<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--#include file="../../../../shujuku.asp"-->



<%
	if  request("look")="addok" then 
			name = session("mingzhi")
			xmid=request("xmid")'项目id'
			a1=request("a1") 
			a2=request("a2") 
			a3=request("a3") 
			a5=request("a5") 
			a8=request("a8") 
			rate=request("rate") 
			tax=request("tax") 
			ysp_id=request("ysp_id") 
			ysp_jicheng_id=request("ysp_jicheng_id") 

			f_SaveName=request("imgUrl")
			
			neirong=request("neirong") 

			'提交到数据库里
			 
			 sql = "select top 1 * from 开发票管理"
            rsxa.open sql, connxa, 3, 2
            rsxa.addnew
			if  a1<>"" then 
            	rsxa("开票日期") = a1 ' 使用之前获取的 a1 值
			end if 
			if a2<>"" then 
           	 rsxa("开票金额") = a2 ' 使用之前获取的 a2 值
			end if 

            rsxa("单位") = a3 ' 使用之前获取的 a3 值
            rsxa("开票内容") = neirong ' 使用之前获取的 neirong 值
            rsxa("发票税种") = a5 ' 使用之前获取的 a5 值
          	rsxa("发票号") = a8
          	rsxa("缴税金额") = tax
          	rsxa("税率") = rate
            
            rsxa("imgUrl") = "../xinan/shoufapiao/upfiles_caiwubu/"&f_SaveName
         
            rsxa("录入人") = session("mingzhi")
            rsxa("录入时间") = now()
            rsxa("缴税月份") = month(a1)
            rsxa("是否缴税") = 1
            
            If xmid <> "" Then ' 使用之前获取的 xmid 值
                rsxa("lt合同id") = xmid
            Else
                rsxa("lt合同id") = Null
            End If

            rsxa.update
            rsxa.close

			'在自动添加到合同文件柜里
			sql="select top 1 * from 合同管理_20_财务文件柜"
			rserp.open sql,connerp,3,2
			rserp.addnew
			rserp("文件名")=a3&"开的发票金额"&a2
			rserp("分类")="财务-发票与收据"
			rserp("文件说明")=NUll
			rserp("文件大小")=f_Size
			rserp("内部url")="../xinan/shoufapiao/upfiles_caiwubu/"&f_SaveName
			rserp("文件类型")=f_Ext
			rserp("录入人")=session("mingzhi")
			rserp("录入时间")=now()
			rserp("项目管理表id")=xmid
			rserp.update
			rserp.close

			response.write "<script language='javascript'>alert('添加成功！');location.href='../../../shoufapiao/YYB_CCB_shoufapiao.asp';</script>"
			response.end
	end if 











IF  request("look")=""  then
	xmid=0  '合同号'
	kehu=""  '客户单位'
	jine=0   '应收发票金额'
	rate=0
	tax=0
	if  request("xmid")<>"" then 
		xmid=Replace(Replace(Replace(Replace(Replace(request("xmid"),"'",""),"or",""),"and",""),"--",""),";","") 
	end if 
	 
	if  request("jine")<>"" then 
		jine=Replace(Replace(Replace(Replace(Replace(request("jine"),"'",""),"or",""),"and",""),"--",""),";","") 
	end if 
	 
	if  request("rate")<>"" then 
		rate=Replace(Replace(Replace(Replace(Replace(request("rate"),"'",""),"or",""),"and",""),"--",""),";","") 
	end if 
	 
	if  request("tax")<>"" then 
		tax=Replace(Replace(Replace(Replace(Replace(request("tax"),"'",""),"or",""),"and",""),"--",""),";","") 
	end if 
	
%>

<html xmlns="http://www.w3.org/1999/xhtml">

	<head>
		<!-- Required meta tags -->
	 

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="../../../../images/2017/xinanlogo22.png" />

		<!-- Title -->
		<title>新增开发票信息</title>
	<link rel="stylesheet" href="../../../../upload2019/js/ssi-uploader/styles/ssi-uploader.css"/>
		<script src="../../../../upload2019/js/jquery-1.11.3.min.js"></script>
		<script src="../../../../upload2019/js/ssi-uploader/js/ssi-uploader_tijiao.js"></script>

		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="../../../../js/Tycoon/css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="../../../../js/Tycoon/fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="../../../../js/Tycoon/css/main.css">
<!-- Datepicker css -->
		<link rel="stylesheet" href="../../../../js/Tycoon/vendor/datepicker/css/classic.css" />
		<link rel="stylesheet" href="../../../../js/Tycoon/vendor/datepicker/css/classic.date.css" />

		<!-- Data Tables -->
		<link rel="stylesheet" href="../../../../js/Tycoon/vendor/datatables/dataTables.bs4.css" />
		<link rel="stylesheet" href="../../../../js/Tycoon/vendor/datatables/dataTables.bs4-custom.css" />
		<link href="../../../../js/Tycoon/vendor/datatables/buttons.bs.css" rel="stylesheet" />
		 <!-- 列固定需要用的代码 -->
	<link rel="stylesheet" href="../../../../js/Tycoon/js/dataTables.dataTables.css" />
	<link rel="stylesheet" href="../../../../js/Tycoon/js/fixedColumns.dataTables.min.css" />

	 

	<!-- Notify css -->
	<link rel="stylesheet" href="../../../../js/Tycoon/vendor/notify/notify-flat.css" />
	
	 
 
		<!-- *************
			************ Vendor Css Files *************
		************ -->
		<style>
				#myTable {  
					width: 2000px; /* 或者你需要的宽度 */  
					border-collapse: collapse; /* 可选，使边框合并 */  
					}  
					
					#myTable thead tr {  
					position: sticky; /* 启用粘性定位 */  
					top: 0; /* 相对于视口定位，始终在顶部 */  
					background-color: #5a8dee ; /* 可选，为表头设置背景色，以便在滚动时与内容区分 */  
					z-index: 10; /* 提高z-index以确保表头在其他内容之上 */  
					}  
					
					#myTable th, #myTable td {  
					padding: 8px; /* 可选，设置单元格内边距 */  
					text-align: left; /* 可选，设置文本对齐方式 */  
					border-bottom: 1px solid #ddd; /* 可选，设置单元格底部边框 */  
					}

					/* 添加预览图片样式 */
					.upload-preview-container {
						margin-top: 10px;
					}
					
					.upload-preview {
						max-width: 600px; /* 限制最大宽度 */
						max-height: 500px; /* 限制最大高度 */
						width: auto;
						height: auto;
						object-fit: contain; /* 保持图片比例 */
						border: 1px solid #ddd;
						border-radius: 4px;
						box-shadow: 0 2px 4px rgba(0,0,0,0.1);
					}

					.pdf-preview {
						padding: 10px;
						background: #f5f5f5;
						border: 1px solid #ddd;
						border-radius: 4px;
						margin-top: 10px;
					}
				</style>
	
	</head>

	<body>

	
<!--显示上传附件 -->

<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
	<div class="modal-dialog  modal-xl" role="document">
		<div class="modal-content">
			<div class="modal-header">
				 <h4 class="modal-title" id="exampleModalLabel"></h4>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close" id="btn_modal_close"><span aria-hidden="true">×</span></button>
			</div>
			<div class="modal-body">
			   <iframe id="NoPermissioniframe" width="100%" height="600px" frameborder="0" ></iframe>
			</div>
		   
		</div>
	</div>
</div>
<script type="text/javascript">

//添加
function  show_add(url){
var _this=$(this);
$("#NoPermissioniframe").attr("src",url);
$('#exampleModal').modal({show:true});
}

</script>

<!-- Main container start -->
<div class="card">
	<div class="card-header">
		<!-- <button onclick="location.reload()" class="btn  btn-primary btn-sm"><i class="icon-cycle"></i> 刷新</button>
		<button onclick="window.open(window.location.href, '_blank')" class="btn btn-primary btn-sm"><i class="icon-filter_none"></i> 新窗口打开</button> -->
		<h3>新增开发票信息</h3>
			<div class="col-md-12 " id="tixing"></div>
	</div>
	<div class="card-body">
	 
	 
		<form id="myform" name="myform" method="post" action="?look=addok" >
			<div class="container-fluid">
				<div class="row mb-3">
					<div class="col-6">
						<div class="form-group">
							<label>上传发票[自动识别发票内容]
								 <!-- <span class="text-danger" id="tixingtext"></span>  -->
							</label>
							<input type="hidden" id="imgUrl" name="imgUrl" name="imgUrl">
							<div>
								<input type="file" id="fileToUpload" class="form-control"  accept=".jpg,.jpeg,.png,.pdf">
								<button type="button" class="btn btn-info" id="uploadBtn" onclick="uploadFile()">上传</button>
							</div>
							<div class="upload-preview-container">
								<img id="preview" class="upload-preview">
								<div id="pdfPreview" class="pdf-preview"></div>
								<!-- <button type="button" class="remove-btn  btn-info btn-sm" id="removeBtn" onclick="removeFile()">&times;</button> -->
							</div>
						</div>

						<!-- <div class="form-group">
							<label>上传发票 <span class="text-danger" id="tixingtext"></span> </label>
							<input type="file" name="ssi-upload"  id="ssi-upload"  >
							
							<small class="form-text text-muted">支持格式："JPG","jpg","png","gif","pdf"</small>
						</div> -->
					</div>
				</div>
				<div class="row mb-3">
					<div class="col-12">
					<div class="form-group">
						<label>发票联</label>
						<div class="form-row">
							<div class="col">
								<label>开票日期<span class="text-danger">*</span></label>
								<div class="custom-date-input">
									<input name="a1" id="a1" type="text" class="form-control datepicker" placeholder="开票日期" required>
								</div>
							</div>
							<div class="col">
								<label>发票号码<span class="text-danger">*</span></label>
								<input name="a8" id="a8" type="text" class="form-control"  required>
								<input type="hidden" id="ysp_id" name="ysp_id" value="<%=yingshoufapiao_id%>" >
								<input type="hidden" id="ysp_jicheng_id" value="<%=yingshoufapiao_jicheng_id%>" >
								 
							</div>

							<div class="col">
								<label>发票税种<span class="text-danger">*</span></label>
								<select name="a5" id="a5" class="form-control">
								<option selected>增值税专票</option>
								<option>增值税普票</option>
								<option>普票</option>
								</select>
							</div>
						</div>

						<div class="form-row">
							
							<div class="col">
								<label>发票总额[含税额]<span class="text-danger">*</span></label>
								<div class="input-group">
									<input name="a2" id="a2" type="text"  class="form-control" value="<%=jine%>"  required>
									<div class="input-group-append">
										<span class="input-group-text">元</span>
									</div>
								</div>
							</div>
							
							<div class="col">
								<label>发票税率<span class="text-danger">*</span></label>
								<div class="input-group">
									<input name="rate" id="rate" type="text"  class="form-control" value="<%=rate%>"  required>
									<div class="input-group-append">
										<span class="input-group-text">%</span>
									</div>
								</div>
							</div>

							<div class="col">
								<label>缴税金额<span class="text-danger">*</span></label>
								<div class="input-group">
									<input name="tax" id="tax" type="text" class="form-control" value="<%=tax%>" required>
								</div>
							</div>
						</div>
					</div>
					</div>
				</div>

				<div class="row mb-3">
					<div class="col-12">
					<div class="form-group">
						<label>购买方单位名称<span class="text-danger">*</span></label>
						<input name="a3" id="a3" type="text" class="form-control"  value="<%=kehu%>" >
					</div>
					</div>
				</div>
				<div class="row mb-3">
					<div class="col-12">
					<div class="form-group">
						<label>发票归属信安项目<span class="text-danger">*</span></label>
						<select name="xmid" id="xmid" class="form-control">
							<%
							if xmid<>0 and xmid<>"" then 
								sql="select id,合同ID,项目名称,客户单位 from [合同管理_1_项目信息] where id="&xmid&" order by id desc"
								rserp.open sql,connerp,1,2
								if  not rserp.eof then 
								%>
								<option selected value=<%=rserp("id")%>>[<%=rserp("合同ID")%>]【<%=rserp("项目名称")%>】 [客户：<%=rserp("客户单位")%>]</option>
								<%
								end if 
								rserp.close
							else 
							%>
								<option value="" selected></option>
								<%
								
								sql="select id,合同ID,项目名称,客户单位 from [合同管理_1_项目信息] where 是否完成=0 order by id desc"
								rserp.open sql,connerp,1,2
								while not rserp.eof
								%>
								<option value=<%=rserp("id")%>>[<%=rserp("合同ID")%>]【<%=rserp("项目名称")%>】 [客户：<%=rserp("客户单位")%>]</option>
								<%
								rserp.movenext
								wend
								rserp.close
							end if 
								%>
						</select>
					</div>
					</div>
				</div>

				<div class="row mb-3">
					<div class="col-12">
						<div class="form-group">
							<label>备注</label>
							<script type="text/javascript" charset="gb2312" src="../../../../js/ueditor/ueditor.config.js"></script>
							<script type="text/javascript" charset="gb2312" src="../../../../js/ueditor/ueditor.all.min.js"> </script>
							<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
							<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
							<script type="text/javascript" charset="gbk" src="../../../../js/ueditor/lang/zh-cn/zh-cn.js"></script>

							<textarea id="neirong" name="neirong" type="text/plain" style="width:100%;height:100px;">

							</textarea>
							<script type="text/javascript">
							var ue = UE.getEditor('neirong');
							</script>
								<!-- <table style="width: 100%;border-collapse:collapse;border: 1px red solid " border="1" cellspacing="0"  cellpadding="0"><tr><td>货号</td><td>货品名称</td><td>规格</td><td>单位</td><td>数量</td><td>单价</td><td>金额</td><td>税率</td><td>税额</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr></table> -->


						
						</div>
						<button class="btn btn-info " id="tijiao" type="submit">提交</button>
					</div>
				</div>
				
				

			</div>
			<script>
				// 新增表单验证逻辑
				function checkFormValidity() {
					const a1 = $('#a1').val().trim();
					const a2 = $('#a2').val().trim();
					const a8 = $('#a8').val().trim();
					//$('#tijiao').prop('disabled', !(a1 && a2 && a8));
				}
				
				// 初始化检查
				$(document).ready(function() {
					// 绑定输入事件
					$('#a1, #a2, #a8').on('input', checkFormValidity);
					
					// 处理日期选择器
					$('.datepicker').on('change', function() {
						checkFormValidity();
					});
				
					// 初始状态检查
					checkFormValidity();
				});
				</script>

				<script>

					function uploadFile() {
						var fileInput = document.getElementById('fileToUpload');
						var file = fileInput.files[0];
						if (!file) {
							alert('请选择文件');
							return;
						}

						var formData = new FormData();
						formData.append('fileToUpload', file);

						var uploadBtn = document.getElementById('uploadBtn');
						uploadBtn.disabled = true;
						uploadBtn.innerHTML = '上传中...';

						fetch('upload.asp', {
							method: 'POST',
							body: formData
						})
						.then(response => response.json())
						.then(data => {
							if (data.success) {
								document.getElementById('imgUrl').value = data.filename;
								
								// 自动填充发票信息
								if (data.apiResponse) {
									if (data.apiResponse.date) document.getElementById('a1').value = data.apiResponse.date;
									if (data.apiResponse.no) document.getElementById('a8').value = data.apiResponse.no;
									if (data.apiResponse.total_price) document.getElementById('a2').value = data.apiResponse.total_price;
									if (data.apiResponse.buyer) document.getElementById('a3').value = data.apiResponse.buyer;
									if (data.apiResponse.rate) document.getElementById('rate').value = data.apiResponse.rate.replace('%', '');
									if (data.apiResponse.tax) document.getElementById('tax').value = data.apiResponse.tax
									// 设置发票类型
									if (data.apiResponse.type) {
										var a5Select = document.getElementById('a5');
										if (data.apiResponse.type.includes('专票')) {
											a5Select.value = '增值税专票';
										} else if (data.apiResponse.type.includes('普票')) {
											a5Select.value = '增值税普票';
										}
									}
								}
								
								// 预览处理
								var preview = document.getElementById('preview');
								var pdfPreview = document.getElementById('pdfPreview');
								
								// 清除现有预览
								if (preview) preview.style.display = 'none';
								if (pdfPreview) pdfPreview.style.display = 'none';
								
								// 根据文件类型显示不同的预览
								if (file.type === 'application/pdf') {
									if (pdfPreview) {
										pdfPreview.style.display = 'block';
										pdfPreview.innerHTML = '<p>PDF文件已上传: ' + data.filename + '</p>';
									}
								} else if (file.type.startsWith('image/')) {
									if (preview) {
										preview.style.display = 'block';
										preview.src = "/xinan/shoufapiao/upfiles_caiwubu/" + data.filename;
									}
								}
								
								// 重置上传按钮状态
								uploadBtn.innerHTML = '重新上传';
								uploadBtn.disabled = false;
								
								// 清空文件上传域
								document.getElementById('fileToUpload').value = '';
								
								// 显示成功消息
								alert('上传成功！发票信息已自动填充。');
								
							} else {
								alert('上传失败：' + (data.error || '未知错误'));
								uploadBtn.innerHTML = '上传';
								uploadBtn.disabled = false;
							}
						})
						.catch(error => {
							console.error('上传错误：', error);
							alert('上传出错：' + error);
							uploadBtn.innerHTML = '上传';
							uploadBtn.disabled = false;
						});
					}

					function removeFile() {
						var preview = document.getElementById('preview');
						var pdfPreview = document.getElementById('pdfPreview');
						var removeBtn = document.getElementById('removeBtn');
						
						preview.src = '';
						preview.style.display = 'none';
						pdfPreview.style.display = 'none';
						removeBtn.style.display = 'none';

						document.getElementById('imgUrl').value = '';
						document.getElementById('uploadBtn').innerHTML = '上传';
						document.getElementById('fileToUpload').value = '';
					}


					$(document).ready(function() {
						// 定义必填字段的 ID 数组
						const requiredFields = ['#a8','#a1', '#a2', '#a3'];
						const tixingText = $('#tixingtext');
						// 检查所有必填字段是否都有值
						function checkFields() {
							let allFilled = true;
							$.each(requiredFields, function(index, field) {
								
								if ($(field).val().trim() === '') {
									allFilled = false;
									return false; // 跳出 each 循环
								}
							});
							// 根据检查结果启用或禁用上传输入框
							$('#ssi-upload').prop('disabled',!allFilled);
							// 根据检查结果显示或隐藏提示信息
							if (allFilled) {
								tixingText.hide();
							} else {
								tixingText.show().text('当前上传按钮不可用,需先填写上面的必填项才可上传');
							}
						}

						// 为每个必填字段绑定输入事件，输入时触发检查
						$(requiredFields.join(',')).on('input', checkFields);

						// 页面加载时先检查一次
						checkFields();
					});




					function getFormData(){
						var ue = UE.getEditor('neirong');
						var params= {
							"xmid" : $.trim($('#xmid').val()),
							"a1" : $.trim($('#a1').val()),
							"a2" : $.trim($('#a2').val()),
							"a3" : $.trim($('#a3').val()),
							"a5" : $.trim($('#a5').val()),
							"a8" : $.trim($('#a8').val()),
							"ysp_id": $.trim($('#ysp_id').val()),
							"ysp_jicheng_id": $.trim($('#ysp_jicheng_id').val()),
							
							"neirong" : $.trim(ue.getContent())

							 
							
						}
						console.log(params)
						return params;
				  	}
					  function validateFields() {
						var fields = ['a1', 'a2', 'a3', 'a8'];
						for (var i = 0; i < fields.length; i++) {
							var field = $('#' + fields[i]);
							if ($.trim(field.val()) === '') {
								alert('请填写所有必填字段！');
								field.focus();
								return false;
							}
						}
						return true;
					}
		 
					$('#ssi-upload').ssi_uploader({
						//参数参考地址:http://ssbeefeater.github.io/#ssi-uploader/documentation
						url: '../../../shoufapiao/shoufapiao_add_ok.asp', //后台url
						setData:getFormData,
						preview: true, //是否支持预览
						maxNumberOfFiles:1,//支持上传文件的最大数目
						allowed: ["JPG","jpg","png","gif","bmp","txt","ps","doc","xls","ppt","docx","xlsx","pptx","pdf","et","wps","zip","rar","7z","tar","gz","jpeg"],//允许的文件类型
						maxFileSize:500, //允许上传的最大文件尺寸m
						locale:"zh_CN",//中文语言
						errorHandler: {
							method: function (msg,type) {
								alert(msg); //错误提示
							}
						},
						onEachUpload:function(fileInfo){
							console.log(fileInfo);
							
						},
						onUpload: function () {
								console.log('上传成功！');
								alert('上传成功！');
								//window.location.reload();
								window.location.href='../../../shoufapiao/yyb_ccb_shoufapiao.asp'
							}
						
					});
				 
				</script>
			
			</form> 
				 
				
						
				 

		 

			

 
	</div>
</div>
				<!-- Main container end -->

				
				<div id="notes"></div>			

			
		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="../../../../js/Tycoon/js/jquery.min.js"></script>
		<script src="../../../../js/Tycoon/js/bootstrap.bundle.min.js"></script>
		<script src="../../../../js/Tycoon/js/moment.js"></script>


		 
	
			<!-- *************
				************ Vendor Js Files *************
			************* -->
			<!-- Slimscroll JS -->
			<script src="../../../../js/Tycoon/vendor/slimscroll/slimscroll.min.js"></script>
			<script src="../../../../js/Tycoon/vendor/slimscroll/custom-scrollbar.js"></script>
	 
 	<!-- Datepickers -->
	 <script src="../../../../js/Tycoon/vendor/datepicker/js/picker.js"></script>
	 <script src="../../../../js/Tycoon/vendor/datepicker/js/picker.date.js"></script>
	 <script src="../../../../js/Tycoon/vendor/datepicker/js/custom-picker.js"></script>
			<!-- Main JS -->
			<script src="../../../../js/Tycoon/js/main.js"></script>
			 
		
		
		<script src="../../../../js/Tycoon/js/jquery.easing.1.3.js"></script>
		<script src="../../../../js/Tycoon/vendor/notify/notify.js"></script>
 
 
<script>
	function showAlert(message, duration) {  
		var notes = $('#notes').notify({
			removeIcon: '<i class="icon-close"></i>'
		});
		notes.show(message, {
			title: '设置',
		});
		// 设置弹窗在指定的时间后自动关闭  
		setTimeout(function() {  
			$('.icon-close').click();  
		}, duration);  
	};


 

</script>
 
		

	</body>
</html>
<%end if%>