<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--include file="../lib2/functions.asp"-->


<%
Dim jsonRequest, fso, logFile, logPath
data = Request.Form("data")
If data = "" Then
    data = Request.QueryString("data")
End If

' 将接收到的JSON数据保存到txt文件中
logPath = Server.MapPath("flow_data.txt")

Set fso = Server.CreateObject("Scripting.FileSystemObject")
Set logFile = fso.OpenTextFile(logPath, 8, True) ' 8表示追加写入,True表示如果文件不存在则创建

'data是根据$分割的
Dim arrData
arrData = Split(data, "$")

If UBound(arrData) < 8 Then
    Response.Write "{""code"":-1, ""msg"":""数据字段不完整""}"
    Response.End
End If

' 写入时间戳和JSON数据
logFile.WriteLine Now() & " - " & data
'logFile.Close

'遍历arrData数组,处理每个元素
Dim i, itemParts
For i = 0 To UBound(arrData)
    '每个item按/分割,只取分割后的第1个元素(索引为1),忽略第0个元素
    If InStr(arrData(i), "/") > 0 Then
        itemParts = Split(arrData(i), "/")
        If UBound(itemParts) >= 1 Then
            arrData(i) = itemParts(1) '只保留第1个元素
        End If
    End If
Next

//定义一个映射关系，名称->索引值，比如：公司主体->0
'定义字段名称与索引的映射关系
Dim FIELD_INDEX
Set FIELD_INDEX = CreateObject("Scripting.Dictionary")

'' 以下字段为自定义字段，索引值根据实际情况调整
FIELD_INDEX.Add "归属公司", 5
FIELD_INDEX.Add "申请部门", 6
FIELD_INDEX.Add "一级科目", 7
FIELD_INDEX.Add "项目", 8
FIELD_INDEX.Add "事由", 9
FIELD_INDEX.Add "金额", 10
FIELD_INDEX.Add "有无发票", 11
FIELD_INDEX.Add "支付账本", 12
FIELD_INDEX.Add "收款对象", 13

'使用映射关系获取数据
flow_id = arrData(0)
title = arrData(1)
create_time = arrData(2)
create_userid = arrData(3)
'李成
'create_userid = "*****************"
approval_result = arrData(4)

company_name = arrData(FIELD_INDEX("归属公司"))
dept_name = arrData(FIELD_INDEX("申请部门"))
subject = arrData(FIELD_INDEX("一级科目"))
project_name = arrData(FIELD_INDEX("项目"))
pay_comment = arrData(FIELD_INDEX("事由"))
amount = arrData(FIELD_INDEX("金额"))
is_invoice = arrData(FIELD_INDEX("有无发票"))
pay_account = arrData(FIELD_INDEX("支付账本"))
get_account = arrData(FIELD_INDEX("收款对象"))

'将有无发票字段转换为布尔值
Dim is_invoice_bool
If is_invoice = "是" Then
    is_invoice_bool = "True"
Else
    is_invoice_bool = "False"
End If

'response.write is_invoice
'response.end

'科目
subjectData = Split(subject, "|")
Dim first_subject, second_subject
first_subject = subject
second_subject = ""

'如果科目包含一级和二级，则分别提取
If UBound(subjectData) >= 1 Then
    first_subject = subjectData(0)
    second_subject = subjectData(1)
End If

'取消调试输出和终止
'response.write get_account
'response.end

'先查询审批编号是否已存在
Dim rsCheck, sql
sql = "SELECT COUNT(*) FROM caiwu_baoxiao_dingding_feiyong WHERE [钉钉审批编号]='" & flow_id & "'"
Set rsCheck = conn.Execute(sql)
If rsCheck(0) > 0 Then
    Response.Write "{""code"":1,""msg"":""审批记录已存在""}"
    Response.End
End If
Set rsCheck = Nothing

'使用ADO Recordset对象插入数据
Dim rs2
Set rs2 = Server.CreateObject("ADODB.Recordset")
rs2.Open "caiwu_baoxiao_dingding_feiyong", conn, 1, 3 '1=adOpenKeyset, 3=adLockOptimistic

'根据钉钉ID获取用户名称
sql3="select mingzhi from user2011 where dingding_id='"&create_userid&"'"
rs.open sql3,conn,1,2
'response.write sql3
'response.end
if not rs.eof then
    mingcheng = rs(0)
else
    mingcheng = ""
end if
rs.close

'添加新记录
rs2.AddNew

'设置字段值
rs2("钉钉审批编号") = flow_id
rs2("标题") = title
rs2("申请日期") = create_time
rs2("申请人") = mingcheng
rs2("当前状态") = approval_result

rs2("申请部门") = dept_name
rs2("归属公司") = company_name
rs2("金额") = amount
rs2("事由") = pay_comment
rs2("一级科目") = first_subject
rs2("二级科目") = second_subject
rs2("支付账本") = pay_account
rs2("有无发票") = is_invoice_bool
rs2("收款对象") = get_account

rs2("添加时间") = Now()

'更新记录
rs2.Update

'记录日志
'LogMessage "INSERT INTO [caiwu_baoxiao_dingding_feiyong] 使用Recordset对象添加记录成功"
logFile.WriteLine "INSERT INTO [caiwu_baoxiao_dingding_feiyong] 使用Recordset对象添加记录成功"
logFile.Close

'关闭记录集
rs2.Close
Set rs2 = Nothing

Set logFile = Nothing
Set fso = Nothing

' 返回成功响应
Response.Write "{""code"":1}"
%>