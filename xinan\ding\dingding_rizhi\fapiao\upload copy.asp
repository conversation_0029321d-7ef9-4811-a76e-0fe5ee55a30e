<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="uploadutil.asp"-->
<%
' 确保上传目录存在
Dim objFSO
Set objFSO = Server.CreateObject("Scripting.FileSystemObject")
Dim uploadDir
' 修改为实际使用的上传目录
uploadDir = Server.MapPath("upfiles_caiwubu")
If Not objFSO.FolderExists(uploadDir) Then
    On Error Resume Next
    objFSO.CreateFolder(uploadDir)
    If Err.Number <> 0 Then
        Response.Write "创建目录失败: " & Err.Description
        Response.End
    End If
    On Error Goto 0
End If
Set objFSO = Nothing

' 检查是否是表单提交
If Request.ServerVariables("REQUEST_METHOD") = "POST" Then
    ' 设置响应为JSON格式
    Response.Charset="UTF-8"
    Response.ContentType="application/json"
    
    Dim f_Name,f_SaveName,f_Path,f_Size,f_Ext,f_Err,f_Save,f_Time
    dim FileUpload, FormName
    FormName = "fileToUpload"'文件域名称
    set FileUpload = New UpLoadClass
    FileUpload.Charset="UTF-8"
    FileUpload.SavePath="upfiles_caiwubu/"
    ' 设置最大文件大小为 5MB
    FileUpload.MaxSize = 5 * 1024 * 1024
    FileUpload.FileType="jpg/png/gif/txt/bmp/ps/doc/xls/ppt/docx/xlsx/pptx/pdf/et/wps/zip/rar/7z/tar/gz/jpeg"
    
    ' 添加错误处理
    On Error Resume Next
    FileUpload.Open() '开始执行上传程序
    If Err.Number <> 0 Then
        Response.Write "{""error"":""上传初始化失败: " & Err.Description & """}"
        Response.End
    End If
    On Error Goto 0

    f_Err = FileUpload.Form(FormName & "_Err") '获取上传状态
    IF f_Err = 0 Then '上传成功
        f_Name = FileUpload.Form(FormName & "_Name")'原文件名
        f_SaveName = FileUpload.Form(FormName)'保存文件名
        f_Path = FileUpload.SavePath'保存路径
        f_Size = FileUpload.Form(FormName & "_Size")'文件大小
        f_Ext = FileUpload.Form(FormName & "_Ext")'文件类型
        f_Time = Now()'保存时间

        ' response.write f_Path & f_SaveName
        ' response.end

        ' 记录上传文件信息
        WriteLog "文件上传成功：" & f_Name & ", 保存为：" & f_SaveName & ", 类型：" & f_Ext & ", 大小：" & f_Size

        ' 步骤1：调用附件接口上传文件
        Dim recognitionResult
        recognitionResult = CallRecognitionAPI(Server.MapPath(f_Path & f_SaveName))
        WriteLog "附件上传接口返回：" & recognitionResult

        ' 步骤2：调用工作流接口
        ' Dim workflowResult
        ' workflowResult = CallWorkflowAPI(recognitionResult)
        ' WriteLog "工作流接口返回：" & workflowResult

        ' 步骤3：解析工作流响应结果
        ' Dim finalResult
        ' finalResult = ExtractJsonFromWorkflow(workflowResult)
        ' WriteLog "最终解析结果：" & finalResult

        ' 返回处理结果
        ' Response.Write "{""success"":true,""filename"":""" & f_SaveName & """,""originalname"":""" & f_Name & """,""result"":" & finalResult & "}"
    Else
        ' 输出更详细的错误信息
        Dim errMsg
        Select Case f_Err
            Case 1
                errMsg = "文件大小超出限制（最大5MB）"
            Case 2
                errMsg = "文件类型不允许"
            Case 3
                errMsg = "文件大小超出限制且类型不允许"
            Case Else
                errMsg = "未知错误"
        End Select
        Response.Write "{""success"":false,""error"":""" & errMsg & """}"
    End If

    Set FileUpload = Nothing
    Response.End ' 确保在返回JSON后结束响应
Else
    ' 非POST请求，显示上传表单
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文件上传</title>
    <style>
        .upload-form {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .message {
            color: #333;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 4px solid #5cb85c;
        }
        .error {
            border-left: 4px solid #d9534f;
        }
        .file-input {
            margin: 15px 0;
        }
        .submit-btn {
            background-color: #5cb85c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .submit-btn:hover {
            background-color: #4cae4c;
        }
    </style>
</head>
<body>
    <div class="upload-form">
        <h2>文件上传</h2>
        
        <form method="post" enctype="multipart/form-data">
            <div class="file-input">
                <label for="fileToUpload">选择文件：</label>
                <input type="file" name="fileToUpload" id="fileToUpload" required>
            </div>
            <div>
                <input type="submit" value="上传文件" class="submit-btn">
            </div>
        </form>
        
        <div style="margin-top: 15px; font-size: 14px; color: #666;">
            <p>支持的文件类型：JPG、JPEG、PNG、PDF</p>
            <p>最大文件大小：5MB</p>
        </div>
    </div>
</body>
</html>
<%
End If
%>
<%
' 修改附件上传接口调用函数
Function CallRecognitionAPI(filePath)
    WriteLog "开始调用识别接口，文件路径：" & filePath
    
    On Error Resume Next
    
    ' 读取文件内容
    Dim objStream
    Set objStream = Server.CreateObject("ADODB.Stream")
    objStream.Type = 1 ' 二进制
    objStream.Open
    
    WriteLog "正在读取文件..."
    objStream.LoadFromFile filePath
    
    If Err.Number <> 0 Then
        WriteLog "读取文件失败：" & Err.Description
        CallRecognitionAPI = "{""error"":""读取文件失败: " & Err.Description & """}"
        Exit Function
    End If
    
    ' 获取文件内容
    objStream.Position = 0
    Dim fileContents : fileContents = objStream.Read
    objStream.Close
    Set objStream = Nothing
    
    ' 准备HTTP请求
    Dim objXMLHTTP
    Set objXMLHTTP = Server.CreateObject("MSXML2.ServerXMLHTTP.6.0")
    
    ' 生成唯一的boundary
    Dim boundary : boundary = "----" & Replace(CreateObject("Scripting.Dictionary").HashVal, "-", "")
    
    ' 获取文件名
    Dim fileName : fileName = Mid(filePath, InStrRev(filePath, "\") + 1)
    
    ' 记录请求URL和头部信息
    WriteLog "请求URL: http://**************/v1/files/upload"
    WriteLog "请求头 Authorization: Bearer app-e76frkl7f0BsNJDLRRLKXGTI"
    WriteLog "请求头 Content-Type: multipart/form-data; boundary=" & boundary
    WriteLog "上传文件名: " & fileName
    
    ' 构建multipart/form-data请求体
    Dim strBoundary : strBoundary = "--" & boundary
    Dim strContentDisposition : strContentDisposition = "Content-Disposition: form-data; name=""file""; filename=""" & fileName & """"
    Dim strContentType : strContentType = "Content-Type: application/octet-stream"
    Dim strCrLf : strCrLf = Chr(13) & Chr(10)
    
    ' 记录请求体结构
    WriteLog "请求体结构:"
    WriteLog strBoundary
    WriteLog strContentDisposition
    WriteLog strContentType
    WriteLog "文件大小: " & LenB(fileContents) & " 字节"
    
    ' 设置请求头
    objXMLHTTP.Open "POST", "http://**************/v1/files/upload", False
    objXMLHTTP.setRequestHeader "Authorization", "Bearer app-e76frkl7f0BsNJDLRRLKXGTI"
    objXMLHTTP.setRequestHeader "Content-Type", "multipart/form-data; boundary=" & boundary
    
    ' 构建请求体
    Dim requestBody
    requestBody = strBoundary & strCrLf & _
                 strContentDisposition & strCrLf & _
                 strContentType & strCrLf & strCrLf
    
    ' 使用二进制安全的方式发送数据
    Dim binStream
    Set binStream = Server.CreateObject("ADODB.Stream")
    binStream.Type = 1 ' 二进制
    binStream.Open
    
    ' 写入表单头部
    binStream.Write StringToBinary(requestBody)
    ' 写入文件内容
    binStream.Write fileContents
    ' 写入结束边界
    binStream.Write StringToBinary(strCrLf & strBoundary & "--" & strCrLf)
    
    ' 获取完整的二进制数据
    binStream.Position = 0
    Dim postData : postData = binStream.Read
    binStream.Close
    Set binStream = Nothing
    
    ' 发送请求
    WriteLog "开始发送请求..."
    objXMLHTTP.send postData
    
    If Err.Number <> 0 Then
        WriteLog "发送请求失败：" & Err.Description
        CallRecognitionAPI = "{""error"":""调用识别接口失败: " & Err.Description & """}"
    Else
        WriteLog "请求状态码: " & objXMLHTTP.Status
        WriteLog "响应头: " & objXMLHTTP.getAllResponseHeaders()
        
        If objXMLHTTP.Status = 200 Then
            WriteLog "识别请求成功，状态码：200"
            WriteLog "响应内容：" & objXMLHTTP.responseText
            CallRecognitionAPI = objXMLHTTP.responseText
        Else
            WriteLog "识别请求失败，状态码：" & objXMLHTTP.Status & "，响应内容：" & objXMLHTTP.responseText
            CallRecognitionAPI = "{""error"":""识别接口返回错误: " & objXMLHTTP.Status & """}"
        End If
    End If
    
    On Error Goto 0
    Set objXMLHTTP = Nothing
End Function

' 辅助函数：将字符串转换为二进制数据
Function StringToBinary(str)
    Dim stream
    Set stream = Server.CreateObject("ADODB.Stream")
    stream.Type = 2 ' 文本
    stream.Charset = "utf-8"
    stream.Open
    stream.WriteText str
    
    stream.Position = 0
    stream.Type = 1 ' 切换到二进制
    StringToBinary = stream.Read
    
    stream.Close
    Set stream = Nothing
End Function

' 修改工作流接口调用函数
Function CallWorkflowAPI(recognitionResponse)
    WriteLog "开始调用工作流接口"
    WriteLog "识别接口返回结果：" & recognitionResponse
    
    ' 检查是否存在错误
    If InStr(recognitionResponse, """error""") > 0 Then
        WriteLog "识别接口返回错误，跳过工作流调用"
        CallWorkflowAPI = recognitionResponse
        Exit Function
    End If
    
    On Error Resume Next
    
    ' 解析文件ID
    Dim uploadFileId
    uploadFileId = Mid(recognitionResponse, InStr(recognitionResponse, """id"":""") + 6)
    uploadFileId = Left(uploadFileId, InStr(uploadFileId, """") - 1)
    
    If Err.Number <> 0 Then
        WriteLog "解析文件ID失败：" & Err.Description
        CallWorkflowAPI = "{""error"":""解析文件ID失败""}"
        Exit Function
    End If
    
    WriteLog "获取到文件ID：" & uploadFileId
    
    ' 调用工作流API
    Dim objXMLHTTP, jsonData
    Set objXMLHTTP = Server.CreateObject("MSXML2.ServerXMLHTTP.6.0")
    
    objXMLHTTP.Open "POST", "http://**************/v1/workflows/run", False
    objXMLHTTP.setRequestHeader "Authorization", "Bearer app-e76frkl7f0BsNJDLRRLKXGTI"
    objXMLHTTP.setRequestHeader "Content-Type", "application/json"
    
    jsonData = "{""inputs"":{""file"":{""transfer_method"":""local_file"",""upload_file_id"":""" & uploadFileId & """,""type"":""document""}},""response_mode"":""blocking"",""user"":""abc-123""}"
    
    WriteLog "发送工作流请求：" & jsonData
    objXMLHTTP.send jsonData
    
    If Err.Number <> 0 Then
        WriteLog "工作流请求失败：" & Err.Description
        CallWorkflowAPI = "{""error"":""调用工作流接口失败: " & Err.Description & """}"
    Else
        If objXMLHTTP.Status = 200 Then
            WriteLog "工作流请求成功"
            CallWorkflowAPI = objXMLHTTP.responseText
        Else
            WriteLog "工作流请求失败，状态码：" & objXMLHTTP.Status
            CallWorkflowAPI = "{""error"":""工作流接口返回错误: " & objXMLHTTP.Status & """}"
        End If
    End If
    
    On Error Goto 0
    Set objXMLHTTP = Nothing
End Function

' 添加日志写入函数
Function WriteLog(logMessage)
    Dim fs, logFile, logPath
    logPath = Server.MapPath("upload_log.txt")
    
    Set fs = Server.CreateObject("Scripting.FileSystemObject")
    Set logFile = fs.OpenTextFile(logPath, 8, True) ' 8 表示追加写入
    
    logFile.WriteLine Now() & " - " & logMessage
    logFile.Close
    
    Set logFile = Nothing
    Set fs = Nothing
End Function

Function ExtractJsonFromWorkflow(workflowResponse)
    WriteLog "开始解析工作流响应：" & workflowResponse
    
    ' 检查workflowResponse是否包含error
    If InStr(workflowResponse, """error""") > 0 Then
        WriteLog "工作流返回错误，跳过数据提取"
        ExtractJsonFromWorkflow = workflowResponse
        Exit Function
    End If
    
    On Error Resume Next
    ' 查找 ```json 和 ``` 之间的内容
    Dim jsonStart, jsonEnd, jsonText
    jsonStart = InStr(workflowResponse, """text"":""```json" & vbLf)
    
    If jsonStart > 0 Then
        jsonStart = jsonStart + 13 ' 跳过 "text":"```json
        jsonEnd = InStr(jsonStart, workflowResponse, "```""")
        
        If jsonEnd > 0 Then
            jsonText = Mid(workflowResponse, jsonStart, jsonEnd - jsonStart)
            ' 清理可能的多余换行符
            jsonText = Replace(jsonText, vbCrLf, "")
            jsonText = Replace(jsonText, vbLf, "")
            WriteLog "成功提取JSON数据：" & jsonText
            ExtractJsonFromWorkflow = jsonText
        Else
            WriteLog "未找到JSON结束标记"
            ExtractJsonFromWorkflow = "{""error"":""无法找到JSON结束标记""}"
        End If
    Else
        WriteLog "未找到JSON开始标记"
        ExtractJsonFromWorkflow = "{""error"":""无法找到JSON内容""}"
    End If
    
    If Err.Number <> 0 Then
        WriteLog "解析JSON时发生错误：" & Err.Description
        ExtractJsonFromWorkflow = "{""error"":""解析JSON失败: " & Err.Description & """}"
    End If
    On Error Goto 0
End Function
%>