﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>每年检查批次统计表</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">
		<!-- Chat css -->
		<link rel="stylesheet" href="css/chat.css">

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
	</head>

	<body>

		<!-- Loading starts -->
		<div id="loading-wrapper">
			<div class="spinner-border" role="status">
				<span class="sr-only">Loading...</span>
			</div>
		</div>
		<!-- Loading ends -->
		
	
			
			<!-- Page content start  -->
			<div class="page-content">				
				
				
				<!-- Main container start -->
				<div class="main-container">

					<!-- Page header start -->
					<div class="page-header">
						
						<!-- Breadcrumb start -->
						<ol class="breadcrumb">
							<li class="breadcrumb-item">欢迎 用户甲!</li>
						</ol>
						<!-- Breadcrumb end -->

						<div class="app-actions">
							<button type="button" class="btn active">今年</button>
							<button type="button" class="btn">去年</button>
							<button type="button" class="btn">前年</button>
							
						</div>

					</div>
					<!-- Page header end -->

					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-eye1"></i>
								</div>
								<div class="sale-num">
									<h3>5次</h3>
									<p>今年累计检查批次</p>
								</div>
							</div>
						</div>
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon danger">
									<i class="icon-people_outline"></i>
								</div>
								<div class="sale-num">
									<h3>300家</h3>
									<p>我市入编检查单位数量</p>
								</div>
							</div>
						</div>
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon warning">
									<i class="icon-people"></i>
								</div>
								<div class="sale-num">
									<h3>200家</h3>
									<p>今年已检查我市单位数量</p>
								</div>
							</div>
						</div>
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon success">
									<i class="icon-activity"></i>
								</div>
								<div class="sale-num">
									<h3>99分</h3>
									<p>今年检查评价得分</p>
								</div>
							</div>
						</div>
					</div>
					<!-- Row end -->

					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">最近一次检查结果:批次1号</div>
								</div>
								<div class="card-body">

									<!-- Row start -->
									<div class="row gutters align-items-center">
										<div class="col-xl-2 col-lg-3 col-md-12 col-sm-12 col-12">
											<div class="monthly-avg">
												<h5>检查单位</h5>
												<div class="avg-block">
													<h3 class="avg-total text-danger">25家</h3>
													<h6 class="avg-label">已完成检查单位数量</h6>
												</div>
												<div class="avg-block">
													<h3 class="avg-total text-info">50家</h3>
													<h6 class="avg-label">计划检查单位数量</h6>
												</div>
											</div>
										</div>
										<div class="col-xl-8 col-lg-6 col-md-12 col-sm-12 col-12">
											<div id="lineRevenueGraph"></div>
										</div>
										<div class="col-xl-2 col-lg-3 col-md-12 col-sm-12 col-12">
											<div class="monthly-avg">
												<h5>检查单位得分</h5>
												<div class="avg-block">
													<h3 class="avg-total text-danger">100分</h3>
													<h6 class="avg-label">最高分:医院甲</h6>
												</div>
												<div class="avg-block">
													<h3 class="avg-total text-info">58分</h3>
													<h6 class="avg-label">最低分:医院乙</h6>
												</div>
											</div>
										</div>
									</div>
									<!-- Row end -->

								</div>
							</div>
						</div>
					</div>
					<!-- Row end -->

					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-8 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">今年检查批次时间轴</div>
								</div>
								<div class="card-body">
									<ul class="team-activity">
										<li class="product-list clearfix">
											<div class="product-time">
												<p class="date center-text">2022年5月10日</p>
												<span class="badge badge-danger">批次1号</span>
											</div>
											<div class="product-info">
												<div class="activity">
													<h6>检查批次名称:1-大数据安全检查</h6>
													<p>检查状态:进行中</p>
												</div>
												<div class="status">
													<div class="progress">
														<div class="progress-bar bg-danger" role="progressbar" aria-valuenow="49" aria-valuemin="0" aria-valuemax="100" style="width: 49%">
															<span class="sr-only">49% Complete (success)</span>
														</div>
													</div>
													<p>计划检查50家,已完成40家</p>
												</div>
											</div>
										</li>
										<li class="product-list clearfix">
											<div class="product-time">
												<p class="date center-text">2022年4月1日</p>
												<span class="badge badge-info">批次2号</span>
											</div>
											<div class="product-info">
												<div class="activity">
													<h6>检查批次名称:1-大数据安全检查</h6>
													<p>检查状态:已完成</p>	
												</div>
												<div class="status">
													<div class="progress">
														<div class="progress-bar bg-info" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
															<span class="sr-only">100% Complete (success)</span>
														</div>
													</div>
													<p>计划检查50家,已完成50家</p>
												</div>
											</div>
										</li>
										<li class="product-list clearfix">
											<div class="product-time">
												<p class="date center-text">2022年3月1日</p>
												<span class="badge badge-info">批次3号</span>
											</div>
											<div class="product-info">
												<div class="activity">
													<h6>检查批次名称:3-大数据安全检查</h6>
													<p>检查状态:已完成</p>	
												</div>
												<div class="status">
													<div class="progress">
														<div class="progress-bar bg-info" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
															<span class="sr-only">100% Complete (success)</span>
														</div>
													</div>
													<p>计划检查50家,已完成50家</p>
												</div>
											</div>
										</li>
										<li class="product-list clearfix">
											<div class="product-time">
												<p class="date center-text">2022年1月1日</p>
												<span class="badge badge-info">批次4号</span>
											</div>
											<div class="product-info">
												<div class="activity">
													<h6>检查批次名称:4-大数据安全检查</h6>
													<p>检查状态:已完成</p>	
												</div>
												<div class="status">
													<div class="progress">
														<div class="progress-bar bg-info" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
															<span class="sr-only">100% Complete (success)</span>
														</div>
													</div>
													<p>计划检查50家,已完成50家</p>
												</div>
											</div>
										</li>
										
									</ul>
								</div>
							</div>
						</div>
						<div class="col-xl-4 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="card">
								<div class="card-header">
									<div class="card-title">我市检查单位数量累计</div>
								</div>
								<div class="card-body pt-0">
									<div id="radialTasks"></div>
									<ul class="task-list-container">
										<li class="task-list-item">
											<div class="task-icon bg-info">
												<i class="icon-people"></i>
											</div>
											<div class="task-info">
												<h6 class="task-title">今年新增</h6>
												<p class="amount-spend text-info">12家</p>
											</div>
										</li>
										<li class="task-list-item">
											<div class="task-icon bg-success">
												<i class="icon-people"></i>
											</div>
											<div class="task-info">
												<h6 class="task-title">累计数量</h6>
												<p class="amount-spend text-success">300家</p>
											</div>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
					<!-- Row end -->

					
					
					
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6 col-12">
							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">最近一次检查得分最高top5排名</div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<div class="products-sold-container">
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">1</div>
													<div class="product-title">
														<div class="title">用户1</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">2</div>
													<div class="product-title">
														<div class="title">单位2</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">3</div>
													<div class="product-title">
														<div class="title">单位3</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">99分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">4</div>
													<div class="product-title">
														<div class="title">单位4</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">95分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-danger sm">5</div>
													<div class="product-title">
														<div class="title">单位5</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">93分</div>
												</div>
											</div>
											
											
										
											
										</div>
									</div>
								</div>
							</div>
							<!-- Card end -->
						</div>
						<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6 col-12">
							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">最近一次检查得分最差top5排名</div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<div class="products-sold-container">
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-warning sm">1</div>
													<div class="product-title">
														<div class="title">用户12</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">0分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-warning sm">2</div>
													<div class="product-title">
														<div class="title">单位23</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">30分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-warning sm">3</div>
													<div class="product-title">
														<div class="title">单位33</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">40分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-warning sm">4</div>
													<div class="product-title">
														<div class="title">单位64</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">50分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-warning sm">5</div>
													<div class="product-title">
														<div class="title">单位15</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">59分</div>
												</div>
											</div>
											
											
										
											
										</div>
									</div>
								</div>
							</div>
							<!-- Card end -->
						</div>
						<div class="col-xl-4 col-lg-6 col-md-6 col-sm-6 col-12">
							<!-- Card start -->
							<div class="card">
								<div class="card-header">
									<div class="card-title">今年检查平均得分最高top5排名</div>
								</div>
								<div class="card-body">
									<div class="customScroll5">
										<div class="products-sold-container">
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-success sm">1</div>
													<div class="product-title">
														<div class="title">用户12</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-success sm">2</div>
													<div class="product-title">
														<div class="title">单位23</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-success sm">3</div>
													<div class="product-title">
														<div class="title">单位33</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">100分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-success sm">4</div>
													<div class="product-title">
														<div class="title">单位64</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">99分</div>
												</div>
											</div>
											<div class="product">
												<div class="product-details">
													<div class="text-avatar circle bg-success sm">5</div>
													<div class="product-title">
														<div class="title">单位15</div>
													</div>
												</div>
												<div class="product-sold">
													<div class="sold text-info">99分</div>
												</div>
											</div>
											
											
										
											
										</div>
									</div>
								</div>
							</div>
							<!-- Card end -->
						</div>
					
						
					</div>
					<!-- Row end -->

				</div>
				<!-- Main container end -->

			
				

				
			</div>
			<!-- Page content end -->

	
			

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>


		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>

		<!-- Polyfill JS -->
		<script src="vendor/polyfill/polyfill.min.js"></script>
		<script src="vendor/polyfill/class-list.min.js"></script>

		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		
		<!-- Peity Charts -->
		<script src="vendor/peity/peity.min.js"></script>
		<script src="vendor/peity/custom-peity.js"></script>
		
		<!-- Circleful Charts -->
		<script src="vendor/circliful/circliful.min.js"></script>
		<script src="vendor/circliful/circliful.custom.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>