<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../../shujuku.asp"-->
<% 
if  session("name")="" then
%>
对不起你还没有登陆，请<a href="../../../index.asp">登陆</a>
<%
else 


'=[通知]准入控制判断=============================================================================

qread=""
qadd=""
qedit=""
qdel=""
sql="select * from 准入控制管理系统 where  用户='"&session("mingzhi")&"' and 系统名称='工作汇报系统'"
rs2.open sql,conn2,1,2
if not rs2.eof then 
qread=RS2("可读取权限")
qadd=RS2("可添加权限")
qedit=RS2("可修改权限")
qdel=RS2("可删除权限")
end if
RS2.close

%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script> 
<script src="../js/json2.js" type="text/javascript"></script>
<title>我的钉钉日志列表</title>
<!-- 下面都是bootstrap需要引入的 ===========================================================-->
<!-- ==================================================================================================================== -->
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,Chrome=1" />
<meta http-equiv="X-UA-Compatible" content="IE=9" />

<!-- 引入 Bootstrap -->
<link href="../../../js/bootstrap/css/bootstrap.min.css" rel="stylesheet">
<link href="../../../js/bootstrap/css/scojs.css" rel="stylesheet">
 <!-- 包括所有已编译的插件 -->
<script src="../../../js/bootstrap/js/bootstrap.min.js"></script>
<script src="../../../js/bootstrap/js/sco.tooltip.js"></script>
 <!-- select.插件 -->

  <script type="text/javascript" src="../../../js/bootstrap/css/bootstrap-select.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../js/bootstrap/css/bootstrap-select.css">


<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
  <script src="../../../js/bootstrap/html5shiv.min.js"></script>
  <script src="../../../js/bootstrap/respond.min.js"></script>
<![endif]-->

<!-- bootstrap 的日期插件 -->
<link rel="stylesheet" type="text/css" href="../../../js/bootstrap/css/bootstrap-datetimepicker.min.css"  rel="stylesheet" media="screen">

<script type="text/javascript" src="../../../js/bootstrap/js/bootstrap-datetimepicker.js"charset="UTF-8" ></script>
<script type="text/javascript" src="../../../js/bootstrap/js/bootstrap-datetimepicker.zh-CN.js" charset="UTF-8"></script>
<link href="../../../js/bootstrap/bootstrapTable/bootstrap-table.css" rel="stylesheet">
<script src="../../../js/bootstrap/bootstrapTable/bootstrap-table.js"></script>
<!--下拉列表插件bootstrap-select-->
<script src="../../../js/bootstrap/bootstrap-select/bootstrap-select.min.js"></script>
<link rel="stylesheet" type="text/css" href="../../../js/bootstrap/bootstrap-select/bootstrap-select.min.css" />
</head>

<body >
	<div class="row bg-primary"  style="padding-top: 10px;">
		<div class="col-md-2">
			 <h4><strong>钉钉日志导入OA</strong></h4>
			
		</div>
		<div class="col-md-4">
			<label >起止日期<font color="#DF3C2F">(间隔不能超180天)</font></label>
			    
			    <input type="text" class="form-control datepicker"  id="startTime" value="<%=dateadd("m",-1,year(date)&"-"&month(date)&"-1")%>" style="width:120px;display:inline-block;">~
				<input type="text" class="form-control datepicker"  id="endTime" value="<%=date()%>"style="width:120px;display:inline-block;" >
			  
		</div>
		<script type="text/javascript">
			$('.datepicker').datetimepicker({
				minView: "month",
				format: 'yyyy-mm-dd',
				autoclose: true
			});
		</script>
		
		<div class="col-md-2">
			<div class="form-group">
			    <label >日志类型</label>
			   <select id="templateName" class="form-control" style="width:150px;display:inline-block;">
			   	<option value="日报">日报</option>
			   	<option value="周报">周报</option>
				<option value="月报">月报</option>
				<option value="拜访记录">拜访记录</option>
				<option value="项目负责人日报">项目负责人日报</option>
				<option value="人事负责人日报">人事负责人日报</option>
			   </select>
			  </div>
		</div>
		<div class="col-md-2">
			<label>录入人</label>
		
			<select  id="userName" class="form-control" style="width:100px;display:inline-block;">
				<option selected="selected" value="<%=session("mingzhi")%>"><%=session("mingzhi")%></option>
			
				<option   value="">全部人员</option>
			<%if qread=true then%>  
				<%
				sql="select mingzhi from user2011 where 是否锁定=0 and name<>'fanyahui' order by mingzhi"
				rs2.open sql,conn2,1,2
				while not rs2.eof 
				
				%>
				<option value="<%=rs2(0)%>"><%=rs2(0)%></option>
				<%
				rs2.movenext
				wend
				rs2.close
				%>
			<%end if%>
			</select>
			
			
			
		</div>
		<div class="col-md-2">
			<input type="button" class="btn btn-info" id="query" value="查询"/>
			
		</div>
	</div>


	<div style="padding:10px,0,50px,0">
		<table class="table table-bordered table-hover">
			<thead>
				<tr>
                	<th width="5%" height="40px">选择</th>
					<!--<th width="5%">日志ID</th>-->
					<th width="5%">录入人</th>
                    <th width="10%">录入日期</th>
					<!--<th width="5%">实施日期</th>-->
					<th width="70%">实施情况</th>
					<th width="5%">日志类型</th>
					<!--<th width="10%">操作</th>-->
				</tr>
			</thead>
			<tbody id="datalist">
			
			</tbody>
			<tr id="tmp" style="display:none">
            	<td width="5%" height="40px" align="center">
					<input type="checkbox" id="report_id" name="report_id" value="" />
					<input type="hidden" id="creator_id" value="" />
					<input type="hidden" id="creator_name" value="" />
					<input type="hidden" id="create_time" value="" />
					<input type="hidden" id="bussiness_time" value="" />
					<input type="hidden" id="contents" value="" />
					<input type="hidden" id="template_name" value="" />
				</td>
				<td   align="center"></td>
				<!--<td   align="center"></td>-->
               <!-- <td   align="center"></td>-->
				<td  align="center"></td>
				<td  align="left"></td>
				<td   align="center"></td>
				<!--<td   align="center"></td>-->
			</tr>
		</table>
	</div>
	<nav class="navbar navbar-default navbar-fixed-bottom">
	  <div class="container">
		<div class="col-md-2" align="left">
			 <div class="checkbox">
			    <label>
			      <input type="checkbox" id="all" > <strong>全选</strong>
			    </label>
			  </div> 
		</div>
		<div class="col-md-2" align="left">
			  <div class="checkbox disabled">
			    <label>
			      <input type="checkbox" value="" checked="checked" disabled>
			      <strong>1、导入到汇报系统</strong>
			    </label>
			  </div>
		</div>
		<div class="col-md-6" id="xm"><label>2、导入到我的售前项目</label>
			<select name="shouqianXM_id" id="shouqianXM_id" class="selectpicker" data-live-search="true" title="请选择" style="width:200px;display:inline-block;" >
			<option selected="selected">请选择</option>
			<%
			sql="select id,项目名称 from 技术部测试管理表 where 项目名称<>'' and (项目销售负责人='"&session("mingzhi")&"' or 项目技术负责人='"&session("mingzhi")&"' or 其他配合人员 like '%"&session("mingzhi")&"%' ) order by id desc " 
			rs2.open sql,conn2,1,1
			while not rs2.eof
			%>
			<option value="<%=rs2("id")%>" ><%=rs2("项目名称")%></option>
			<%
			rs2.movenext
			wend
			rs2.close
			%>
			</select><br>
			<label>3、导入到我的售后项目</label>
				<select name="shouhouXM_id" id="shouhouXM_id" class="selectpicker" data-live-search="true" title="请选择" style="width:200px;display:inline-block;" >
				<option selected="selected">请选择</option>
				<%
				sql="select id,项目名称 from 技术部项目管理表 where 项目名称<>'' and (项目销售负责人='"&session("mingzhi")&"' or 项目技术负责人='"&session("mingzhi")&"' or 其他配合人员 like '%"&session("mingzhi")&"%' ) order by id desc" 
				rs2.open sql,conn2,1,1
				while not rs2.eof
				%>
				<option value="<%=rs2("id")%>" ><%=rs2("项目名称")%></option>
				<%
				rs2.movenext
				wend
				rs2.close
				%>
				</select>
		</div>
		<div class="col-md-2">
			<input type="button" id="bathIns_shouqian" value="导入OA系统"  class="btn btn-danger btn-lg"/>
			
		</div>
		
	    
	  </div>
	</nav>
	
	
	<script type="text/javascript">
		//由于钉钉日志查询的起始日期要求必须是时间的毫秒数格式,所以需要把日期转化为这格式 https://ding-doc.dingtalk.com/doc#/serverapi2/yknhmg
		function toNormalDate(str,format) {
			var oDate = new Date(str), oYear = oDate.getFullYear(),oMonth = oDate.getMonth() + 1,oDay =  oDate.getDate(),oHour = oDate.getHours(),oMin = oDate.getMinutes(),oSen = oDate.getSeconds();
			oTime = oYear + '-' + getzf(oMonth) + '-' + getzf(oDay)+ ' ' + getzf(oHour)+ ':' + getzf(oMin)+ ':' + getzf(oSen); //最后拼接时间
			if(!format){
				format="Y-m-d-h-i-s";
			}
			if(format==="Y-m-d"){
				dataTime= oTime.substr(0,10);
			}else if(format==="Y-m-d-h-i"){
				dataTime= oTime.substr(0,21);
			}else if(format==="Y-m-d-h-i-s"){
				dataTime= oTime;
			}
			return dataTime;
		}
		//getzf 函数为把1-9月份数字前面加个0字符
		function getzf(num) {
			if(parseInt(num) < 10) {
				num = '0' + num;
			}
			return num;
		}
		//getLogContent函数是获取日志的内容 由于日报的内容会有很多组件组成,所以要把日报内容全部以数组的形式读取出来
		function getLogContent(contents){
			var content = [];
			for(var i=0;i<contents.length;i++) {
				var conn = contents[i];
				//JavaScript push() 方法可向数组的末尾添加一个或多个元素，并返回新的长度。
				content.push(conn.key+":"+conn.value);
			}
			//JavaScript join() 方法用于把数组中的所有元素放入一个字符串。下面是代表用"；"隔开
			return content.join("；");
		}
		//导入到数据库
		function addLog(ids){
			var logs = [];
			ids = ids.split(",");
			for(var i=0;i<ids.length;i++){
				var logid = ids[i];
				var log = {};
				var tr = $("#report_id_"+logid);
				log.report_id=$("#report_id",tr).val();
				log.creator_id=$("#creator_id",tr).val();
				log.creator_name=$("#creator_name",tr).val();
				log.create_time=$("#create_time",tr).val();
				log.bussiness_time=$("#bussiness_time",tr).val();
				log.contents=$("#contents",tr).val();
				log.template_name=$("#template_name",tr).val();
				//log.shouqianXM_id=$("#shouqianXM_id",div).val();
				//log.shouhouXM_id=$("#shouhouXM_id",div).val();
				
				log.shouqianXM_id=$("#shouqianXM_id option:selected").val();
				log.shouhouXM_id=$("#shouhouXM_id option:selected").val();
				logs.push(log);
			}
			$.ajax({
				 type: "POST",
				 url: "savelog.asp",
				 data: {logs:JSON.stringify(logs),},
				 dataType: "json",
				 success: function(data){
					 if(data.errcode==0){
						alert(data.errmsg || "导入日志成功");
					}else{
						alert(data.errmsg || 'error')
					}
					
				 },
				 error:function(e){
					console.log(e);
				 }
			});
		}
		$(function(){
			//批量导入日志
			$("#bathIns_shouqian").click(function(){
				var idArr = new Array;
        		$("#datalist :checkbox[id='report_id']").each(function(i){
					if($(this).prop("checked")){
						idArr.push($(this).val());	
					}
        		});
       			var ids = idArr.join(',');
				if(ids==""){
					alert("请选择记录");	
					return;
				}
				addLog(ids);
			});
			
			//查询按钮
			$("#query").click(function(){
				$.ajax({
					 type: "POST",
					 url: "queryloglist.asp",  //把下面的4个参数内容传递到这个页面里进行查询
					 data: {
						 startTime:$.trim($("#startTime").val()), 
					 	 endTime:$.trim($("#endTime").val()),
						 templateName:$.trim($("#templateName").val()),
						 userName:$.trim($("#userName").val())
					 },
					 dataType: "json",
					 success: function(data){
						if(data.errcode==0){
							var list = data.list;//查询成功后找到日志列表datalist里面的内容,这些名字都是钉钉开发文档里规定好的名字https://ding-doc.dingtalk.com/doc#/serverapi2/yknhmg
							$("#datalist").html("");
							for(var i=0;i<list.length;i++){
								var item = list[i];
								var con = $("#tmp").clone();//jQuery clone() 方法，克隆所有的 <p> 元素
								con.attr("id","report_id_"+item.report_id);//jQuery attr() 方法 设置或返回被选元素的属性和值。
								var tds = con.find("td");
								$(tds[0]).find(":checkbox[id='report_id']").val(item.report_id);//调取日志id
								$(tds[0]).find(":hidden[id='creator_name']").val(item.creator_name);//调取日志创建人
								$(tds[0]).find(":hidden[id='create_time']").val(toNormalDate(item.create_time));//调取日志创建时间
								$(tds[0]).find(":hidden[id='bussiness_time']").val(toNormalDate(item.create_time));//调取日志创建时间
								$(tds[0]).find(":hidden[id='contents']").val(getLogContent(item.contents));//调取日志内日
								$(tds[0]).find(":hidden[id='template_name']").val(item.template_name);//调取日志模板名
								$(tds[0]).find(":hidden[id='creator_id']").val(item.creator_id);//调取日志创建人userid 即钉钉id
								
								//$(tds[0]).html(i);
								//$(tds[1]).html(item.report_id);
								$(tds[1]).html(item.creator_name);
								$(tds[2]).html(toNormalDate(item.create_time));
								//$(tds[4]).html(toNormalDate(item.create_time));
								$(tds[3]).html(getLogContent(item.contents));
								$(tds[4]).html(item.template_name);
								//$(tds[7]).html('<a href="javascript:;" onclick="addLog(\''+item.report_id+'\')">导入日志</a>');
								con.show();
								$("#datalist").append(con);
							}
							alert("查询成功");
						}else{
							alert(data.errmsg || 'error')
						}
						
					 },
					 error:function(e){
						console.log(e);
					 }
				});
			});
			
			//全选或全不选
			$("#all").click(function(){   
				if(this.checked){   
					$("#datalist :checkbox").prop("checked", true);  
				}else{   
					$("#datalist :checkbox").prop("checked", false);
				}   
			}); 
		});
		
	</script>
</body>
</html>
<%end if%>