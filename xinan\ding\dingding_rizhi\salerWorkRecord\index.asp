<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跟进记录统计</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            margin-top: 50px;
        }
        .table th {
            background-color: #33D8E6; /* 添加背景色 */
            font-weight: bold; /* 加粗 */
            color: #fff;
            text-align: center;
        }
        .table td {
            text-align: center;
        }
    </style>
</head>
<body>
<%
' 定义黑名单数组
Dim blacklist
blacklist = Array("16181925804593972", "manager7977") ' 你需要将需要忽略的用户 ID 添加到这个数组中

Set userMap = Server.CreateObject("Scripting.Dictionary")

' 查询用户表获取用户名称
sql2 = "SELECT * FROM user2011"
rs.open sql2,conn,1,2

' 创建用户 ID 到用户名的映射
Do While Not rs.EOF
    if not userMap.Exists(rs("dingding_id").value) then
        ' response.write rs("mingzhi").value & " - " & rs("是否锁定").value & "<br>"
        if rs("是否锁定").value = "True" then
            Dim originalLength
            originalLength = UBound(blacklist)
            ReDim Preserve blacklist(originalLength + 1)

            blacklist(originalLength + 1) = rs("dingding_id").value
        else
            userMap.Add rs("dingding_id").value, rs("mingzhi").value
        end if
    end if
    rs.MoveNext
Loop
rs.Close

'response.write rstJSON.JSONoutput()
'response.End

' 查询SQL sqlserver
sql = "SELECT creator_userid, " & _
      "SUM(CASE WHEN DATEPART(YEAR, gmt_create) = DATEPART(YEAR, DATEADD(WEEK, -1, GETDATE())) AND " & _
      "          DATEPART(WEEK, gmt_create) = DATEPART(WEEK, DATEADD(WEEK, -1, GETDATE())) THEN 1 ELSE 0 END) AS LastWeekCount, " & _
      "SUM(CASE WHEN MONTH(gmt_create) = MONTH(GETDATE()) THEN 1 ELSE 0 END) AS ThisMonthCount, " & _
      "SUM(CASE WHEN MONTH(gmt_create) = MONTH(DATEADD(MONTH, -1, GETDATE())) THEN 1 ELSE 0 END) AS LastMonthCount, " & _
      "SUM(CASE WHEN DATEPART(QUARTER, gmt_create) = DATEPART(QUARTER, GETDATE()) THEN 1 ELSE 0 END) AS ThisQuarterCount, " & _
      "SUM(CASE WHEN DATEPART(QUARTER, gmt_create) = DATEPART(QUARTER, DATEADD(MONTH, -3, GETDATE())) THEN 1 ELSE 0 END) AS LastQuarterCount, " & _
      "SUM(CASE WHEN YEAR(gmt_create) = YEAR(GETDATE()) THEN 1 ELSE 0 END) AS ThisYearCount, " & _
      "SUM(CASE WHEN YEAR(gmt_create) = YEAR(DATEADD(YEAR, -1, GETDATE())) THEN 1 ELSE 0 END) AS LastYearCount " & _
      "FROM saler_work_record " & _
      "GROUP BY creator_userid"


' 查询SQL access
'sql = "SELECT creator_userid, " & _
'      "SUM(IIF(Format(gmt_create, 'ww', 2) = Format(Date(), 'ww', 2), 1, 0)) AS LastWeekCount, " & _
'      "SUM(IIF(Month(gmt_create) = Month(Date()), 1, 0)) AS ThisMonthCount, " & _
'      "SUM(IIF(Month(gmt_create) = Month(Date())-1, 1, 0)) AS LastMonthCount, " & _
'      "SUM(IIF(Format(gmt_create, 'q', 2) = Format(Date(), 'q', 2), 1, 0)) AS ThisQuarterCount, " & _
'      "SUM(IIF(Format(gmt_create, 'q', 2) = Format(DateAdd('m', -3, Date()), 'q', 2), 1, 0)) AS LastQuarterCount, " & _
'      "SUM(IIF(Year(gmt_create) = Year(Date()), 1, 0)) AS ThisYearCount, " & _
'      "SUM(IIF(Year(gmt_create) = Year(Date())-1, 1, 0)) AS LastYearCount " & _
'      "FROM saler_work_record " & _
'      "GROUP BY creator_userid"

LogMessage "workRecord query local db, sql -> " & sql
rs.open sql,conn,1,2
%>
<div class="container">
    <h1 class="text-center mb-4">销售跟进记录统计</h1>

    <!-- <h3>不统计用户或离职用户清单</h3> -->
    <%
        For Each item In blacklist
            'Response.Write(item & "<br>")
        Next
    %>

<br>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>姓名</th>
                <th>上周</th>
                <th>本月</th>
                <th>上月</th>
                <th>本季度</th>
                <th>上季度</th>
                <th>今年</th>
                <th>去年</th>
            </tr>
        </thead>
        <tbody id="follow-up-data">
<%
Function IsInArray(valToBeFound, arr)
    Dim i
    For i = LBound(arr) To UBound(arr)
        If arr(i) = valToBeFound Then
            IsInArray = True
            Exit Function
        End If
    Next
    IsInArray = False
End Function


Dim username
Do While Not rs.EOF 
    If Not IsInArray(rs("creator_userid").Value, blacklist) Then
%>
            <tr>
                <td title="<%=rs("creator_userid")%>"><%=userMap(rs("creator_userid").value)%></td>
                <td><%=rs("LastWeekCount")%></td>
                <td><%=rs("ThisMonthCount")%></td>
                <td><%=rs("LastMonthCount")%></td>
                <td><%=rs("ThisQuarterCount")%></td>
                <td><%=rs("LastQuarterCount")%></td>
                <td><%=rs("ThisYearCount")%></td>
                <td><%=rs("LastYearCount")%></td>
            </tr>

<%
    end if

    rs.MoveNext
Loop
rs.close
Set rs = Nothing

conn.close
Set conn = Nothing
%>


        </tbody>
    </table>
    <div style="color:#f00">数据提取来源：钉钉/客户管理/跟进记录</div>
</div>

<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.1/js/bootstrap.js"></script>



</body>
</html>
