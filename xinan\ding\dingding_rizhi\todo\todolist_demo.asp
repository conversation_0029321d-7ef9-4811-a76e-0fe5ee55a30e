<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include file="../../../shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--#include file="../lib2/functions.asp"-->

<%
Response.Charset="utf-8"
Response.ContentType="application/json"
Set rstJSON = New aspJSON
With rstJSON.data
    .Add "errcode", 0
    .Add "errmsg", "success"
    .Add "list", rstJSON.Collection()
End With


'获取access_token
Dim accessToken
accessToken = GetAccessToken("","",appkey,appsecret)
LogMessage "tasks accessToken -> " & accessToken

''''''''''''''''''''''''''''''''''''''''
'''''''请将以下方法一同拷贝使用'''''''''''
''''''''''''''''''''''''''''''''''''''''
''''''''''''''''开始''''''''''''''''''''
''''''''''''''''''''''''''''''''''''''''
'发送http/https请求
Function PostHttpPageWithHeader(method, httpUrl, headerToken, data)
    If IsNull(httpUrl) = True Or Len(httpUrl)<18 Or httpUrl = "$False$" Then
        PostHttpPageWithHeader = "$False$"
        Exit Function
    End If
    Dim Http
    Set Http = server.CreateObject("MSXML2.ServerXMLHTTP")
    Http.setOption(2) = 13056
    'Set Http = server.CreateObject("MSXML2.XMLHTTP")
    Http.Open method, httpUrl, False
    'Http.setRequestHeader "CONTENT-TYPE", "application/x-www-form-urlencoded"
    'Http.setRequestHeader "CONTENT-TYPE", " application/json"
    Http.setRequestHeader "CONTENT-TYPE", " application/json;charset=utf-8"
    Http.setRequestHeader "x-acs-dingtalk-access-token", headerToken
    Http.Send data
    If Http.Readystate<>4 Then
        Set Http = Nothing
        PostHttpPageWithHeader = "$False$"
        Exit Function
    End If
    'PostHttpPage = bytesToBSTR(Http.responseBody, "g b2312")
    PostHttpPageWithHeader = bytesToBSTR(Http.responseBody, "utf-8")
    Set Http = Nothing
    If Err.Number<>0 Then
        Err.Clear
    End If
End Function


'Method: POST或者GET
'HttpUrl: URL
'data: 请求参数
Function PostHttpPageJSONWithHeader(method, httpUrl, accessToken, data)
    LogMessage "PostHttpPageJSONWithHeader"
    dim rststr
    rststr=PostHttpPageWithHeader(Method,HttpUrl,accessToken,data)
    LogMessage "Method -> " & Method & " / HttpUrl -> " & HttpUrl
    'LogMessage "Response2 -> " & rststr
    'response.write rststr
    'response.end
    Set PostHttpPageJSONWithHeader = New aspJSON
    PostHttpPageJSONWithHeader.loadJSON(rststr)
End Function



'创建待办任务
'params              请求参数
'accessToken        accessToken
Function CreateTasks(params, todoUserUnionId, accessToken)
    dim url
    url = "https://api.dingtalk.com/v1.0/todo/users/" & todoUserUnionId & "/tasks"
    LogMessage "createTasks url -> " & url
    LogMessage "createTasks params -> " & params

    Set CreateTasks = PostHttpPageJSONWithHeader("POST", url, accessToken, params)
    LogMessage "createTasks" & CreateTasks.JSONOutput()
End Function



'根据用户钉钉ID获取用户unionid
'userId         用户id
'accessToken        accessToken
Function getUserUnionId(userId, accessToken)
    Set userInfoResult = GetUserInfo(userId, accessToken)

    dim errCode,errMsg,unionId
    errCode = userInfoResult.data("errcode")
    errMsg = userInfoResult.data("errmsg")
    unionId = userInfoResult.data("unionid")
    if errCode = 0 then
        LogMessage "unionId -> " & unionId
        getUserUnionId = unionId
    else
        LogMessage "[ERROR] getUnionId -> " & errCode & ":" & errMsg
    end if
End Function


'执行创建待办任务操作
'todoUserDingId             任务代办人钉钉ID
'appointUserDingId          任务指派人钉钉ID
'subject                    任务标题
'description                任务描述
'expireTime                 过期时间
'accessToken                accessToken
Function doCreateTask(todoUserDingId, appointUserDingId, subject, description, expireTime, accessToken)
    '代办人钉钉union_id、交办人钉钉union_id
    Dim todoUserUnionId, appointUserUnionId, dueTime
    todoUserUnionId = getUserUnionId(todoUserDingId, accessToken)
    appointUserUnionId = getUserUnionId(appointUserDingId, accessToken)
    dueTime = int(ToUnixTime(CDate(expireTime),+8))*1000

    If todoUserUnionId = "" or appointUserUnionId = "" Then
        response.write "用户unionID获取异常，无法继续创建待办任务"
        response.end
    End If


    Set params = New aspJSON
    params.data.Add "subject", subject
    params.data.Add "description", description
    params.data.Add "creatorId", appointUserUnionId
    params.data.Add "dueTime", dueTime
    params.data.Add "isOnlyShowExecutor", true
    params.data.Add "executorIds", params.Collection()
    with params.data("executorIds")
        .Add 0, todoUserUnionId
    end with

    Dim errCode,errMsg,taskId
    Set createTasksResult = CreateTasks(params.JSONoutput(), todoUserUnionId ,accessToken)
    taskId = createTasksResult.data("id")
    If taskId = "" Then
        errCode = createTasksResult.data("code")
        errMsg = createTasksResult.data("message")
        Response.Write "待办任务创建失败，" & errCode & ":" & errMsg
        Response.End
    Else
        Response.Write "待办任务创建成功，taskId:" & taskId
    End If
End Function

function name_to_dingdingID(userName)
    if userName <> "" then
        '通过名称将数据库中的用户钉钉id
        sql="select dingding_id from user2011 where mingzhi='"&userName&"'"
        rs2.open sql,conn2,1,2
        if not rs2.eof then
        userid_oa=rs2(0)
        end if
        rs2.close
        name_to_dingdingID = userid_oa
    else 
        name_to_dingdingID=""
    end if 
end function

''''''''''''''''''''''''''''''''''''''''
'''''''请将以上方法一同拷贝使用'''''''''''
''''''''''''''''''''''''''''''''''''''''
''''''''''''''''结束''''''''''''''''''''
''''''''''''''''''''''''''''''''''''''''

'使用示例
'https://oa.work.secdriver.com/xinan/ding/dingding_rizhi/todo/todolist_demo.asp?todo=范亚辉&appoint=范亚辉&subject=测试待办标题11&description=测试待办test description&time=2024-5-19 09:35:53

'任务主体信息：代办人钉钉ID,指派人钉钉ID,任务标题，任务描述，任务截止日期
dim todoUserDingId, appointUserDingId, subject, description, expireTime
'todoUserDingId = "16181925804593972"
'appointUserDingId = "16181925804593972"
'subject = "测试待办标题" & now()
'description = "测试待办test description"
'expireTime = "2024-5-19 09:35:53"


'代办人钉钉ID'
todoUserDingId = name_to_dingdingID(request.querystring("todo"))
'指派人钉钉ID'
appointUserDingId = name_to_dingdingID(request.querystring("appoint"))
'任务标题'
subject = request.querystring("subject")
'任务描述'
description = request.querystring("description")
'任务截止日期'
expireTime = request.querystring("time")

'直接调用，该方法不加括号调用
doCreateTask todoUserDingId, appointUserDingId, subject, description, expireTime, accessToken
%>