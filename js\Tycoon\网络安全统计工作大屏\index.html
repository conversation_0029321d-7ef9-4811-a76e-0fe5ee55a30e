﻿<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>网络安全工作统计</title>
    <script type="text/javascript" src="js/jquery.js"></script>
    <script type="text/javascript" src="js/echarts.min.js"></script>
    <script type="text/javascript" src="js/js.js"></script>
    <script type="text/javascript" src="js/jquery.liMarquee.js"></script>
    <script type="text/javascript" src="js/jquery.cxselect.min.js"></script>

    <link rel="stylesheet" href="css/comon0.css">
</head>
<body>
    <div style="background:#000d4a url(images/bg.jpg) center top;">
        <div class="loading">
            <div class="loadbox"> <img src="images/loading.gif"> 页面加载中... </div>
        </div>
        <div class="back"></div>
        <div class="head">
            <div class="weather"><span id="showTime"></span></div>
            <h1>焦作网信办2022年度网络安全工作看板</h1>

        </div>
        <script>
            var t = null;
            t = setTimeout(time, 1000);/*開始运行*/
            function time() {
                clearTimeout(t);/*清除定时器*/
                dt = new Date();
                var y = dt.getFullYear();
                var mt = dt.getMonth() + 1;
                var day = dt.getDate();
                var h = dt.getHours();
                var m = dt.getMinutes();
                var s = dt.getSeconds();
                document.getElementById("showTime").innerHTML = y + "年" + mt + "月" + day + "日" + h + "时" + m + "分" + s + "秒";
                t = setTimeout(time, 1000); 
            }

        </script>
        <div class="mainbox">
            <ul class="clearfix">
                <li>
                    <div class="boxall" style="height:400px;">
                        <div class="alltitle">当前安全检查工作</div>
                        <div class="navboxall" id="echart5"></div>
                    </div>
                    <div class="boxall" style="height:260px;">
                        <div class="alltitle">各单位得分排名</div>
                        <div class="navboxall">
                            <div class="wraptit">
                                <span>单位</span><span>得分</span><span>排名</span>
                            </div>
                            <div class="wrap">
                                <ul>
                                    <li><p><span>焦作市移动公司</span><span>100</span><span>1</span></p></li>
                                    <li><p><span>中原内配公司</span><span>100</span><span>1</span></p></li>
                                    <li><p><span>焦作市儿童医院</span><span>99</span><span>2</span></p></li>
                                    <li><p><span>单位42</span><span>99</span><span>2</span></p></li>
                                    <li><p><span>单位15</span><span>99</span><span>2</span></p></li>
                                    <li><p><span>单位46</span><span>98</span><span>3</span></p></li>
                                    <li><p><span>单位17</span><span>95</span><span>4</span></p></li>
                                    <li><p><span>单位81</span><span>90</span><span>5</span></p></li>
                                    <li><p><span>单位19</span><span>89</span><span>6</span></p></li>

                                </ul>
                            </div>

                        </div>
                    </div>
                    <div class="boxall" style="height:260px;">
                        <div class="alltitle">本次检查常见问题分类</div>

                        <div class="navboxall" id="echart1"></div>
                    </div>
                </li>
                <li>

                    <div class="boxall" style="height:230px">
                        <div class="clearfix navboxall" style="height: 100%">
                            <div class="pulll_left num">
                                <div class="numbt">单位<span>(单位：家)</span></div>
                                <div class="numtxt">193</div>
                            </div>
                            <div class="pulll_right zhibiao">
                                <div class="zb1"><span>政府</span><div id="zb1"></div></div>
                                <div class="zb2"><span>医疗</span><div id="zb2"></div></div>
                                <div class="zb3"><span>企业</span><div id="zb3"></div></div>
                            </div>
                        </div>
                    </div>
                    <div class="boxall" style="height:350px">
                        <div class="alltitle">月度通报次数</div>
                        <div class="navboxall" id="echart4"></div>

                    </div>
                    <div class="boxall" style="height:340px">
                        <div class="alltitle">应急演练和安全培训</div>
                        <div class="navboxall" id="echart3"></div>
                    </div>
                </li>
                <li>
                    <div class="boxall" style="height:300px">
                        <div class="alltitle">日常性工作累积</div>
                        <div class="navboxall">
                            <table class="table1" width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <th scope="col"></th>
                                        <th scope="col">工作</th>
                                        <th scope="col">数量(条）</th>
                                       
                                    </tr>
                                    <tr>
                                        <td><span>1</span></td>
                                        <td>法律法规与制度要求</td>
                                        <td>18<br></td>
                                    </tr>
                                    <tr>
                                        <td><span>2</span></td>
                                        <td>网络安全热点工作</td>
                                        <td>92</td>
                                    </tr>

                                    <tr>
                                        <td><span>3</span></td>
                                        <td>网络安全通报工作</td>
                                        <td>19</td>
                                    </tr>
                                    <tr>
                                        <td><span>4</span></td>
                                        <td>网络安全培训工作</td>
                                        <td>13</td>
                                    </tr>
                                    <tr>
                                        <td><span>5</span></td>
                                        <td>网络安全应急演练</td>
                                        <td>6</td>
                                    </tr>
                                    <tr>
                                        <td><span>6</span></td>
                                        <td>网络安全重保工作</td>
                                        <td>2</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="boxall" style="height: 300px">
                        <div class="alltitle">网络安全问题突出单位</div>
                        <div class="navboxall">
                            <table class="table1" width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <th scope="col">排名</th>
                                        <th scope="col">单位</th>
                                        <th scope="col">检查数量</th>
                                        <th scope="col">年度得分</th>
                                    </tr>
                                    <tr>
                                        <td><span>1</span></td>
                                        <td>焦作单位1</td>
                                        <td>5<br></td>
                                        <td>58<br></td>
                                    </tr>
                                    <tr>
                                        <td><span>2</span></td>
                                        <td>焦作单位2</td>
                                        <td>5<br></td>
                                        <td>59<br></td>
                                    </tr>
                                    <tr>
                                        <td><span>3</span></td>
                                        <td>焦作单位3</td>
                                        <td>5<br></td>
                                        <td>60<br></td>
                                    </tr>
                                    <tr>
                                        <td><span>4</span></td>
                                        <td>焦作单位4</td>
                                        <td>5<br></td>
                                        <td>61<br></td>
                                    </tr>
                                    <tr>
                                        <td><span>5</span></td>
                                        <td>焦作单位5</td>
                                        <td>5<br></td>
                                        <td>62<br></td>
                                    </tr>
                                   
                                    
                                </tbody>
                            </table>
                        </div>

                    </div>

                    <div class="boxall" style="height:320px">
                        <div class="alltitle">网络安全突出问题</div>
                        <div class="navboxall" id="echart2"></div>
                    </div>


                </li>
            </ul>

        </div>
    </div>

    <script>
        $(function () {
            $('.wrap,.adduser').liMarquee({
                direction: 'up',/*身上滚动*/
                runshort: false,/*内容不足时不滚动*/
                scrollamount: 20/*速度*/
            });
        });

    </script>


</body>
</html>
