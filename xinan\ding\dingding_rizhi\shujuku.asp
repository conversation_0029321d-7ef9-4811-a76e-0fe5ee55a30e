<%
Dim conn, rs, connerp, rserp, rsp, connstr
Dim sql_localname, sql_username, sql_password, sql_databasename, sql_databasename2, sql_databasename3, sql_databasename4, dbweizhi2
sql_localname = "(local)" ' 基本数据库

' 基本数据库连接
connstr = "Provider = Sqloledb; User ID = " & sql_username & "; Password = " & sql_password & "; Initial Catalog = " & sql_databasename & "; Data Source = " & sql_localname & ";"
Set conn = Server.CreateObject("ADODB.Connection")
conn.Open connstr

Set rs = Server.CreateObject("ADODB.Recordset")
Set rs.ActiveConnection = conn

Set rs_2 = Server.CreateObject("ADODB.Recordset")
Set rs_2.ActiveConnection = conn

Set rs_3 = Server.CreateObject("ADODB.Recordset")
Set rs_3.ActiveConnection = conn

Set rs_4 = Server.CreateObject("ADODB.Recordset")
Set rs_4.ActiveConnection = conn

Set rsp = Server.CreateObject("ADODB.Recordset")
Set rsp.ActiveConnection = conn

' 第二个数据库连接 (ERP数据库)
Set objConn2 = Server.CreateObject("ADODB.Connection")
objConn2.Open connstr

Set erprs2 = Server.CreateObject("ADODB.Recordset")
Set erprs2.ActiveConnection = objConn2

' ================= ERP数据库 ========================
Set objConn = Server.CreateObject("ADODB.Connection")
connstr = "Provider = Sqloledb; User ID = " & sql_username & "; Password = " & sql_password & "; Initial Catalog = " & sql_databasename2 & "; Data Source = " & sql_localname & ";"
objConn.Open connstr

Set erprs = Server.CreateObject("ADODB.Recordset")
Set erprs.ActiveConnection = objConn

Set connerp = Server.CreateObject("ADODB.Connection")
connerp.Open connstr

Set rserp = Server.CreateObject("ADODB.Recordset")
Set rserp.ActiveConnection = connerp

' ================= 信安数据库 ========================
Set connxa = Server.CreateObject("ADODB.Connection")
connstr = "Provider = Sqloledb; User ID = " & sql_username & "; Password = " & sql_password & "; Initial Catalog = " & sql_databasename3 & "; Data Source = " & sql_localname & ";"
connxa.Open connstr

Set rsxa = Server.CreateObject("ADODB.Recordset")
Set rsxa.ActiveConnection = connxa

' ================= 日志数据库 ========================
Set connrz = Server.CreateObject("ADODB.Connection")
connstr = "Provider = Sqloledb; User ID = " & sql_username & "; Password = " & sql_password & "; Initial Catalog = " & sql_databasename4 & "; Data Source = " & sql_localname & ";"
connrz.Open connstr

Set rsrz = Server.CreateObject("ADODB.Recordset")
Set rsrz.ActiveConnection = connrz

' ================= 产品数据库连接 ========================
Set connCP = Server.CreateObject("ADODB.Connection")
connCPSTR = "Provider=microsoft.jet.oledb.4.0;Data Source=" & dbweizhi2 & "\iwms.cs"
connCP.Open connCPSTR

Set rscp = Server.CreateObject("ADODB.Recordset")
Set rscp.ActiveConnection = connCP
%>
