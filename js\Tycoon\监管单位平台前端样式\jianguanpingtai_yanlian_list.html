﻿<!doctype html>
<html lang="en">
	<head>
		<!-- Required meta tags -->
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

		<!-- Meta -->
		<meta name="description" content="Responsive Bootstrap4 Dashboard Template">
		<meta name="author" content="ParkerThemes">
		<link rel="shortcut icon" href="img/fav.png" />

		<!-- Title -->
		<title>网络安全应急演练工作</title>


		<!-- *************
			************ Common Css Files *************
		************ -->
		<!-- Bootstrap css -->
		<link rel="stylesheet" href="css/bootstrap.min.css">
		<!-- Icomoon Font Icons css -->
		<link rel="stylesheet" href="fonts/style.css">
		<!-- Main css -->
		<link rel="stylesheet" href="css/main.css">
		<!-- Chat css -->
		<link rel="stylesheet" href="css/chat.css">
			<!-- Data Tables -->
			<link rel="stylesheet" href="vendor/datatables/dataTables.bs4.css" />
			<link rel="stylesheet" href="vendor/datatables/dataTables.bs4-custom.css" />
			<link href="vendor/datatables/buttons.bs.css" rel="stylesheet" />

		<!-- *************
			************ Vendor Css Files *************
		************ -->
		
	</head>

	<body>

		<!-- Loading starts -->
		<div id="loading-wrapper">
			<div class="spinner-border" role="status">
				<span class="sr-only">Loading...</span>
			</div>
		</div>
		<!-- Loading ends -->
		
	
			
			<!-- Page content start  -->
			<div class="page-content">				
				
				
				<!-- Main container start -->
				<div class="main-container">
					<!-- Page header start -->
					<div class="page-header">
											
						<!-- Breadcrumb start -->
						
						<!-- Breadcrumb end -->
						<div class="app-actions">
							<div class="custom-dropdown-group">
								<div class="dropdown show">
									<button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
										年份筛选
									</button>
									<div class="dropdown-menu dropdown-menu-left" >
										<a class="dropdown-item" href="jianguanpingtai_nian_mingxi_list.html">2022年</a>
										<a class="dropdown-item" href="jianguanpingtai_nian_mingxi_list.html">2021年</a>
										<a class="dropdown-item" href="jianguanpingtai_nian_mingxi_list.html">2020年</a>
										<a class="dropdown-item" href="jianguanpingtai_nian_mingxi_list.html">2019年</a>
										<a class="dropdown-item" href="jianguanpingtai_nian_mingxi_list.html">2028年</a>
									</div>
								</div>
								
							</div>
						</div>

					</div>
					<!-- Page header end -->
					
				
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-star2"></i>
								</div>
								<div class="sale-num">
									<h3>4次</h3>
									<p>今年计划应急演练次数</p>
								</div>
							</div>
						</div>
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-star2"></i>
								</div>
								<div class="sale-num">
									<h3>5次</h3>
									<p>今年组织应急演练次数</p>
								</div>
							</div>
						</div>
						
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-star2"></i>
								</div>
								<div class="sale-num">
									<h3>1家</h3>
									<p>自行组织演练单位</p>
								</div>
							</div>
						</div>
						<div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12">
							<div class="info-stats2">
								<div class="info-icon info">
									<i class="icon-star2"></i>
								</div>
								<div class="sale-num">
									<h3>30家</h3>
									<p>未组织过演练单位</p>
								</div>
							</div>
						</div>
						

					</div>
					<!-- Row end -->
					<!-- Row start -->
					<div class="row gutters">
						<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
							<div class="table-container">
								<div class="t-header">
									<H2>网络安全应急演练工作
									<button class="btn btn-primary btn-rounded"><i class="icon-plus1"></i>新增</button>
								</H2>
									//添加页面未画！ 字段可以参考该表格显示内容即可。
								</div>
								<div class="table-responsive">
									<table id="basicExample" class="table custom-table">
										<thead>
											<tr>
												<th>演练起止日期</th>
												<th>演练主题</th>
												<th>涉及单位数量和名单</th>
												<th>演练方式（模拟演练和实战演练）</th>
												<th>演练预期目标</th>
												<th>演练效果</th>
												<th>支撑单位</th>
												<th>提交人</th>
												<th>操作</th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td>2016年11月7日-12月31日</td>
												<td><h5><a target="_blank" href="#" class="text-info">关于勒索病毒攻击方法的应急演练</a></h5> </td>
												<td>涉及单位10家，分别是：。。。。。。
												</td>
												
												<td>模拟演练</td>
												<td>使得各单位熟练掌握勒索病毒的爆发前的预防方法</td>
												<td>达到预期效果</td>
												<td>河南信安世纪科技有限公司</td>

												<td>李丽</td>
												
												<td>修改|删除</td>
											</tr>
											
											
											
										</tbody>
									</table>

									
								</div>
							</div>
						</div>
						
					</div>
					<!-- Row end -->
					
					
					
					
					
					
					
					

				</div>
				<!-- Main container end -->

			
				

				
			</div>
			<!-- Page content end -->

	
			

		<!--**************************
			**************************
				**************************
							Required JavaScript Files
				**************************
			**************************
		**************************-->
		<!-- Required jQuery first, then Bootstrap Bundle JS -->
		<script src="js/jquery.min.js"></script>
		<script src="js/bootstrap.bundle.min.js"></script>
		<script src="js/moment.js"></script>
		<!-- Data Tables -->
		<script src="vendor/datatables/dataTables.min.js"></script>
		<script src="vendor/datatables/dataTables.bootstrap.min.js"></script>

		<!-- Custom Data tables -->
		<script src="vendor/datatables/custom/custom-datatables.js"></script>
		<script src="vendor/datatables/custom/fixedHeader.js"></script>

		<!-- Download / CSV / Copy / Print -->
		<script src="vendor/datatables/buttons.min.js"></script>
		<script src="vendor/datatables/jszip.min.js"></script>
		<script src="vendor/datatables/vfs_fonts.js"></script>
		<script src="vendor/datatables/html5.min.js"></script>
		<script src="vendor/datatables/buttons.print.min.js"></script>

		<!-- *************
			************ Vendor Js Files *************
		************* -->
		<!-- Slimscroll JS -->
		<script src="vendor/slimscroll/slimscroll.min.js"></script>
		<script src="vendor/slimscroll/custom-scrollbar.js"></script>

		<!-- Polyfill JS -->
		<script src="vendor/polyfill/polyfill.min.js"></script>
		<script src="vendor/polyfill/class-list.min.js"></script>

		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		<script src="vendor/apex/custom/home/<USER>"></script>
		
		<!-- Peity Charts -->
		<script src="vendor/peity/peity.min.js"></script>
		<script src="vendor/peity/custom-peity.js"></script>
		
		<!-- Circleful Charts -->
		<script src="vendor/circliful/circliful.min.js"></script>
		<script src="vendor/circliful/circliful.custom.js"></script>
		<!-- Apex Charts -->
		<script src="vendor/apex/apexcharts.min.js"></script>

		

		<!-- Bar Graphs -->
		<script src="vendor/apex/examples/bar/basic-bar-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-graph-grouped.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-stack-graph-full-width.js"></script>
		<script src="vendor/apex/examples/bar/basic-bar-negative-values.js"></script>

		<!-- Candlestick Graphs -->
		<script src="vendor/apex/examples/candlestick/basic-candlestick-graph.js"></script>

		<!-- Column Graphs -->
		<script src="vendor/apex/examples/column/basic-column-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-graph-datalables.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph.js"></script>
		<script src="vendor/apex/examples/column/basic-column-stack-graph-fullheight.js"></script>

		<!-- Line Graphs -->
		<script src="vendor/apex/examples/line/basic-line-graph.js"></script>
		<script src="vendor/apex/examples/line/line-with-data-labels.js"></script>
		<script src="vendor/apex/examples/line/stepline.js"></script>

		<!-- Donut Graphs -->
		<script src="vendor/apex/examples/pie/basic-donut-graph.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome-gradient.js"></script>
		<script src="vendor/apex/examples/pie/basic-donut-graph-monochrome.js"></script>
		
		<!-- Main JS -->
		<script src="js/main.js"></script>

	</body>
</html>