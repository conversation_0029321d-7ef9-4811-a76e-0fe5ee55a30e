<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CISP相关功能API文档</title>
    <style>
        body {
            font-family: Arial, '微软雅黑', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1,
        h2,
        h3 {
            color: #2c3e50;
            margin-top: 30px;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        th {
            background-color: #f5f5f5;
        }

        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        code {
            font-family: Consolas, monospace;
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .note {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }

        .endpoint {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .method {
            display: inline-block;
            padding: 5px 10px;
            background: #28a745;
            color: white;
            border-radius: 3px;
            font-weight: bold;
            margin-right: 10px;
        }

        .api-section {
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 30px 0;
            background: #fff;
        }
    </style>
</head>

<body>
    <h1>CISP相关数据功能API文档</h1>

    <div>测试环境地址：http://193.168.1.25:12380/xinan/ding/dingding_rizhi/cisp</div>
    <div>生产环境地址：https://oa.work.secdriver.com/cisp</div>
    <div>密钥：1A833DA63A6B7E20098DAE06D06602E1</div>

    <h3>签名生成规则</h3>
    <ol>
        <li>将所有请求参数（不包括sign参数本身）按参数名的字典序排序</li>
        <li>将排序后的参数以key=value的形式用&符号拼接成字符串</li>
        <li>在拼接后的字符串末尾加上&key=您的密钥</li>
        <li>对最终拼接的字符串进行MD5加密，得到签名值</li>
    </ol>

    <h3>请求示例</h3>
    <pre><code>POST /cisp_personal_info.asp HTTP/1.1
        Host: localhost:12380
        Content-Length: 58
        
        action=debug&a=1&b=2&sign=00e9f0664097f6825272fca54f890935</code></pre>
    

    <h3>响应示例</h3>
    <pre><code>{
        "success": true,
        "message": "API请求成功"
    }</code></pre>
    

    <h2>接口列表</h2>
    <div class="api-section">
        <h2>1. 培训报名接口</h2>
        <p>该接口用于提交培训报名信息。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp_personal_info.asp</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: application/x-www-form-urlencoded</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>name</td>
                <td>String</td>
                <td>是</td>
                <td>姓名</td>
                <td>张三</td>
            </tr>
            <tr>
                <td>sex</td>
                <td>String</td>
                <td>是</td>
                <td>性别（男/女）</td>
                <td>男</td>
            </tr>
            <tr>
                <td>mobile</td>
                <td>String</td>
                <td>是</td>
                <td>手机号</td>
                <td>13800138000</td>
            </tr>
            <tr>
                <td>unit_name</td>
                <td>String</td>
                <td>是</td>
                <td>单位名称</td>
                <td>XX公司</td>
            </tr>
            <tr>
                <td>id_card</td>
                <td>String</td>
                <td>是</td>
                <td>证件号码</td>
                <td>110101199001011234</td>
            </tr>
            <tr>
                <td>training_type</td>
                <td>String</td>
                <td>是</td>
                <td>报考方向</td>
                <td>CISP</td>
            </tr>
        </table>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "培训报名成功"
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "必填项不能为空"
}</code></pre>

        <h3>错误码说明</h3>
        <table>
            <tr>
                <th>错误消息</th>
                <th>说明</th>
                <th>解决方案</th>
            </tr>
            <tr>
                <td>仅支持POST请求方式</td>
                <td>使用了非POST请求方式</td>
                <td>将请求方式改为POST</td>
            </tr>
            <tr>
                <td>必填项不能为空</td>
                <td>必填字段为空</td>
                <td>检查必填字段是否都已填写</td>
            </tr>
            <tr>
                <td>培训报名失败：[具体错误]</td>
                <td>数据库操作失败</td>
                <td>根据具体错误信息处理</td>
            </tr>
        </table>

    </div>

    <div class="api-section">
        <h2>2. 个人信息添加接口</h2>
        <p>该接口用于添加个人基本信息。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp_personal_info.asp</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: application/x-www-form-urlencoded</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>action</td>
                <td>String</td>
                <td>是</td>
                <td>操作类型，必须为 "add_personal_info"</td>
                <td>add_personal_info</td>
            </tr>
            <tr>
                <td>name</td>
                <td>String</td>
                <td>是</td>
                <td>姓名</td>
                <td>张三</td>
            </tr>
            <tr>
                <td>name_pinyin</td>
                <td>String</td>
                <td>是</td>
                <td>姓名拼音</td>
                <td>zhangsan</td>
            </tr>
            <tr>
                <td>gender</td>
                <td>String</td>
                <td>是</td>
                <td>性别</td>
                <td>1-男 0-女</td>
            </tr>
            <tr>
                <td>ethnicity</td>
                <td>String</td>
                <td>是</td>
                <td>民族</td>
                <td>汉族</td>
            </tr>
            <tr>
                <td>birth_date</td>
                <td>Date</td>
                <td>是</td>
                <td>出生日期</td>
                <td>1990-01-01</td>
            </tr>
            <tr>
                <td>hometown</td>
                <td>String</td>
                <td>是</td>
                <td>籍贯</td>
                <td>北京市</td>
            </tr>
            <tr>
                <td>political_status</td>
                <td>String</td>
                <td>是</td>
                <td>政治面貌</td>
                <td>群众</td>
            </tr>
            <tr>
                <td>major</td>
                <td>String</td>
                <td>是</td>
                <td>专业</td>
                <td>计算机科学与技术</td>
            </tr>
            <tr>
                <td>education</td>
                <td>String</td>
                <td>是</td>
                <td>最高学历</td>
                <td>本科</td>
            </tr>
            <tr>
                <td>id_card</td>
                <td>String</td>
                <td>是</td>
                <td>身份证号</td>
                <td>110101199001011234</td>
            </tr>
            <tr>
                <td>work_unit</td>
                <td>String</td>
                <td>是</td>
                <td>工作单位</td>
                <td>XX科技有限公司</td>
            </tr>
            <tr>
                <td>address</td>
                <td>String</td>
                <td>是</td>
                <td>通信地址</td>
                <td>北京市海淀区XX路XX号</td>
            </tr>
            <tr>
                <td>postal_code</td>
                <td>String</td>
                <td>是</td>
                <td>邮编</td>
                <td>100000</td>
            </tr>
            <tr>
                <td>phone</td>
                <td>String</td>
                <td>是</td>
                <td>联系电话</td>
                <td>010-12345678</td>
            </tr>
            <tr>
                <td>mobile</td>
                <td>String</td>
                <td>是</td>
                <td>手机号码</td>
                <td>13800138000</td>
            </tr>
            <tr>
                <td>email</td>
                <td>String</td>
                <td>是</td>
                <td>电子邮箱</td>
                <td><EMAIL></td>
            </tr>
        </table>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息，成功后会返回用户ID，使用|分割，供其他接口调用</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "个人信息添加成功|5"
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "必填项不能为空"
}</code></pre>

        <h3>错误码说明</h3>
        <table>
            <tr>
                <th>错误消息</th>
                <th>说明</th>
                <th>解决方案</th>
            </tr>
            <tr>
                <td>仅支持POST请求方式</td>
                <td>使用了非POST请求方式</td>
                <td>将请求方式改为POST</td>
            </tr>
            <tr>
                <td>必填项不能为空</td>
                <td>必填字段为空</td>
                <td>检查必填字段是否都已填写</td>
            </tr>
            <tr>
                <td>个人信息添加失败：[具体错误]</td>
                <td>数据库操作失败</td>
                <td>根据具体错误信息处理</td>
            </tr>
        </table>


    </div>

    <div class="api-section">
        <h2>3. 文化程度添加接口</h2>
        <p>该接口用于添加个人文化程度信息，支持单条或多条记录添加。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp_personal_info.asp</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: application/x-www-form-urlencoded</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <h4>基础参数</h4>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>action</td>
                <td>String</td>
                <td>是</td>
                <td>操作类型，必须为 "add_education"</td>
                <td>add_education</td>
            </tr>
            <tr>
                <td>person_id</td>
                <td>Integer</td>
                <td>是</td>
                <td>个人信息ID，关联个人信息表的ID</td>
                <td>1</td>
            </tr>
            <tr>
                <td>record_count</td>
                <td>Integer</td>
                <td>是</td>
                <td>要添加的文化程度记录数量，不提供时默认为1</td>
                <td>3</td>
            </tr>
        </table>

        <h4>单条记录参数（当record_count不提供或为1时）</h4>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>education_time</td>
                <td>String</td>
                <td>是</td>
                <td>教育经历时间段</td>
                <td>2010-2014</td>
            </tr>
            <tr>
                <td>school</td>
                <td>String</td>
                <td>是</td>
                <td>毕业学校</td>
                <td>北京大学</td>
            </tr>
            <tr>
                <td>education_level</td>
                <td>String</td>
                <td>是</td>
                <td>学历</td>
                <td>本科</td>
            </tr>
            <tr>
                <td>education_major</td>
                <td>String</td>
                <td>是</td>
                <td>专业</td>
                <td>计算机科学与技术</td>
            </tr>
        </table>

        <h4>多条记录参数（当record_count大于1时）</h4>
        <p>多条记录时，每条记录的参数名需要添加下标后缀，如 education_time_1, education_time_2 等</p>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>education_time_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的教育经历时间段</td>
                <td>2010-2014</td>
            </tr>
            <tr>
                <td>school_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的毕业学校</td>
                <td>北京大学</td>
            </tr>
            <tr>
                <td>education_level_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的学历</td>
                <td>本科</td>
            </tr>
            <tr>
                <td>education_major_{i}</td>
                <td>String</td>
                <td>是</td>
                <td>第i条记录的专业</td>
                <td>计算机科学与技术</td>
            </tr>
        </table>
        <p>* 注：多条记录模式下，如果某条记录的必填参数不完整，该记录将被跳过，不会导致整个请求失败</p>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "成功添加3条文化程度记录"
}</code></pre>

        <h4>部分成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "成功添加2条文化程度记录，但有以下错误：记录3参数不完整，已跳过; "
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "个人信息ID不能为空"
}</code></pre>


    </div>

    <div class="api-section">
        <h2>4. 培训证书添加接口</h2>
        <p>该接口用于添加个人培训证书信息，支持单条或多条记录添加。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp_personal_info.asp</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: application/x-www-form-urlencoded</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <h4>基础参数</h4>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>action</td>
                <td>String</td>
                <td>是</td>
                <td>操作类型，必须为 "add_certificate"</td>
                <td>add_certificate</td>
            </tr>
            <tr>
                <td>person_id</td>
                <td>Integer</td>
                <td>是</td>
                <td>个人信息ID，关联个人信息表的ID</td>
                <td>1</td>
            </tr>
            <tr>
                <td>record_count</td>
                <td>Integer</td>
                <td>是</td>
                <td>要添加的培训证书记录数量，不提供时默认为1</td>
                <td>3</td>
            </tr>
        </table>

        <h4>单条记录参数（当record_count不提供或为1时）</h4>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>certificate_name</td>
                <td>String</td>
                <td>是</td>
                <td>证书名称</td>
                <td>安全生产管理证书</td>
            </tr>
            <tr>
                <td>issuing_organization</td>
                <td>String</td>
                <td>是</td>
                <td>发证机构</td>
                <td>国家安全生产监督管理总局</td>
            </tr>
            <tr>
                <td>certificate_number</td>
                <td>String</td>
                <td>是</td>
                <td>证书编号</td>
                <td>CERT2023001</td>
            </tr>
            <tr>
                <td>certificate_file</td>
                <td>String</td>
                <td>是</td>
                <td>证书文件路径</td>
                <td>/uploads/certificates/cert2023001.pdf</td>
            </tr>
            <tr>
                <td>issue_date</td>
                <td>String</td>
                <td>是</td>
                <td>获证日期</td>
                <td>2023-01-15</td>
            </tr>
        </table>

        <h4>多条记录参数（当record_count大于1时）</h4>
        <p>多条记录时，每条记录的参数名需要添加下标后缀，如 certificate_name_1, certificate_name_2 等</p>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>certificate_name_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的证书名称</td>
                <td>安全生产管理证书</td>
            </tr>
            <tr>
                <td>issuing_organization_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的发证机构</td>
                <td>国家安全生产监督管理总局</td>
            </tr>
            <tr>
                <td>certificate_number_{i}</td>
                <td>String</td>
                <td>是</td>
                <td>第i条记录的证书编号</td>
                <td>CERT2023001</td>
            </tr>
            <tr>
                <td>certificate_file_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的证书文件路径</td>
                <td>/uploads/certificates/cert2023001.pdf</td>
            </tr>
            <tr>
                <td>issue_date_{i}</td>
                <td>String</td>
                <td>是</td>
                <td>第i条记录的获证日期</td>
                <td>2023-01-15</td>
            </tr>
        </table>
        <p>* 注：多条记录模式下，如果某条记录的必填参数不完整，该记录将被跳过，不会导致整个请求失败</p>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "成功添加3条培训证书记录"
}</code></pre>

        <h4>部分成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "成功添加2条培训证书记录，但有以下错误：记录3参数不完整，已跳过; "
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "个人信息ID不能为空"
}</code></pre>


    </div>
    <div class="api-section">
        <h2>5. 个人资料文档更新接口</h2>
        <p>该接口用于更新个人资料文档信息。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp_personal_info.asp</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: application/x-www-form-urlencoded</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>action</td>
                <td>String</td>
                <td>是</td>
                <td>操作类型，必须为 "update_personal_docs"</td>
                <td>update_personal_docs</td>
            </tr>
            <tr>
                <td>person_id</td>
                <td>Integer</td>
                <td>是</td>
                <td>个人信息ID，关联个人信息表的ID</td>
                <td>1</td>
            </tr>
            <tr>
                <td>first_education</td>
                <td>String</td>
                <td>是</td>
                <td>第一学历证书路径</td>
                <td>/uploads/education/first_edu.pdf</td>
            </tr>
            <tr>
                <td>highest_education</td>
                <td>String</td>
                <td>是</td>
                <td>最高学历证书路径</td>
                <td>/uploads/education/highest_edu.pdf</td>
            </tr>
            <tr>
                <td>photo</td>
                <td>String</td>
                <td>是</td>
                <td>电子版照片路径</td>
                <td>/uploads/photos/photo.jpg</td>
            </tr>
            <tr>
                <td>recommendation_letter</td>
                <td>String</td>
                <td>是</td>
                <td>个人推荐书路径</td>
                <td>/uploads/docs/recommendation.pdf</td>
            </tr>
            <tr>
                <td>declaration_letter</td>
                <td>String</td>
                <td>是</td>
                <td>个人声明书路径</td>
                <td>/uploads/docs/declaration.pdf</td>
            </tr>
            <tr>
                <td>id_card_front</td>
                <td>String</td>
                <td>是</td>
                <td>身份证正面照片路径</td>
                <td>/uploads/idcard/front.jpg</td>
            </tr>
            <tr>
                <td>id_card_back</td>
                <td>String</td>
                <td>是</td>
                <td>身份证反面照片路径</td>
                <td>/uploads/idcard/back.jpg</td>
            </tr>
        </table>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "个人资料文档更新成功"
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "个人信息ID不能为空"
}</code></pre>

        <h3>错误码说明</h3>
        <table>
            <tr>
                <th>错误消息</th>
                <th>说明</th>
                <th>解决方案</th>
            </tr>
            <tr>
                <td>个人信息ID不能为空</td>
                <td>未提供person_id参数</td>
                <td>检查person_id参数是否已填写</td>
            </tr>
            <tr>
                <td>至少需要提供一个要更新的字段</td>
                <td>所有更新字段均为空</td>
                <td>至少提供一个有效的更新字段</td>
            </tr>
            <tr>
                <td>个人资料文档更新失败：[具体错误]</td>
                <td>数据库操作失败</td>
                <td>根据具体错误信息处理</td>
            </tr>
        </table>


    </div>
    <div class="api-section">
        <h2>6. 工作经历添加接口</h2>
        <p>该接口用于添加个人工作经历信息，支持单条或多条记录添加。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp_personal_info.asp</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: application/x-www-form-urlencoded</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <h4>基础参数</h4>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>action</td>
                <td>String</td>
                <td>是</td>
                <td>操作类型，必须为 "add_work_experience"</td>
                <td>add_work_experience</td>
            </tr>
            <tr>
                <td>person_id</td>
                <td>Integer</td>
                <td>是</td>
                <td>个人信息ID，关联个人信息表的ID</td>
                <td>1</td>
            </tr>
            <tr>
                <td>record_count</td>
                <td>Integer</td>
                <td>否</td>
                <td>要添加的工作经历记录数量，不提供时默认为1</td>
                <td>3</td>
            </tr>
        </table>

        <h4>单条记录参数（当record_count不提供或为1时）</h4>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>start_date</td>
                <td>String</td>
                <td>是</td>
                <td>起始日期</td>
                <td>2010-01-01</td>
            </tr>
            <tr>
                <td>end_date</td>
                <td>String</td>
                <td>是</td>
                <td>结束日期</td>
                <td>2015-12-31</td>
            </tr>
            <tr>
                <td>company</td>
                <td>String</td>
                <td>是</td>
                <td>工作单位</td>
                <td>XX科技有限公司</td>
            </tr>
            <tr>
                <td>position</td>
                <td>String</td>
                <td>是</td>
                <td>职务</td>
                <td>技术经理</td>
            </tr>
            <tr>
                <td>main_duties</td>
                <td>String</td>
                <td>否</td>
                <td>主要职责</td>
                <td>负责项目管理和技术架构设计</td>
            </tr>
            <tr>
                <td>certification</td>
                <td>String</td>
                <td>否</td>
                <td>证明材料</td>
                <td>工作证明.pdf</td>
            </tr>
            <tr>
                <td>is_security_related</td>
                <td>String</td>
                <td>否</td>
                <td>是否与信息安全相关(1是/0否)</td>
                <td>1</td>
            </tr>
        </table>

        <h4>多条记录参数（当record_count大于1时）</h4>
        <p>多条记录时，每条记录的参数名需要添加下标后缀，如 start_date_1, start_date_2 等</p>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>start_date_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的起始日期</td>
                <td>2010-01-01</td>
            </tr>
            <tr>
                <td>end_date_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的结束日期</td>
                <td>2015-12-31</td>
            </tr>
            <tr>
                <td>company_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的工作单位</td>
                <td>XX科技有限公司</td>
            </tr>
            <tr>
                <td>position_{i}</td>
                <td>String</td>
                <td>是*</td>
                <td>第i条记录的职务</td>
                <td>技术经理</td>
            </tr>
            <tr>
                <td>main_duties_{i}</td>
                <td>String</td>
                <td>否</td>
                <td>第i条记录的主要职责</td>
                <td>负责项目管理和技术架构设计</td>
            </tr>
            <tr>
                <td>certification_{i}</td>
                <td>String</td>
                <td>否</td>
                <td>第i条记录的证明材料</td>
                <td>工作证明.pdf</td>
            </tr>
            <tr>
                <td>is_security_related_{i}</td>
                <td>String</td>
                <td>否</td>
                <td>第i条记录是否与信息安全相关(1是/0否)</td>
                <td>1</td>
            </tr>
        </table>
        <p>* 注：多条记录模式下，如果某条记录的必填参数不完整，该记录将被跳过，不会导致整个请求失败</p>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "成功添加3条工作经历记录"
}</code></pre>

        <h4>部分成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "成功添加2条工作经历记录，但有以下错误：记录3参数不完整，已跳过; "
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "个人信息ID不能为空"
}</code></pre>




    <div class="api-section">
        <h2>7. 文件上传接口</h2>
        <p>该接口用于上传图片和PDF文件，支持 jpg、jpeg、png、gif、pdf 格式。文件将按照年/月/日/个人ID 的目录结构进行存储，并返回带完整访问前缀的文件URL。</p>

        <div class="endpoint">
            <span class="method">POST</span>
            <h3>接口信息</h3>
            <ul>
                <li><strong>接口URL</strong>: <code>/cisp/upload.asp?person_id={person_id}</code></li>
                <li><strong>请求方式</strong>: POST</li>
                <li><strong>数据格式</strong>: multipart/form-data</li>
                <li><strong>编码方式</strong>: UTF-8</li>
            </ul>
        </div>

        <h3>请求参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
                <th>示例值</th>
            </tr>
            <tr>
                <td>person_id</td>
                <td>String</td>
                <td>是</td>
                <td>个人ID（URL参数）</td>
                <td>12345</td>
            </tr>
            <tr>
                <td>file</td>
                <td>File</td>
                <td>是</td>
                <td>要上传的文件</td>
                <td>-</td>
            </tr>
        </table>

        <h3>文件限制</h3>
        <ul>
            <li>支持的文件类型：.jpg、.jpeg、.png、.gif、.pdf</li>
            <li>最大文件大小：10MB</li>
            <li>文件存储路径格式：/uploads/年/月/日/个人ID/文件名</li>
            <li>返回的<code>data</code>字段为带完整访问前缀的URL，如：<code>http://193.168.1.25:12380/xinan/ding/dingding_rizhi/cisp/uploads/2024/06/08/12345/xxx.jpg</code></li>
        </ul>

        <h3>响应参数</h3>
        <table>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>success</td>
                <td>Boolean</td>
                <td>请求是否成功</td>
            </tr>
            <tr>
                <td>message</td>
                <td>String</td>
                <td>响应消息</td>
            </tr>
            <tr>
                <td>data</td>
                <td>String</td>
                <td>上传成功后的文件完整访问URL</td>
            </tr>
        </table>

        <h3>响应示例</h3>
        <h4>成功响应</h4>
        <pre><code>{
    "success": true,
    "message": "上传成功",
    "data": "http://193.168.1.25:12380/xinan/ding/dingding_rizhi/cisp/uploads/2024/06/08/12345/202406081234567890.jpg"
}</code></pre>

        <h4>失败响应</h4>
        <pre><code>{
    "success": false,
    "message": "个人ID不能为空"
}</code></pre>

        <h3>错误码说明</h3>
        <table>
            <tr>
                <th>错误消息</th>
                <th>说明</th>
                <th>解决方案</th>
            </tr>
            <tr>
                <td>个人ID不能为空</td>
                <td>未提供person_id参数</td>
                <td>在URL中添加person_id参数</td>
            </tr>
            <tr>
                <td>没有选择文件</td>
                <td>未上传任何文件</td>
                <td>确保已选择要上传的文件</td>
            </tr>
            <tr>
                <td>文件大小超出限制(最大10MB)</td>
                <td>上传的文件超过大小限制</td>
                <td>压缩文件大小或选择更小的文件</td>
            </tr>
            <tr>
                <td>文件类型不允许</td>
                <td>上传了不支持的文件格式</td>
                <td>确保文件为jpg、jpeg、png、gif、pdf格式</td>
            </tr>
            <tr>
                <td>上传初始化失败: ...</td>
                <td>上传组件初始化失败</td>
                <td>检查服务器权限、磁盘空间等</td>
            </tr>
            <tr>
                <td>未知错误，f_Err=...，扩展名=...</td>
                <td>其它上传失败</td>
                <td>检查返回的f_Err和扩展名</td>
            </tr>
            <tr>
                <td>仅支持POST请求方式</td>
                <td>使用了错误的请求方法</td>
                <td>使用POST方法上传文件</td>
            </tr>
        </table>

    </div>

    <h2>注意事项</h2>
    <div class="note">
        <ol>
            <li>所有字符串类型的参数都会进行SQL注入防护处理</li>
            <li>日期格式请使用 "YYYY-MM-DD" 格式</li>
            <li>身份证号请确保符合18位身份证号码格式要求</li>
            <li>接口采用 UTF-8 编码，请确保提交的中文内容使用正确的编码方式</li>
            <li>添加文化程度信息或培训证书信息前，必须先添加个人信息并获取个人信息ID</li>
            <li>person_id 必须是已存在的个人信息ID，否则会因外键约束而添加失败</li>
            <li>选填字段如果不传值，在数据库中会被设置为 NULL</li>
            <li>添加多条文化程度记录时，参数名需要添加下标后缀，如 education_time_1, school_1 等</li>
            <li>添加多条培训证书记录时，参数名需要添加下标后缀，如 certificate_name_1, issuing_organization_1 等</li>
            <li>多条记录模式下，如果某条记录的必填参数不完整，该记录将被跳过，不会导致整个请求失败</li>
        </ol>
    </div>
</body>

</html>