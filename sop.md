 现阶段内部测试验收

 - 效率方面，梳理出来节省人员工作量方面有哪些具体的功能？
    1. 定时任务自动同步预上架设备信息
    2. 自动同步sop项目信息，包括售前项目以及售后项目
    3. 自动同步sop项目所绑定的客户单位数据
    4. 自动同步处理上架审批流
    5. 自动同步OA用户信息
    6. 自动同步主机资产和网站资产
    7. 自动同步流量监测告警数据
    8. 自动同步蜜罐失陷主机、攻击者画像、时间轴告警数据
    9. 自动同步阻断日志数据
    10. 自动同步网站风险和网站风险汇总数据
    11. 自动同步网站风险和网站风险汇总数据
    12. 自动同步主机agent、主机风险台账
    13. 自动同步系统资源、引擎状态
    14. 用户离职交接批量数据替换
    15. sop服务模板配置，大大简化配置的复杂度
    16. 预警配置的通用配置模板，简化相同配置的多次操作
    17. 设备授权日期自动更新，并进行提醒
    18. 设备测试到期提前消息通知


 - 流程方面，平台上流程标准化方面，哪些实现了，哪些还没有实现，梳理出来已经实现的功能有哪些？
   1. 实现从客户单位-》项目-》设备，层级关系数据管理，数据逻辑清晰展示
   2. 项目下的任务看板管理，从sop服务模板的一件导入数据开始，然后可以给其他用户配置任务，之后就在公司任务中以及任务处理状况中，监测任务的执行的状态，以及每个员工的任务状况
   3. 系统监测方面，通过自动拉取数据，然后根据预警配置阈值，来自动监测蜜罐以及流量监测的数据，超过阈值的数据，自动根据发送告警信息到钉钉

 - 质量方面，标准化输出日、周报，还有哪些质量方面的控制、约束，比如定时任务、绩效任务等，梳理出来清单。
    1. 从爬虫任务替换为新的api接口调用方式，能够提高数据的精准率，提高统计数据的效率
    2. 从安全运营平台拉取过来的数据，通过动态配置，实现数据预警提醒
    3. 通过发送的日报周报数据，做出日周报趋势图，通过折线趋势，来分析安全运营数据的变化曲线状况

